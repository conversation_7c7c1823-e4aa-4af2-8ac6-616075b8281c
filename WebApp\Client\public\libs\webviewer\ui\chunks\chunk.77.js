(window.webpackJsonp=window.webpackJsonp||[]).push([[77],{1676:function(e,a,t){var r=t(32),l=t(1805);"string"==typeof(l=l.__esModule?l.default:l)&&(l=[[e.i,l,""]]);var o={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let a;a=document.getElementsByTagName("apryse-webviewer"),a.length||(a=function e(a,t=document){const r=[];return t.querySelectorAll(a).forEach(e=>r.push(e)),t.querySelectorAll("*").forEach(t=>{t.shadowRoot&&r.push(...e(a,t.shadowRoot))}),r}("apryse-webviewer"));const t=[];for(let r=0;r<a.length;r++){const l=a[r];if(0===r)l.shadowRoot.appendChild(e),e.onload=function(){t.length>0&&t.forEach(a=>{a.innerHTML=e.innerHTML})};else{const a=e.cloneNode(!0);l.shadowRoot.appendChild(a),t.push(a)}}},singleton:!1};r(l,o);e.exports=l.locals||{}},1805:function(e,a,t){(a=e.exports=t(33)(!1)).push([e.i,".ScaleOverlay{position:absolute;z-index:95;border-radius:4px;box-shadow:0 0 3px 0 var(--document-box-shadow);background:var(--component-background)}.open.ScaleOverlay{visibility:visible}.closed.ScaleOverlay{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.MobilePopupWrapper .scale-overlay-calibrate .scale-overlay-footer .calibration-apply:enabled:hover,.ScaleOverlay .scale-overlay-footer .calibration-apply:enabled:hover{background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.MobilePopupWrapper .scale-overlay-calibrate .scale-overlay-footer .calibration-apply:disabled,.ScaleOverlay .scale-overlay-footer .calibration-apply:disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.MobilePopupWrapper .scale-overlay-calibrate .scale-overlay-footer .calibration-apply:disabled span,.ScaleOverlay .scale-overlay-footer .calibration-apply:disabled span{color:var(--primary-button-text)}.ScaleOverlay .scale-overlay-header .add-new-scale:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.MobilePopupWrapper{display:block;padding:12px 16px}.MobilePopupWrapper .scale-overlay-header{height:32px;display:flex;flex-direction:row;align-items:center;font-size:13px;justify-content:flex-start}.MobilePopupWrapper .scale-overlay-header .scale-overlay-icon{width:24px;height:24px;margin-right:12px}.MobilePopupWrapper .scale-overlay-header .scale-overlay-icon svg{width:24px;height:24px}.MobilePopupWrapper .scale-overlay-header .scale-overlay-title{color:var(--text-color);font-weight:700;font-size:13px;margin-right:16px}.MobilePopupWrapper .scale-overlay-header .add-new-scale{width:100%;padding:var(--padding-small) var(--padding);background-color:transparent;color:var(--secondary-button-text);font-weight:400;border:1px solid;margin-left:12px}.MobilePopupWrapper .scale-overlay-header .add-new-scale span{font-size:13px}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector{position:relative;font-size:13px}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .scale-overlay-selection{width:176px;height:32px;background-color:transparent;border:1px solid var(--border);border-radius:var(--border-radius);line-height:32px;padding:0 var(--padding-small);color:var(--text-color);font-weight:400;font-size:13px;text-align:left;text-overflow:ellipsis;white-space:nowrap;cursor:pointer}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .scale-overlay-selection:hover .scale-overlay-arrow,.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .scale-overlay-selection[focus-within] .scale-overlay-arrow{outline:1px solid var(--primary-button-hover);border-radius:var(--border-radius)}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .scale-overlay-selection:focus-within .scale-overlay-arrow,.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .scale-overlay-selection:hover .scale-overlay-arrow{outline:1px solid var(--primary-button-hover);border-radius:var(--border-radius)}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .scale-overlay-selection:hover .Icon,.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .scale-overlay-selection[focus-within] .Icon{color:var(--primary-button-hover)}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .scale-overlay-selection:focus-within .Icon,.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .scale-overlay-selection:hover .Icon{color:var(--primary-button-hover)}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .scale-overlay-arrow{display:flex;justify-content:center;width:20px;height:20px;color:var(--text-color);cursor:pointer}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .scale-overlay-arrow .Icon{width:20px;height:20px}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .scale-overlay-list{display:flex;grid-row-gap:4px;row-gap:4px;flex-direction:column;border-top:1px solid transparent;width:176px;font-size:13px}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector ul{margin:0;list-style-type:none;position:absolute;min-width:157px;left:0;top:0;text-align:left;letter-spacing:0;display:none;border-radius:var(--border-radius);pointer-events:all;z-index:1000;background-color:var(--component-background);box-shadow:0 0 4px var(--box-shadow);padding-left:0;padding-bottom:var(--padding-small)}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .scale-overlay-selected-arrow{border:0;padding:0;background-color:transparent;width:100%;height:100%}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .scale-overlay-selected-arrow .Icon{margin:auto}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .scale-overlay-item{display:flex;flex-direction:row;align-items:center;justify-content:space-between;height:32px;grid-column-gap:var(--padding-small);-moz-column-gap:var(--padding-small);column-gap:var(--padding-small);font-size:13px}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .scale-overlay-item .options{font-size:13px}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li{display:flex;flex-direction:row;align-items:center;justify-content:space-between;height:32px;line-height:32px;padding-left:var(--padding-small);padding-right:var(--padding-small);border:1px solid transparent;grid-column-gap:var(--padding-small);-moz-column-gap:var(--padding-small);column-gap:var(--padding-small);font-size:13px}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li .add-new-scale{width:100%;padding:var(--padding-small) var(--padding);background-color:transparent;color:var(--secondary-button-text);font-weight:400;border:1px solid;font-size:13px;margin:0}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:first-child:hover .scale-overlay-arrow{outline:1px solid var(--primary-button-hover);border-radius:var(--border-radius)}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:first-child:hover .Icon{color:var(--primary-button-hover)}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):hover,.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child)[focus-within]{background-color:var(--scale-overlay-item-hover)}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):focus-within,.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):hover{background-color:var(--scale-overlay-item-hover)}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):hover .options,.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child)[focus-within] .options{color:var(--dropdown-item-active-text);font-size:13px}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):focus-within .options,.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):hover .options{color:var(--dropdown-item-active-text);font-size:13px}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):hover .Icon,.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child)[focus-within] .Icon{color:var(--dropdown-item-active-icon)}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):focus-within .Icon,.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):hover .Icon{color:var(--dropdown-item-active-icon)}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):hover .delete:hover:not(:disabled) .Icon,.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):hover .delete[focus-within]:not(:disabled) .Icon,.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child)[focus-within] .delete:hover:not(:disabled) .Icon,.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child)[focus-within] .delete[focus-within]:not(:disabled) .Icon{color:var(--icon-color)}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):focus-within .delete:focus-within:not(:disabled) .Icon,.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):focus-within .delete:hover:not(:disabled) .Icon,.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):hover .delete:focus-within:not(:disabled) .Icon,.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):hover .delete:hover:not(:disabled) .Icon{color:var(--icon-color)}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li:last-child{height:32px;font-size:13px}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .option-selected{background-color:var(--dropdown-item-active);font-size:13px}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .option-selected .options{color:var(--dropdown-item-active-text);font-size:13px}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .option-selected .Icon{color:var(--dropdown-item-active-icon)}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li .delete{width:24px;height:24px;border:none;background-color:transparent;padding:0;border-radius:var(--border-radius);display:flex;align-items:center;justify-content:center}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li .delete .Icon{width:16px;height:16px;display:flex}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .delete:hover,.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .delete[focus-within]{background-color:var(--popup-button-hover)}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .delete:focus-within,.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .delete:hover{background-color:var(--popup-button-hover)}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .delete:disabled{background-color:transparent;cursor:not-allowed}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector li .options{border:none;background-color:transparent;padding:0;display:flex;align-items:center;justify-content:space-between;height:100%;font-size:13px}.MobilePopupWrapper .scale-overlay-header .scale-overlay-selector .scale-value{margin-right:1px}.MobilePopupWrapper .scale-overlay-calibrate{height:125px}.MobilePopupWrapper .scale-overlay-calibrate .scale-overlay-header{height:32px;margin-bottom:8px;flex-direction:row;align-items:center;justify-content:flex-start;font-size:13px;font-weight:700}.MobilePopupWrapper .scale-overlay-calibrate .scale-overlay-content{font-size:13px;margin-bottom:8px}.MobilePopupWrapper .scale-overlay-calibrate .divider{width:calc(100% + 2.375rem);height:1px;margin-left:-1rem;background:var(--gray-5);margin-top:12px;margin-bottom:16px}.MobilePopupWrapper .scale-overlay-calibrate .scale-overlay-footer{margin-top:var(--padding-medium);display:flex;flex-direction:row;justify-content:space-between;align-items:center}.MobilePopupWrapper .scale-overlay-calibrate .scale-overlay-footer .calibration-cancel{padding:0;background-color:transparent;border-radius:var(--border-radius);width:55px;height:32px;color:var(--secondary-button-text);cursor:pointer;border:none;font-size:13px}:host(:not([data-tabbing=true])) .MobilePopupWrapper .scale-overlay-calibrate .scale-overlay-footer .calibration-cancel,html:not([data-tabbing=true]) .MobilePopupWrapper .scale-overlay-calibrate .scale-overlay-footer .calibration-cancel{outline:none}.MobilePopupWrapper .scale-overlay-calibrate .scale-overlay-footer .calibration-cancel:enabled:hover{color:var(--secondary-button-hover)}.MobilePopupWrapper .scale-overlay-calibrate .scale-overlay-footer .calibration-cancel:disabled{opacity:.5}.MobilePopupWrapper .scale-overlay-calibrate .scale-overlay-footer .calibration-apply{padding:0;border:none;background-color:transparent;background:var(--primary-button);border-radius:var(--border-radius);height:32px;width:66px;color:var(--primary-button-text);cursor:pointer;font-size:13px}:host(:not([data-tabbing=true])) .MobilePopupWrapper .scale-overlay-calibrate .scale-overlay-footer .calibration-apply,html:not([data-tabbing=true]) .MobilePopupWrapper .scale-overlay-calibrate .scale-overlay-footer .calibration-apply{outline:none}.MobilePopupWrapper .MeasurementDetail{word-wrap:break-word;flex-wrap:wrap;font-weight:400;height:168px}.MobilePopupWrapper .MeasurementDetail .header{display:flex;justify-content:left;align-items:center;width:100%;margin-top:var(--padding-medium);font-weight:700;grid-gap:var(--padding-small);gap:var(--padding-small);font-size:13px}.MobilePopupWrapper .MeasurementDetail .header .icon{width:24px;height:24px}.MobilePopupWrapper .MeasurementDetail .scale-input{width:70px;height:24px;margin-right:var(--padding-tiny);padding:var(--padding-tiny);font-size:13px}.MobilePopupWrapper .MeasurementDetail .scale-input:disabled{opacity:.5}.MobilePopupWrapper .MeasurementDetail .measurement__detail-container{margin-left:var(--padding-huge)}.MobilePopupWrapper .MeasurementDetail .measurement__detail-container .measurement__detail-item{display:flex;height:24px;align-items:center;margin:var(--padding-tiny);font-size:13px}.MobilePopupWrapper .MeasurementDetail .distance_input{display:flex;margin-top:var(--padding-small)}.MobilePopupWrapper .MeasurementDetail .distance_input .distance-show{margin-right:var(--padding-small);width:79px;height:32px}.MobilePopupWrapper .MeasurementDetail .distance-show{text-decoration:underline;margin-right:var(--padding-small)}.MobilePopupWrapper .MeasurementDetail .angle_input{display:flex;margin-top:var(--padding-small);text-decoration:underline}.MobilePopupWrapper .MeasurementDetail .measurement__deltas--X,.MobilePopupWrapper .MeasurementDetail .measurement__deltas--Y{display:flex;margin-top:var(--padding-small)}.MobilePopupWrapper .MeasurementDetail .measurement_list{width:64px;font-size:13px}.MobilePopupWrapper .MeasurementDetail .measurement{padding:5px}.ScaleOverlay{flex-direction:column;padding:var(--padding-small) var(--padding-medium)}.ScaleOverlay button{cursor:pointer}.ScaleOverlay .divider{width:calc(100% + 1.375rem);height:1px;margin-left:-1rem;background:var(--gray-5);margin-top:12px}.ScaleOverlay[\\:has\\(.scale-overlay-calibrate\\)]{padding:var(--padding-medium) 0}.ScaleOverlay:has(.scale-overlay-calibrate){padding:var(--padding-medium) 0}.ScaleOverlay[\\:has\\(.scale-overlay-calibrate\\)] .scale-overlay-content,.ScaleOverlay[\\:has\\(.scale-overlay-calibrate\\)] .scale-overlay-footer,.ScaleOverlay[\\:has\\(.scale-overlay-calibrate\\)] .scale-overlay-header{padding:0 var(--padding-medium)}.ScaleOverlay:has(.scale-overlay-calibrate) .scale-overlay-content,.ScaleOverlay:has(.scale-overlay-calibrate) .scale-overlay-footer,.ScaleOverlay:has(.scale-overlay-calibrate) .scale-overlay-header{padding:0 var(--padding-medium)}.ScaleOverlay[\\:has\\(.scale-overlay-calibrate\\)] .divider{width:100%;margin-left:0}.ScaleOverlay:has(.scale-overlay-calibrate) .divider{width:100%;margin-left:0}.ScaleOverlay .scale-overlay-header{position:relative;display:flex;flex-direction:row;justify-content:left;font-weight:400;align-items:center;grid-gap:var(--padding-small);gap:var(--padding-small)}.ScaleOverlay .scale-overlay-header .scale-overlay-title{color:var(--text-color);font-weight:700}.ScaleOverlay .scale-overlay-header .add-new-scale{width:100%;padding:var(--padding-small) var(--padding);background-color:transparent;color:var(--secondary-button-text);font-weight:400;border:1px solid var(--secondary-button-border)}.ScaleOverlay .scale-overlay-header .add-new-scale:hover{border:1px solid var(--secondary-button-hover);box-shadow:none}.ScaleOverlay .scale-overlay-header .scale-overlay-icon,.ScaleOverlay .scale-overlay-header .scale-overlay-icon svg{width:24px;height:24px}.ScaleOverlay .scale-overlay-header .scale-overlay-selector{position:relative}.ScaleOverlay .scale-overlay-header .scale-overlay-selector .scale-overlay-selection{min-width:157px;background-color:transparent;border:1px solid var(--border);border-radius:var(--border-radius);line-height:32px;padding:0 var(--padding-small);color:var(--text-color);font-weight:400;text-align:left;text-overflow:ellipsis;white-space:nowrap;cursor:pointer}.ScaleOverlay .scale-overlay-header .scale-overlay-selector .scale-overlay-selection:hover .scale-overlay-arrow,.ScaleOverlay .scale-overlay-header .scale-overlay-selector .scale-overlay-selection[focus-within] .scale-overlay-arrow{outline:1px solid var(--primary-button-hover);border-radius:var(--border-radius)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector .scale-overlay-selection:focus-within .scale-overlay-arrow,.ScaleOverlay .scale-overlay-header .scale-overlay-selector .scale-overlay-selection:hover .scale-overlay-arrow{outline:1px solid var(--primary-button-hover);border-radius:var(--border-radius)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector .scale-overlay-selection:hover .Icon,.ScaleOverlay .scale-overlay-header .scale-overlay-selector .scale-overlay-selection[focus-within] .Icon{color:var(--primary-button-hover)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector .scale-overlay-selection:focus-within .Icon,.ScaleOverlay .scale-overlay-header .scale-overlay-selector .scale-overlay-selection:hover .Icon{color:var(--primary-button-hover)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector .scale-overlay-arrow{display:flex;justify-content:center;align-items:center;width:24px;height:24px;color:var(--text-color);cursor:pointer}.ScaleOverlay .scale-overlay-header .scale-overlay-selector .scale-overlay-arrow .Icon{width:16px;height:16px}.ScaleOverlay .scale-overlay-header .scale-overlay-selector .scale-overlay-list{display:flex;grid-row-gap:4px;row-gap:4px;flex-direction:column;border-top:1px solid transparent}.ScaleOverlay .scale-overlay-header .scale-overlay-selector ul{margin:0;list-style-type:none;position:absolute;min-width:157px;left:0;top:0;text-align:left;letter-spacing:0;display:none;border-radius:var(--border-radius);pointer-events:all;z-index:1000;background-color:var(--component-background);box-shadow:0 0 4px var(--box-shadow);padding-left:0;padding-bottom:var(--padding-small)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector .scale-overlay-selected-arrow{border:0;padding:0;background-color:transparent;width:100%;height:100%}.ScaleOverlay .scale-overlay-header .scale-overlay-selector .scale-overlay-selected-arrow .Icon{margin:auto}.ScaleOverlay .scale-overlay-header .scale-overlay-selector .scale-overlay-item,.ScaleOverlay .scale-overlay-header .scale-overlay-selector li{display:flex;flex-direction:row;align-items:center;justify-content:space-between;height:32px;grid-column-gap:var(--padding-small);-moz-column-gap:var(--padding-small);column-gap:var(--padding-small)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector li{line-height:32px;padding-left:var(--padding-small);padding-right:var(--padding-small);border:1px solid transparent}.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:first-child:hover .scale-overlay-arrow{outline:1px solid var(--primary-button-hover);border-radius:var(--border-radius)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:first-child:hover .Icon{color:var(--primary-button-hover)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):hover,.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child)[focus-within]{background-color:var(--scale-overlay-item-hover)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):focus-within,.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):hover{background-color:var(--scale-overlay-item-hover)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):hover .options,.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child)[focus-within] .options{color:var(--dropdown-item-active-text)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):focus-within .options,.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):hover .options{color:var(--dropdown-item-active-text)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):hover .Icon,.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child)[focus-within] .Icon{color:var(--dropdown-item-active-icon)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):focus-within .Icon,.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):hover .Icon{color:var(--dropdown-item-active-icon)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):hover .delete:hover:not(:disabled) .Icon,.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):hover .delete[focus-within]:not(:disabled) .Icon,.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child)[focus-within] .delete:hover:not(:disabled) .Icon,.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child)[focus-within] .delete[focus-within]:not(:disabled) .Icon{color:var(--icon-color)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):focus-within .delete:focus-within:not(:disabled) .Icon,.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):focus-within .delete:hover:not(:disabled) .Icon,.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):hover .delete:focus-within:not(:disabled) .Icon,.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:not(:last-child):not(:first-child):hover .delete:hover:not(:disabled) .Icon{color:var(--icon-color)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector li:last-child{height:32px}.ScaleOverlay .scale-overlay-header .scale-overlay-selector .option-selected{background-color:var(--dropdown-item-active)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector .option-selected .options{color:var(--dropdown-item-active-text)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector .option-selected .Icon{color:var(--dropdown-item-active-icon)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector li .delete{width:24px;height:24px;border:none;background-color:transparent;padding:0;border-radius:var(--border-radius);display:flex;align-items:center;justify-content:center}.ScaleOverlay .scale-overlay-header .scale-overlay-selector li .delete .Icon{width:16px;height:16px;display:flex}.ScaleOverlay .scale-overlay-header .scale-overlay-selector .delete:hover,.ScaleOverlay .scale-overlay-header .scale-overlay-selector .delete[focus-within]{background-color:var(--popup-button-hover)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector .delete:focus-within,.ScaleOverlay .scale-overlay-header .scale-overlay-selector .delete:hover{background-color:var(--popup-button-hover)}.ScaleOverlay .scale-overlay-header .scale-overlay-selector .delete:disabled{background-color:transparent;cursor:not-allowed}.ScaleOverlay .scale-overlay-header .scale-overlay-selector li .options{border:none;background-color:transparent;padding:0;display:flex;align-items:center;justify-content:space-between;height:100%}.ScaleOverlay .scale-overlay-header .scale-overlay-selector .scale-value{margin-right:1px}.ScaleOverlay .scale-overlay-content{width:192px;min-height:32px;font-weight:400;font-size:13px;line-height:16px;margin-top:16px}.ScaleOverlay .scale-overlay-footer{margin-top:var(--padding-medium);display:flex;flex-direction:row;justify-content:space-between;width:220px;align-items:center}.ScaleOverlay .scale-overlay-footer .calibration-cancel{padding:0;background-color:transparent;border-radius:var(--border-radius);width:72px;height:32px;color:var(--secondary-button-text);cursor:pointer;border:none}:host(:not([data-tabbing=true])) .ScaleOverlay .scale-overlay-footer .calibration-cancel,html:not([data-tabbing=true]) .ScaleOverlay .scale-overlay-footer .calibration-cancel{outline:none}.ScaleOverlay .scale-overlay-footer .calibration-cancel:enabled:hover{color:var(--secondary-button-hover)}.ScaleOverlay .scale-overlay-footer .calibration-cancel:disabled{opacity:.5}.ScaleOverlay .scale-overlay-footer .calibration-apply{padding:0;border:none;background-color:transparent;background:var(--primary-button);border-radius:var(--border-radius);height:32px;width:72px;color:var(--primary-button-text);cursor:pointer}:host(:not([data-tabbing=true])) .ScaleOverlay .scale-overlay-footer .calibration-apply,html:not([data-tabbing=true]) .ScaleOverlay .scale-overlay-footer .calibration-apply{outline:none}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ScaleOverlay .scale-overlay-footer .calibration-apply{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .ScaleOverlay .scale-overlay-footer .calibration-apply{font-size:13px}}.ScaleOverlay .MeasurementDetail{word-wrap:break-word;flex-wrap:wrap;font-weight:400}.ScaleOverlay .MeasurementDetail .header{display:flex;justify-content:left;align-items:center;width:100%;margin-top:var(--padding-medium);font-weight:700;grid-gap:var(--padding-small);gap:var(--padding-small);font-size:13px}.ScaleOverlay .MeasurementDetail .header .icon{width:24px;height:24px}.ScaleOverlay .MeasurementDetail .scale-input{width:70px;height:24px;margin-right:var(--padding-tiny);padding:var(--padding-tiny)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ScaleOverlay .MeasurementDetail .scale-input{font-size:13px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .ScaleOverlay .MeasurementDetail .scale-input{font-size:13px}}.ScaleOverlay .MeasurementDetail .scale-input:disabled{opacity:.5}.ScaleOverlay .MeasurementDetail .measurement__detail-container{margin-left:var(--padding-huge)}.ScaleOverlay .MeasurementDetail .measurement__detail-container .measurement__detail-item{display:flex;height:24px;align-items:center;margin:var(--padding-tiny);font-size:13px}.ScaleOverlay .MeasurementDetail .distance_input{display:flex;margin-top:var(--padding-small)}.ScaleOverlay .MeasurementDetail .distance_input .distance-show{margin-right:var(--padding-small)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .ScaleOverlay .MeasurementDetail .distance_input .distance-show{width:79px;height:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .ScaleOverlay .MeasurementDetail .distance_input .distance-show{width:79px;height:32px}}.ScaleOverlay .MeasurementDetail .distance-show{text-decoration:underline;margin-right:var(--padding-small)}.ScaleOverlay .MeasurementDetail .angle_input{display:flex;margin-top:var(--padding-small);text-decoration:underline}.ScaleOverlay .MeasurementDetail .measurement__deltas--X,.ScaleOverlay .MeasurementDetail .measurement__deltas--Y{display:flex;margin-top:var(--padding-small)}.ScaleOverlay .MeasurementDetail .measurement_list{width:64px;font-size:13px}.ScaleOverlay .MeasurementDetail .measurement{padding:5px}",""]),a.locals={LEFT_HEADER_WIDTH:"41px",RIGHT_HEADER_WIDTH:"41px"}},1976:function(e,a,t){"use strict";t.r(a);t(23),t(8),t(49),t(53),t(36),t(19),t(12),t(13),t(14),t(10),t(9),t(11),t(16),t(15),t(20),t(18);var r=t(2),l=(t(30),t(141),t(24),t(41),t(0)),o=t.n(l),n=t(48),i=t(43),c=(t(96),t(110),t(83),t(17)),s=t.n(c),d=t(76),p=t(4),u=t.n(p),v=t(429),m=t(6),y=t(3),h=t(1),f=t(135);function b(e){return function(e){if(Array.isArray(e))return x(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||w(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function g(e,a){return function(e){if(Array.isArray(e))return e}(e)||function(e,a){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var r,l,o,n,i=[],c=!0,s=!1;try{if(o=(t=t.call(e)).next,0===a){if(Object(t)!==t)return;c=!1}else for(;!(c=(r=o.call(t)).done)&&(i.push(r.value),i.length!==a);c=!0);}catch(e){s=!0,l=e}finally{try{if(!c&&null!=t.return&&(n=t.return(),Object(n)!==n))return}finally{if(s)throw l}}return i}}(e,a)||w(e,a)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function w(e,a){if(e){if("string"==typeof e)return x(e,a);var t=Object.prototype.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?x(e,a):void 0}}function x(e,a){(null==a||a>e.length)&&(a=e.length);for(var t=0,r=new Array(a);t<a;t++)r[t]=e[t];return r}var M={scales:u.a.arrayOf(u.a.object).isRequired,selectedScales:u.a.arrayOf(u.a.string).isRequired,onScaleSelected:u.a.func.isRequired,onAddingNewScale:u.a.func.isRequired,ariaLabelledBy:u.a.string},S=window.Core.Scale,O=function(e){var a=e.scales,t=void 0===a?[]:a,c=e.selectedScales,p=void 0===c?[]:c,u=e.onScaleSelected,w=e.onAddingNewScale,x=e.ariaLabelledBy,M=g(Object(v.a)(),1)[0],O=Object(m.d)(),E=g(Object(l.useState)([]),2),A=E[0],P=E[1];Object(l.useEffect)((function(){if(t&&0!==t.length){var e=N(t);P(e)}else P([])}),[t]);var j=Object(m.e)((function(e){return y.a.getIsMultipleScalesMode(e)})),W=p.length>1,N=function(e){var a=[],t=h.a.getScales();return e.forEach((function(e){var r=t[e.toString()],l=[],o=new Set,n=!0;r.forEach((function(e){e instanceof window.Core.Annotations.Annotation&&(o.add(e.PageNumber),l.push(e),h.a.canModify(e)||(n=!1))})),a.push({scale:e,title:e.toString(),measurementsNum:l.length,pages:b(o),canDelete:n})})),a},C=function(e){if(e){var a={message:!!e.pages.length?o.a.createElement("div",{className:"customMessage"},o.a.createElement("p",null,o.a.createElement("span",null,M("option.measurement.deleteScaleModal.scaleIsOn-delete-info"),o.a.createElement("b",null," ".concat(M("option.measurement.deleteScaleModal.page-delete-info")," ").concat(e.pages.join(", ")," ")),M("option.measurement.deleteScaleModal.appliedTo-delete-info"),o.a.createElement("b",null," ".concat(e.measurementsNum," ").concat(e.measurementsNum>1?M("option.measurement.deleteScaleModal.measurements"):M("option.measurement.deleteScaleModal.measurement"),"."))),o.a.createElement("span",null," "),o.a.createElement("span",null,M("option.measurement.deleteScaleModal.deletionIs"),o.a.createElement("b",null," ".concat(M("option.measurement.deleteScaleModal.irreversible")," ")),M("option.measurement.deleteScaleModal.willDeleteMeasurement"))),o.a.createElement("p",null,M("option.measurement.deleteScaleModal.confirmDelete"))):o.a.createElement("div",{className:"customMessage"},o.a.createElement("p",null,o.a.createElement("span",null,M("option.measurement.deleteScaleModal.confirmDelete"),M("option.measurement.deleteScaleModal.thisCantBeUndone")))),title:"".concat(M("option.measurement.deleteScaleModal.deleteScale")," ").concat(e.title),confirmBtnText:M("action.confirm"),onConfirm:function(){return h.a.deleteScale(e.scale)}};O(r.a.showWarningMessage(a))}},I=function(e){var a=h.a.getScalePrecision(e),t=S.getFormattedValue(e.pageScale.value,e.pageScale.unit,a,!1),r=S.getFormattedValue(e.worldScale.value,e.worldScale.unit,a,!1),l="".concat(t," = ").concat(r);return o.a.createElement("div",null,l)},k=M("option.measurement.scaleOverlay.multipleScales");if(p.length&&!W){var _=new S(p[0]);k=I(_)}var T=g(Object(l.useState)(!1),2),D=T[0],L=T[1],R=function(){L((function(e){return!e}))},z=Object(l.useRef)(null);Object(f.a)(z,(function(){L(!1)}));return o.a.createElement(d.a,{className:"scale-overlay-selector",dataElement:"scaleSelector",tabIndex:-1,ref:z,onKeyDown:function(e){"Enter"!==e.key&&" "!==e.key||R()},onClick:R},o.a.createElement("div",{"data-testid":"scale-selector",className:"scale-overlay-selection","aria-expanded":D,"aria-labelledby":x,"aria-controls":"scale-overlay-dropdown",role:"combobox",tabIndex:0},o.a.createElement("div",{className:"scale-overlay-item"},o.a.createElement("div",{className:"scale-overlay-name"},k),o.a.createElement("div",{className:"scale-overlay-arrow"},o.a.createElement(i.a,{glyph:"icon-chevron-down",ariaHidden:!0})))),D&&o.a.createElement("ul",{id:"scale-overlay-dropdown",className:s()("scale-overlay-list")},o.a.createElement("li",null,o.a.createElement("div",{className:"scale-overlay-name"},k),o.a.createElement("div",{className:"scale-overlay-arrow"},o.a.createElement("button",{className:"scale-overlay-selected-arrow"},o.a.createElement(i.a,{glyph:"icon-chevron-up",ariaHidden:!0})))),A.map((function(e){return o.a.createElement("li",{key:e.title,className:s()({'className="scale-overlay-item':!0,"option-selected":p.includes(e.title)})},o.a.createElement("button",{className:s()({options:!0}),onClick:function(){return u(p,e.title)},onKeyDown:function(a){"Enter"!==a.key&&" "!==a.key||(a.preventDefault(),u(p,e.title))}},I(e.scale)),o.a.createElement("button",{className:"delete",tabIndex:0,onClick:function(a){a.preventDefault(),a.stopPropagation(),C(e)},onKeyDown:function(a){"Enter"!==a.key&&" "!==a.key||(a.preventDefault(),C(e))},disabled:A.length<=1||!e.canDelete,"aria-label":"".concat(M("action.delete")," ").concat(e.title)},o.a.createElement(i.a,{glyph:"icon-delete-line"})))})),j&&o.a.createElement("li",null,o.a.createElement(n.a,{onClick:w,label:M("option.measurement.scaleOverlay.addNewScale"),className:"add-new-scale"}))))};O.propTypes=M;var E=O,A=t(99);function P(e,a){return function(e){if(Array.isArray(e))return e}(e)||function(e,a){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var r,l,o,n,i=[],c=!0,s=!1;try{if(o=(t=t.call(e)).next,0===a){if(Object(t)!==t)return;c=!1}else for(;!(c=(r=o.call(t)).done)&&(i.push(r.value),i.length!==a);c=!0);}catch(e){s=!0,l=e}finally{try{if(!c&&null!=t.return&&(n=t.return(),Object(n)!==n))return}finally{if(s)throw l}}return i}}(e,a)||function(e,a){if(!e)return;if("string"==typeof e)return j(e,a);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(e);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return j(e,a)}(e,a)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function j(e,a){(null==a||a>e.length)&&(a=e.length);for(var t=0,r=new Array(a);t<a;t++)r[t]=e[t];return r}var W={scales:u.a.arrayOf(u.a.object).isRequired,selectedScales:u.a.arrayOf(u.a.string).isRequired,onScaleSelected:u.a.func.isRequired,onAddingNewScale:u.a.func.isRequired},N=function(e){var a=e.scales,t=e.selectedScales,r=e.onScaleSelected,l=e.onAddingNewScale,c=P(Object(v.a)(),1)[0],s=Object(A.a)(l);return o.a.createElement("div",{className:"scale-overlay-header"},o.a.createElement(i.a,{glyph:"ic-calibrate",className:"scale-overlay-icon"}),o.a.createElement("h4",{id:"scale-dropdown-label",className:"scale-overlay-title"},c("option.measurementOption.scale")),a.length?o.a.createElement(E,{scales:a,selectedScales:t,onScaleSelected:r,onAddingNewScale:l,ariaLabelledBy:"scale-dropdown-label"}):o.a.createElement(n.a,{className:"add-new-scale",onClick:s,dataElement:"addNewScale",label:c("option.measurement.scaleOverlay.addNewScale")}))};N.propTypes=W;var C=N,I=(t(28),t(343),t(50)),k={distanceMeasurement:"option.measurementOverlay.distanceMeasurement",perimeterMeasurement:"option.measurementOverlay.perimeterMeasurement",areaMeasurement:"option.measurementOverlay.areaMeasurement",rectangularAreaMeasurement:"option.measurementOverlay.areaMeasurement",cloudyRectangularAreaMeasurement:"option.measurementOverlay.areaMeasurement",ellipseMeasurement:"option.measurementOverlay.areaMeasurement",arcMeasurement:"option.measurementOverlay.arcMeasurement"},_=t(376),T=function(e){return 1===e?0:null==e?void 0:e.toString().split(".")[1].length},D=t(38),L=function(e,a,t){var r;if(e&&a)if(t){var l=Math.sqrt(Math.pow(a.x-e.x,2)+Math.pow(a.y-e.y,2)),o=Math.sqrt(Math.pow(a.x-t.x,2)+Math.pow(a.y-t.y,2)),n=Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2));r=Math.acos((o*o+l*l-n*n)/(2*o*l))}else r=Math.atan2(a.y-e.y,a.x-e.x),r=(r=Math.abs(r))>Math.PI/2?Math.PI-r:r;return r},R=function(e){switch(e){case"ft'":return"ft";case'in"':return"in";case"ft-in":return"ft";default:return e}};function z(e,a){return function(e){if(Array.isArray(e))return e}(e)||function(e,a){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var r,l,o,n,i=[],c=!0,s=!1;try{if(o=(t=t.call(e)).next,0===a){if(Object(t)!==t)return;c=!1}else for(;!(c=(r=o.call(t)).done)&&(i.push(r.value),i.length!==a);c=!0);}catch(e){s=!0,l=e}finally{try{if(!c&&null!=t.return&&(n=t.return(),Object(n)!==n))return}finally{if(s)throw l}}return i}}(e,a)||function(e,a){if(!e)return;if("string"==typeof e)return U(e,a);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(e);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return U(e,a)}(e,a)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function U(e,a){(null==a||a>e.length)&&(a=e.length);for(var t=0,r=new Array(a);t<a;t++)r[t]=e[t];return r}var q={'in"':"in","ft'":"ft"};F.propTypes={annotation:u.a.object,isOpen:u.a.bool.isRequired,selectedTool:u.a.object,canModify:u.a.bool};var B=window.Core.Scale;function F(e){var a,t=e.annotation,r=e.isOpen,n=e.selectedTool,i=e.canModify,c=z(Object(v.a)(),1)[0],s=Object(m.e)((function(e){return y.a.isDocumentReadOnly(e)})),d=null==t?void 0:t.Measure.axis[0].factor,p=(null==t?void 0:t.DisplayUnits[0])||(null==n||null===(a=n.Measure)||void 0===a?void 0:a.unit),u=z(Object(l.useState)(((null==t?void 0:t.getLineLength())*d||0).toFixed(2)),2),f=u[0],b=u[1];Object(l.useEffect)((function(){if(t){var e=function(){b((t.getLineLength()*d).toFixed(2)),I(W())};return h.a.addEventListener("mouseMove",e),function(){h.a.removeEventListener("mouseMove",e)}}I(W())}),[t,W,d,n]);var g=function(e){b(e.target.value),S(e),w()},w=function(){h.a.getTool("AnnotationCreateDistanceMeasurement").finish()},x=function(){h.a.getAnnotationManager().selectAnnotation(t)},M=function(){h.a.getAnnotationManager().deselectAnnotation(t)},S=function(e){if(t){var a=Math.abs(e.target.value);a<t.Precision&&(a=t.Precision,b(a));var r=t.Measure.axis[0].factor;O(a/r)}},O=Object(l.useCallback)((function(e){if(r=t.getLineLength(),l=e,!(Math.abs(r-l)<.1)){var a=P();t.setLineLength(Math.min(a,e)),A()}var r,l}),[t,A,P]),E=function(e){var a,t;return null!=e&&null!==(a=e.DisplayUnits)&&void 0!==a&&a.length&&(t=2===e.DisplayUnits.length&&"ft'"===e.DisplayUnits[0]&&'in"'===e.DisplayUnits[1]?"in":e.DisplayUnits[0]),q[t]||t||q[p]||p},A=Object(l.useCallback)((function(){var e=h.a.getAnnotationManager();e.drawAnnotations(t.PageNumber),e.trigger("annotationChanged",[[t],"modify",{}])}),[t]),P=Object(l.useCallback)((function(){var e,a,r=h.a.getCurrentPage(),l=h.a.getPageWidth(r),o=h.a.getPageHeight(r),n=t.getAngle()*(180/Math.PI).toFixed(2),i=t.getStartPoint(),c=i.x,s=i.y;e=Math.abs(n)<90?l:0,a=n>0?o:0;var d=Math.abs((e-c)/Math.cos(t.getAngle())),p=Math.abs((a-s)/Math.sin(t.getAngle()));return Math.min(d,p)}),[t]),j=function(e){I(e.target.value),function(e){var a=e.target.value*(Math.PI/180)*-1,r=t.getLineLength(),l=t.Start,o=Math.cos(a)*r+l.x,n=Math.sin(a)*r+l.y;t.setEndPoint(o,n),t.adjustRect(),A()}(e),w()},W=Object(l.useCallback)((function(){if(!t)return 0;var e=t.getAngle();return((e=(e*=-1)<0?e+2*Math.PI:e)/Math.PI*180).toFixed(2)}),[t]),N=z(Object(l.useState)(W()),2),C=N[0],I=N[1];return Object(l.useEffect)((function(){r||O(t.getLineLength())}),[t,O,r]),o.a.createElement(o.a.Fragment,null,o.a.createElement("div",{className:"measurement__detail-item"},o.a.createElement("div",{className:"measurement_list"},c("option.measurementOverlay.distance"),":"),o.a.createElement("input",{className:"scale-input",type:"number",min:"0",disabled:s||!t||!i,value:t?f:0,autoFocus:!D.l,onChange:function(e){g(e),x()},onBlur:function(e){S(e)},onKeyDown:function(e){"Enter"===e.key&&(g(e),M())}}),R(p)),o.a.createElement("div",{className:"measurement__detail-item"},o.a.createElement("div",{className:"measurement_list"},c("option.measurementOverlay.angle"),":"),o.a.createElement("input",{className:"scale-input",type:"number",min:"0",max:"360",disabled:s||!t||!i,value:C,autoFocus:!D.l,onChange:function(e){j(e),x()},onKeyDown:function(e){"Enter"===e.key&&(j(e),M())}}),"°"),function(){var e=t&&L(t.Start,t.End)||0,a=E(t),r=B.getFormattedValue(t&&Math.abs(f*Math.cos(e)),a,null==t?void 0:t.Precision),l=B.getFormattedValue(t&&Math.abs(f*Math.sin(e)),a,null==t?void 0:t.Precision);return o.a.createElement(o.a.Fragment,null,o.a.createElement("div",{className:"measurement__detail-item"},o.a.createElement("div",{className:"measurement_list"},c("option.measurementOverlay.xAxis"),":"),o.a.createElement("div",{className:"measurement"},r)),o.a.createElement("div",{className:"measurement__detail-item"},o.a.createElement("div",{className:"measurement_list"},c("option.measurementOverlay.yAxis"),":"),o.a.createElement("div",{className:"measurement"},l)))}())}var H=F;function V(e,a){return function(e){if(Array.isArray(e))return e}(e)||function(e,a){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var r,l,o,n,i=[],c=!0,s=!1;try{if(o=(t=t.call(e)).next,0===a){if(Object(t)!==t)return;c=!1}else for(;!(c=(r=o.call(t)).done)&&(i.push(r.value),i.length!==a);c=!0);}catch(e){s=!0,l=e}finally{try{if(!c&&null!=t.return&&(n=t.return(),Object(n)!==n))return}finally{if(s)throw l}}return i}}(e,a)||function(e,a){if(!e)return;if("string"==typeof e)return G(e,a);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(e);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return G(e,a)}(e,a)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function G(e,a){(null==a||a>e.length)&&(a=e.length);for(var t=0,r=new Array(a);t<a;t++)r[t]=e[t];return r}var $={annotation:function(e,a,t){return e.annotation||e.selectedTool?(e.annotation&&u.a.checkPropTypes({annotation:u.a.shape({Precision:u.a.number,DisplayUnits:u.a.arrayOf(u.a.string),getContents:u.a.func})},{annotation:e.annotation},"prop","EllipseMeasurementOverlay"),null):new Error("One of props 'annotation' or 'selectedTool' was not specified in '".concat(t,"'."))},selectedTool:function(e,a,t){return e.annotation||e.selectedTool?(e.selectedTool&&u.a.checkPropTypes({selectedTool:u.a.shape({defaults:u.a.shape({Precision:u.a.number}),Measure:u.a.shape({unit:u.a.string})})},{selectedTool:e.selectedTool},"prop","EllipseMeasurementOverlay"),null):new Error("One of props 'annotation' or 'selectedTool' was not specified in '".concat(t,"'."))},isOpen:u.a.bool.isRequired,canModify:u.a.bool};function K(e){var a,t,r,n,i=e.annotation,c=e.isOpen,s=e.selectedTool,d=e.canModify,p=Object(v.a)().t,u=Object(m.e)((function(e){return y.a.isDocumentReadOnly(e)})),f=(null==i||null===(a=i.getMeasurementTextWithScaleAndUnits)||void 0===a?void 0:a.call(i))||0,b={precision:i?i.Precision:null==s||null===(t=s.defaults)||void 0===t?void 0:t.Precision,unit:R(null===(r=(null==i?void 0:i.Scale)||(null==s||null===(n=s.defaults)||void 0===n?void 0:n.Scale))||void 0===r?void 0:r[1][1]),area:f},g=function(){P(w())};Object(l.useEffect)((function(){return g(),h.a.addEventListener("mouseMove",g),function(){h.a.removeEventListener("mouseMove",g)}}),[i]),Object(l.useEffect)((function(){var e=function(e,a){if("deselected"===a){var t=e[0];O(t.getWidth(),t)}};return h.a.addEventListener("annotationSelected",e),function(){h.a.removeEventListener("annotationSelected",e)}}),[]);var w=function(){if(!i)return 0;var e=i&&T(i.Precision)||0,a=i.Measure.axis[0].factor;return((i.Width/2).toFixed(e)*a).toFixed(e)},x=function(e){var a=e.target.value||0,t=a>0?a:1e-4,r=2*(t/i.Measure.axis[0].factor),l=i.getRect(),o={x1:l.x1,y1:l.y1,x2:l.x1+r,y2:l.y1+r};i.setHeight(r),i.setWidth(r),i.resize(o),P(t),M(i),h.a.getTool("AnnotationCreateEllipseMeasurement").finish()},M=function(e){var a=h.a.getAnnotationManager();a.redrawAnnotation(e),a.trigger("annotationChanged",[[e],"modify",[]])},S=Object(l.useCallback)((function(e){var a=h.a.getCurrentPage(),t=h.a.getPageWidth(a),r=h.a.getPageHeight(a),l=t-e.X,o=r-e.Y;return Math.min(l,o)})),O=Object(l.useCallback)((function(e,a){if(e>S(a)){var t=a.getRect(),r=t.x1,l=t.x2,o=t.y1,n=t.y2,i=a.Width,c=a.Height,s=h.a.getCurrentPage(),d=h.a.getPageWidth(s),p=h.a.getPageHeight(s);l>d&&(t.x2=d,i=d-r),n>p&&(t.y2=p,c=p-o),i<d?a.setWidth(i):a.setWidth(d),c<p?a.setHeight(c):a.setHeight(p),a.resize(t),M(a)}}),[S]);Object(l.useEffect)((function(){c||O(i.getWidth(),i)}),[i,O,c]);var E=V(Object(l.useState)(w()),2),A=E[0],P=E[1];return o.a.createElement("div",{className:"measurement__detail-container"},o.a.createElement("div",{className:"measurement__detail-item"},o.a.createElement("div",{className:"measurement_list"},p("option.shared.precision"),":"),o.a.createElement("div",{className:"measurement"},_.q[b.precision]||b.precision)),o.a.createElement("div",{className:"measurement__detail-item"},o.a.createElement("div",{className:"measurement_list"},p("option.measurementOverlay.area"),":"),o.a.createElement("div",{className:"measurement"},b.area)),o.a.createElement("div",{className:"measurement__detail-item"},o.a.createElement("div",{className:"measurement_list"},p("option.measurementOverlay.radius"),":"),o.a.createElement("input",{autoFocus:!D.l,className:"scale-input",type:"number",min:"0",disabled:u||!i||!d,value:A,onChange:function(e){x(e),h.a.getAnnotationManager().selectAnnotation(i)},onBlur:function(e){return function(e){var a=Math.abs(e.target.value),t=i.Measure.axis[0].factor;O(2*(a/t),i),g()}(e)},onKeyDown:function(e){"Enter"===e.key&&(x(e),h.a.getAnnotationManager().deselectAnnotation(i))}}),b.unit))}K.propTypes=$;var Y=K;t(1676);function X(e){return function(e){if(Array.isArray(e))return J(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,a){if(!e)return;if("string"==typeof e)return J(e,a);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(e);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return J(e,a)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function J(e,a){(null==a||a>e.length)&&(a=e.length);for(var t=0,r=new Array(a);t<a;t++)r[t]=e[t];return r}var Q={annotation:u.a.object,isOpen:u.a.bool.isRequired,selectedTool:u.a.object,canModify:u.a.bool},Z=function(e){var a,t,r,n,c=e.annotation,s=e.isOpen,d=e.selectedTool,p=e.canModify,u=Object(v.a)().t,m=Object(l.useMemo)((function(){var e,a,t,r,l=c?Object(I.g)(c):Object(I.j)(d.name);return{key:l,icon:Object(I.e)(l).icon,color:c?c.Color.toHexString():null==d||null===(e=d.defaults)||void 0===e||null===(a=e.StrokeColor)||void 0===a?void 0:a.toHexString(),contents:c?null===(t=c.getMeasurementTextWithScaleAndUnits)||void 0===t?void 0:t.call(c):0,precision:c?c.Precision:null==d||null===(r=d.defaults)||void 0===r?void 0:r.Precision}}));return o.a.createElement("div",{className:"MeasurementDetail"},(a=m.key,t=m.icon,r=m.color,n=k[a],o.a.createElement("div",{className:"header"},o.a.createElement(i.a,{glyph:t,color:r,className:"icon"}),o.a.createElement("div",null,u(n)))),function(){var e,a,t=m.key,r=m.precision;return"ellipseMeasurement"===t?o.a.createElement(Y,{annotation:c,selectedTool:d,isOpen:s,canModify:p}):o.a.createElement("div",{className:"measurement__detail-container"},o.a.createElement("div",{className:"measurement__detail-item"},o.a.createElement("div",{className:"measurement_list"},u("option.shared.precision"),":"),o.a.createElement("div",{className:"measurement"},_.q[r]||r)),"distanceMeasurement"===t&&o.a.createElement(H,{annotation:c,isOpen:s,selectedTool:d,canModify:p}),["rectangularAreaMeasurement","cloudyRectangularAreaMeasurement","perimeterMeasurement","areaMeasurement"].includes(t)&&function(){var e=m.key,a=m.contents,t={distanceMeasurement:u("option.measurementOverlay.distance"),perimeterMeasurement:u("option.measurementOverlay.perimeter"),areaMeasurement:u("option.measurementOverlay.area"),rectangularAreaMeasurement:u("option.measurementOverlay.area"),cloudyRectangularAreaMeasurement:u("option.measurementOverlay.area")};return o.a.createElement("div",{className:"measurement__detail-item"},o.a.createElement("div",{className:"measurement_list"},t[e],":"),o.a.createElement("div",{className:"measurement"},a))}(),"arcMeasurement"===t&&(a=(null==c?void 0:c.Length)||0,o.a.createElement("div",{className:"measurement__detail-item"},o.a.createElement("div",{className:"measurement_list"},u("option.measurementOverlay.length")),o.a.createElement("div",{className:"measurement"},a))),"arcMeasurement"===t&&(e=(null==c?void 0:c.Radius)||0,o.a.createElement("div",{className:"measurement__detail-item"},o.a.createElement("div",{className:"measurement_list"},u("option.measurementOverlay.radius")),o.a.createElement("div",{className:"measurement"},e))),!["rectangularAreaMeasurement","distanceMeasurement","cloudyRectangularAreaMeasurement"].includes(t)&&function(){if(!c)return o.a.createElement("div",{className:"measurement__detail-item"},o.a.createElement("div",{className:"measurement_list"},u("option.measurementOverlay.angle"),":"),o.a.createElement("div",{className:"measurement"},"0°"));var e=m.key,a=function(e){var a=e.getPath(),t=a.length;return[a[t-3],a[t-2],a[t-1]]},t={distanceMeasurement:function(e){return[e.Start,e.End]},perimeterMeasurement:a,areaMeasurement:a,rectangularAreaMeasurement:a,cloudyRectangularAreaMeasurement:a,arcMeasurement:a}[e](c).filter((function(e){return!!e})),r=L.apply(void 0,X(t));if(r){var l=T(c.Precision);r=(r/Math.PI*180).toFixed(l)}return"arcMeasurement"===e&&(r=c.Angle.toFixed(2)),void 0!==r&&o.a.createElement("div",{className:"measurement__detail-item"},o.a.createElement("div",{className:"measurement_list"},u("option.measurementOverlay.angle"),":"),o.a.createElement("div",{className:"measurement"},r,"°"))}())}())};Z.propTypes=Q;var ee=Z;function ae(e,a){return function(e){if(Array.isArray(e))return e}(e)||function(e,a){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var r,l,o,n,i=[],c=!0,s=!1;try{if(o=(t=t.call(e)).next,0===a){if(Object(t)!==t)return;c=!1}else for(;!(c=(r=o.call(t)).done)&&(i.push(r.value),i.length!==a);c=!0);}catch(e){s=!0,l=e}finally{try{if(!c&&null!=t.return&&(n=t.return(),Object(n)!==n))return}finally{if(s)throw l}}return i}}(e,a)||function(e,a){if(!e)return;if("string"==typeof e)return te(e,a);var t=Object.prototype.toString.call(e).slice(8,-1);"Object"===t&&e.constructor&&(t=e.constructor.name);if("Map"===t||"Set"===t)return Array.from(e);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return te(e,a)}(e,a)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function te(e,a){(null==a||a>e.length)&&(a=e.length);for(var t=0,r=new Array(a);t<a;t++)r[t]=e[t];return r}var re=window.Core.Scale,le={tempScale:u.a.string,onCancelCalibrationMode:u.a.func,onApplyCalibration:u.a.func},oe=function(e){var a,t=e.tempScale,r=e.onCancelCalibrationMode,l=e.onApplyCalibration,n=ae(Object(v.a)(),1)[0],c=Object(m.e)((function(e){return y.a.isElementOpen(e,"annotationPopup")})),s=c&&t;return o.a.createElement("div",{className:"scale-overlay-calibrate"},o.a.createElement("div",{className:"scale-overlay-header"},o.a.createElement(i.a,{glyph:"ic-calibrate",className:"scale-overlay-icon"}),o.a.createElement("div",{className:"scale-overlay-title"},n("option.measurement.scaleModal.calibrate"))),o.a.createElement("div",{className:"scale-overlay-content"},n(s?"option.measurement.scaleOverlay.inputKnowDimension":"option.measurement.scaleOverlay.selectTwoPoints")),o.a.createElement("div",{className:"divider"}),o.a.createElement("div",{className:"scale-overlay-footer"},o.a.createElement("button",{className:"calibration-cancel",onMouseDown:r},n("action.cancel")),o.a.createElement("button",{className:"calibration-apply",disabled:!(c&&t&&(null===(a=new re(t).worldScale)||void 0===a?void 0:a.value)>0),"data-element":"calibrationApply",onMouseDown:l,onTouchStart:l},n("action.apply"))))};oe.propTypes=le;var ne=oe;function ie(e){return function(e){if(Array.isArray(e))return de(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||se(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ce(e,a){return function(e){if(Array.isArray(e))return e}(e)||function(e,a){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var r,l,o,n,i=[],c=!0,s=!1;try{if(o=(t=t.call(e)).next,0===a){if(Object(t)!==t)return;c=!1}else for(;!(c=(r=o.call(t)).done)&&(i.push(r.value),i.length!==a);c=!0);}catch(e){s=!0,l=e}finally{try{if(!c&&null!=t.return&&(n=t.return(),Object(n)!==n))return}finally{if(s)throw l}}return i}}(e,a)||se(e,a)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function se(e,a){if(e){if("string"==typeof e)return de(e,a);var t=Object.prototype.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?de(e,a):void 0}}function de(e,a){(null==a||a>e.length)&&(a=e.length);for(var t=0,r=new Array(a);t<a;t++)r[t]=e[t];return r}var pe=window.Core.Scale,ue={annotations:u.a.arrayOf(u.a.object).isRequired,selectedTool:u.a.object,updateIsCalibration:u.a.func.isRequired,enableOrDisableToolElements:u.a.func.isRequired,onScaleSelected:u.a.func.isRequired,onCancelCalibrationMode:u.a.func.isRequired,onApplyCalibration:u.a.func.isRequired,onAddingNewScale:u.a.func.isRequired},ve=function(e){var a=e.annotations,t=e.selectedTool,r=e.updateIsCalibration,n=e.enableOrDisableToolElements,i=e.onScaleSelected,c=e.onCancelCalibrationMode,s=e.onApplyCalibration,d=e.onAddingNewScale,p=e.forceUpdate,u=ce(Object(m.e)((function(e){return[y.a.getCalibrationInfo(e),y.a.getActiveToolName(e)]}),m.c),2),v=u[0],f=v.isCalibration,b=v.tempScale,g=v.previousToolName,w=void 0===g?"AnnotationCreateDistanceMeasurement":g,x=v.isFractionalUnit,M=u[1],S=ce(Object(l.useState)(h.a.getScales()),2),O=S[0],E=S[1],A=!(!Object.keys(O).length||!t&&(!a.length||a.length>1));Object(l.useEffect)((function(){p()}),[O]),Object(l.useEffect)((function(){var e=function(e){E(e)},a=function(){E(h.a.getScales())},t=function(){d()};return h.a.addEventListener("scaleUpdated",e),h.a.addEventListener("createAnnotationWithNoScale",t),h.a.addEventListener("annotationsLoaded",a),h.a.addEventListener("annotationChanged",a),function(){h.a.removeEventListener("scaleUpdated",e),h.a.removeEventListener("createAnnotationWithNoScale",t),h.a.removeEventListener("annotationsLoaded",a),h.a.removeEventListener("annotationChanged",a)}}),[]),Object(l.useEffect)((function(){"AnnotationCreateCalibrationMeasurement"===M?(!f&&r(!0),n(!0)):(r(!1),n(!1))}),[M,r,n]);var P=Object(l.useMemo)((function(){var e=new Set;return[].concat(ie(a),[t]).forEach((function(a){var t,r=null==a||null===(t=a.Measure)||void 0===t?void 0:t.scale;r&&!e.has(r)&&e.add(r)})),ie(e)}),[a,t,O]),j=Object.keys(O).map((function(e){return new pe(e)})),W=1===a.length&&h.a.canModify(a[0]);return f?o.a.createElement(ne,{tempScale:b,onCancelCalibrationMode:function(){return c(w)},onApplyCalibration:function(){return s(w,b,x)},previousToolName:w}):o.a.createElement(o.a.Fragment,null,o.a.createElement(C,{scales:j,selectedScales:P,onScaleSelected:i,onAddingNewScale:d}),A&&o.a.createElement(ee,{annotation:a.length>1?null:a[0]||null,selectedTool:t,isOpen:!0,canModify:W}))};ve.propTypes=ue;var me=Object(l.memo)(ve),ye=t(176),he=t.n(ye),fe=t(5),be=t(328),ge=t(61);function we(e){return function(e){if(Array.isArray(e))return Se(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||Me(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function xe(e,a){return function(e){if(Array.isArray(e))return e}(e)||function(e,a){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var r,l,o,n,i=[],c=!0,s=!1;try{if(o=(t=t.call(e)).next,0===a){if(Object(t)!==t)return;c=!1}else for(;!(c=(r=o.call(t)).done)&&(i.push(r.value),i.length!==a);c=!0);}catch(e){s=!0,l=e}finally{try{if(!c&&null!=t.return&&(n=t.return(),Object(n)!==n))return}finally{if(s)throw l}}return i}}(e,a)||Me(e,a)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Me(e,a){if(e){if("string"==typeof e)return Se(e,a);var t=Object.prototype.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?Se(e,a):void 0}}function Se(e,a){(null==a||a>e.length)&&(a=e.length);for(var t=0,r=new Array(a);t<a;t++)r[t]=e[t];return r}var Oe=window.Core.Scale,Ee=["distanceToolGroupButton","arcMeasurementToolGroupButton","perimeterToolGroupButton","areaToolGroupButton","rectangleAreaToolGroupButton","ellipseAreaToolGroupButton","countToolGroupButton","cloudyRectangleAreaToolGroupButton","arcToolGroupButton"];a.default=function(e){var a=e.annotations,t=e.selectedTool,n=Object(m.d)(),i=xe(Object(v.a)(),1)[0],c=xe(Object(m.e)((function(e){return[y.a.isElementDisabled(e,fe.a.SCALE_OVERLAY_CONTAINER),y.a.isElementOpen(e,fe.a.SCALE_OVERLAY_CONTAINER),y.a.getScaleOverlayPosition(e)]})),3),d=c[0],p=c[1],u=c[2],f=xe(Object(l.useState)({x:0,y:0}),2),b=f[0],g=f[1],w=xe(Object(l.useReducer)((function(e){return e+1}),0,(function(){return 0})),2)[1],x=xe(Object(m.e)((function(e){return[y.a.getDocumentContainerWidth(e),y.a.getDocumentContainerHeight(e)]})),2),M=x[0],S=x[1],O=Object(l.useRef)(null),E=h.a.getViewerElement(),A=h.a.getScrollViewElement(),P=function(){var e=u.split("-"),a={left:0,top:0};if("top"===e[0])a.top=(null==E?void 0:E.offsetTop)+10||85;else{var t=400;null!=O&&O.current&&(t=O.current.getBoundingClientRect().height),a.top=S+(null==A?void 0:A.offsetTop)-10-t||85}if("right"===e[1])a.left=.666*M,E&&null!=O&&O.current&&(a.left=Math.min((null==E?void 0:E.offsetLeft)+(null==E?void 0:E.offsetWidth)+10||a.left,M-O.current.getBoundingClientRect().width-10));else{if(E&&null!=O&&O.current){var r=O.current.getBoundingClientRect().width;a.left=(null==E?void 0:E.offsetLeft)-10-r||10,A&&a.left<A.offsetLeft&&(a.left=A.offsetLeft+10)}(!a.left||isNaN(a.left)||a.left<0)&&(a.left=10)}return a}();Object(l.useEffect)((function(){g({x:0,y:0})}),[u]);var j,W,N=function(e,a){var t=a.x,r=a.y;g({x:t,y:r})},C=Object(l.useCallback)((function(e){n(r.a.updateCalibrationInfo({isCalibration:e}))}),[]),I=Object(l.useCallback)((function(e){Ee.forEach((function(a){n(r.a.setCustomElementOverrides(a,{disabled:e}))}))}),[]),k=Object(l.useCallback)((function(e){e&&function(e){n(r.a.setSelectedScale(e))}(new Oe(e)),n(r.a.openElements([fe.a.SCALE_MODAL])),n(r.a.setIsAddingNewScale())}),[]),_=Object(l.useCallback)((function(e,r){var l=new Oe(r);if(1===e.length&&e.includes(r))k(r);else{var o=[].concat(we(a),[t]),n=h.a.getDocumentViewer().getMeasurementManager().getOldScalesToDeleteAfterApplying({scale:l,applyTo:o})[0],i=function(){h.a.createAndApplyScale(l,[].concat(we(a),[t]))};n?T(n,i):i()}}),[a,t]),T=function(e,a){var t={message:o.a.createElement("div",{className:"customMessage"},o.a.createElement("p",null,o.a.createElement("span",null,i("option.measurement.deleteScaleModal.ifChangeScale"),o.a.createElement("b",null,e),i("option.measurement.deleteScaleModal.notUsedWillDelete"))),o.a.createElement("p",null,o.a.createElement("span",null,i("option.measurement.deleteScaleModal.ifToContinue")))),title:"".concat(i("option.measurement.deleteScaleModal.deleteScale")," ").concat(e),confirmBtnText:i("action.confirm"),onConfirm:function(){return a()}};n(r.a.showWarningMessage(t))},D=Object(l.useCallback)((function(e){h.a.setToolMode(e),C(!1),n(r.a.setIsElementHidden(fe.a.SCALE_MODAL,!1))}),[]),L=Object(l.useCallback)((function(e,t,l){n(r.a.updateCalibrationInfo({isCalibration:!1,tempScale:t,isFractionalUnit:l})),n(r.a.setIsElementHidden(fe.a.SCALE_MODAL,!1)),h.a.setToolMode(e),h.a.deleteAnnotations([a[0]])}),[a]),R=Object(l.useCallback)((function(){k(),n(r.a.setIsAddingNewScale(!0))}),[]);return Object(ge.b)()?!d&&o.a.createElement(be.a,null,o.a.createElement(me,{annotations:a,selectedTool:t,updateIsCalibration:C,enableOrDisableToolElements:I,onScaleSelected:_,onCancelCalibrationMode:D,onApplyCalibration:L,onAddingNewScale:R,forceUpdate:w,tabIndex:0})):!d&&o.a.createElement(he.a,{position:b,bounds:(j=u.split("-"),W={top:0,bottom:0,left:0,right:0},"top"===j[0]?(W.top=0,W.bottom=S-20,O.current?W.bottom-=O.current.getBoundingClientRect().height:W.bottom-=85):(W.top=20-S,O.current?W.top+=O.current.getBoundingClientRect().height:W.top+=85,W.bottom=0),"right"===j[1]?(W.left=-M,W.right=M/3,P&&(W.right=M-P.left)):(W.left=null==A?void 0:A.offsetLeft,P&&(W.left=(null==A?void 0:A.offsetLeft)-P.left+10),W.right=M-10-35,P&&(W.right-=P.left)),W),onDrag:N,onStop:N,cancel:".scale-overlay-selector, .add-new-scale"},o.a.createElement("div",{className:s()({Overlay:!0,ScaleOverlay:!0,open:p,closed:!p}),"data-element":fe.a.SCALE_OVERLAY_CONTAINER,style:P,ref:O},o.a.createElement(me,{annotations:a,selectedTool:t,updateIsCalibration:C,enableOrDisableToolElements:I,onScaleSelected:_,onCancelCalibrationMode:D,onApplyCalibration:L,onAddingNewScale:R,forceUpdate:w,tabIndex:0})))}}}]);
//# sourceMappingURL=chunk.77.js.map