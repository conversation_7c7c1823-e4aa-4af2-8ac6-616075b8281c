{"version": 3, "sources": ["webpack:///./src/ui/src/components/PageManipulationOverlay/PageRotationControls/PageRotationControls.js", "webpack:///./src/ui/src/components/PageManipulationOverlay/PageRotationControls/PageRotationControlsContainer.js", "webpack:///./src/ui/src/components/PageManipulationOverlay/PageRotationControls/index.js", "webpack:///./src/ui/src/components/PageManipulationOverlay/CustomPageManipulationOperations/CustomPageManipulationOperations.js", "webpack:///./src/ui/src/components/PageManipulationOverlay/CustomPageManipulationOperations/index.js", "webpack:///./src/ui/src/components/PageManipulationOverlay/PageManipulationOverlay.js", "webpack:///./src/ui/src/components/PageManipulationOverlay/PageManipulationOverlayContainer.js", "webpack:///./src/ui/src/components/PageManipulationOverlay/index.js"], "names": ["PageRotationControls", "props", "t", "useTranslation", "rotateClockwise", "rotateCounterClockwise", "DataElementWrapper", "dataElement", "className", "onClick", "<PERSON><PERSON>", "title", "img", "role", "propTypes", "pageNumbers", "PropTypes", "arrayOf", "number", "warn", "bool", "PageRotationControlsContainer", "dispatch", "useDispatch", "noPagesSelectedWarning", "isMobile", "actions", "closeElement", "DataElements", "PAGE_MANIPULATION_OVERLAY", "CustomPageManipulationOperations", "header", "operations", "map", "operation", "key", "renderIcon", "InitialPageManipulationOverlay", "children", "pageManipulationOverlayItems", "childrenA<PERSON>y", "React", "Children", "toArray", "item", "index", "type", "component", "find", "child", "cloneElement", "PageManipulationOverlay", "closeOverlay", "useCallback", "setPageManipulationOverlayAlternativePosition", "closeElements", "document", "core", "getDocument", "documentType", "isXod", "workerTypes", "XOD", "isOffice", "OFFICE", "LEGACY_OFFICE", "filteredPageManipulationOverlayItems", "removedIndices", "reduce", "acc", "push", "filter", "_", "includes", "useEffect", "addEventListener", "removeEventListener", "PageAdditionalControls", "PageManipulationControls", "PageManipulationOverlayContainer", "useSelector", "state", "selectors", "getSelectedThumbnailPageIndexes", "getCurrentPage", "getPageManipulationOverlayItems", "shallowEqual", "selectedPageIndexes", "currentPage", "length", "i", "FlyoutMenu", "menu", "trigger", "PAGE_MANIPULATION_OVERLAY_BUTTON", "aria<PERSON><PERSON><PERSON>", "aria-live"], "mappings": "mSA+CeA,MAzCf,SAA8BC,GAC5B,IAAQC,EAAMC,cAAND,EACAE,EAA4CH,EAA5CG,gBAAiBC,EAA2BJ,EAA3BI,uBAGzB,OACE,oCACE,kBAACC,EAAA,EAAkB,CACjBC,YAAY,qBACZC,UAAU,QAETN,EAAE,kBAEL,kBAACI,EAAA,EAAkB,CACjBC,YAAY,sBACZC,UAAU,MACVC,QAASL,GAET,kBAACM,EAAA,EAAM,CACLC,MAAM,4CACNC,IAAI,6DACJC,KAAK,WAEP,yBAAKL,UAAU,SAASN,EAAE,2CAE5B,kBAACI,EAAA,EAAkB,CACjBC,YAAY,6BACZC,UAAU,MACVC,QAASJ,GAET,kBAACK,EAAA,EAAM,CACLC,MAAM,mDACNC,IAAI,oEACJC,KAAK,WAEP,yBAAKL,UAAU,SAASN,EAAE,oD,wCChC5BY,EAAY,CAChBC,YAAaC,IAAUC,QAAQD,IAAUE,QACzCC,KAAMH,IAAUI,MAGlB,SAASC,EAA8BpB,GACrC,IAAMqB,EAAWC,cACTR,EAAsBd,EAAtBc,YAAaI,EAASlB,EAATkB,KAkBrB,OACE,kBAAC,EAAoB,CACnBd,uBAlB6B,WAC3Bc,GACDK,YAAuBT,EAAaO,IAAajB,YAAuBU,GAEzEV,YAAuBU,GAEzBU,eAAcH,EAASI,IAAQC,aAAaC,IAAaC,6BAavDzB,gBAXsB,WACpBe,GACDK,YAAuBT,EAAaO,IAAalB,YAAgBW,GAElEX,YAAgBW,GAElBU,eAAcH,EAASI,IAAQC,aAAaC,IAAaC,+BAU7DR,EAA8BP,UAAYA,EAE3BO,IC1CAA,ED0CAA,E,oBEAAS,IC1CAA,EDEf,SAA0C7B,GACxC,IAAQM,EAAiDN,EAAjDM,YAAaQ,EAAoCd,EAApCc,YAAagB,EAAuB9B,EAAvB8B,OAAQC,EAAe/B,EAAf+B,WAc1C,OACE,oCACE,kBAAC1B,EAAA,EAAkB,CACjBC,YAAaA,EACbC,UAAU,QAETuB,GAEFC,EAAWC,KAAI,SAACC,GACf,OACE,kBAAC5B,EAAA,EAAkB,CACjB6B,IAAKD,EAAU3B,YACfC,UAAU,MACVD,YAAa2B,EAAU3B,YACvBE,QAAS,kBAAMyB,EAAUzB,QAAQM,KA1B3C,SAAoBmB,GAClB,GAAIA,EAAUtB,IACZ,OACE,kBAACF,EAAA,EAAM,CACLC,MAAOuB,EAAUvB,MACjBC,IAAKsB,EAAUtB,IACfC,KAAK,WAsBFuB,CAAWF,GACZ,yBAAK1B,UAAU,SAAS0B,EAAUvB,a,kPExB9C,SAAS0B,EAA+B,GAAyD,IAAvDC,EAAQ,EAARA,SAAUvB,EAAW,EAAXA,YAAawB,EAA4B,EAA5BA,6BACzDC,EAAgBC,IAAMC,SAASC,QAAQL,GAE7C,OAAOC,EAA6BN,KAAI,SAACW,EAAMC,GAC7C,IAAQtC,EAAsBqC,EAAtBrC,YAAauC,EAASF,EAATE,KACjBC,EAAYP,EAAcQ,MAAK,SAACC,GAAK,OAAKA,EAAMhD,MAAMM,cAAgBA,KACpE4B,EAAM5B,GAAe,GAAJ,OAAOuC,EAAI,YAAID,GAYtC,OAVKE,IACU,YAATD,IACFC,EAAY,yBAAKvC,UAAU,aAGhB,wBAATsC,IACFC,EAAY,kBAAC,EAAgC,GAACZ,IAAK5B,EAAaQ,YAAaA,GAAiB6B,MAI3FG,EACHN,IAAMS,aAAaH,EAAW,CAC9BZ,QAEA,QAuDOgB,MAlDf,SAAiClD,GAC/B,IAAQc,EAA8Cd,EAA9Cc,YAAawB,EAAiCtC,EAAjCsC,6BAEfjB,EAAWC,cAEX6B,EAAeC,uBAAY,WAC/B/B,EAASI,IAAQ4B,8CAA8C,OAC/DhC,EAASI,IAAQ6B,cAAc,CAAC3B,IAAaC,+BAC5C,CAACP,IAEEkC,EAAWC,IAAKC,cAChBC,EAAeH,aAAQ,EAARA,EAAUV,KACzBc,EAAQD,IAAiBE,IAAYC,IACrCC,EAAWJ,IAAiBE,IAAYG,QAAUL,IAAiBE,IAAYI,cACjFC,EAAuC3B,EAC3C,GAAIqB,GAASG,EAAU,CACrB,IAAMI,EAAiBD,EAAqCE,QAAO,SAACC,EAAK,EAAiBxB,GAAU,IACpD,EAME,EAPyBtC,EAAW,EAAXA,YACrD,2BAAhBA,IACF8D,EAAIC,KAAKzB,GACqD,aAAX,QAA/C,EAAAqB,EAAqCrB,EAAQ,UAAE,aAA/C,EAAiDC,OACnDuB,EAAIC,KAAKzB,EAAQ,IAGD,6BAAhBtC,IACF8D,EAAIC,KAAKzB,GACqD,aAAX,QAA/C,EAAAqB,EAAqCrB,EAAQ,UAAE,aAA/C,EAAiDC,OACnDuB,EAAIC,KAAKzB,EAAQ,IAGrB,OAAOwB,IACN,IACHH,EAAuCA,EAAqCK,QAAO,SAACC,EAAG3B,GAAK,OAAMsB,EAAeM,SAAS5B,MAU5H,OAPA6B,qBAAU,WAER,OADAjB,IAAKkB,iBAAiB,iBAAkBvB,GACjC,WACLK,IAAKmB,oBAAoB,iBAAkBxB,MAE5C,IAGD,kBAACf,EAA8B,CAACtB,YAAaA,EAAawB,6BAA8B2B,IACnFN,IAAUG,GAAY,kBAACc,EAAA,EAAsB,CAAC9D,YAAaA,EAAaR,YAAY,2BACvF,kBAAC,EAAoB,CAACQ,YAAaA,EAAaR,YAAY,0BACzDqD,IAAUG,GAAY,kBAACe,EAAA,EAAwB,CAAC/D,YAAaA,EAAaR,YAAY,+B,+iCC3ChFwE,MAjCf,WACE,IAQgB,IAJZC,aAAY,SAACC,GAAK,MAAK,CACzBC,IAAUC,gCAAgCF,GAC1CC,IAAUE,eAAeH,GACzBC,IAAUG,gCAAgCJ,MACzCK,KAAa,GAPdC,EAAmB,KACnBC,EAAW,KACXjD,EAA4B,KAQxBxB,EAAcwE,EAAoBE,OAAS,EAAIF,EAAoBtD,KAAI,SAACyD,GAAC,OAAKA,EAAI,KAAK,CAACF,GAEvFtF,EAAqB,EAAhBC,cAAgB,GAApB,GAER,OACE,kBAACwF,EAAA,EAAU,CACTC,KAAMhE,IAAaC,0BACnBgE,QAASjE,IAAakE,iCACtBC,UAAW7F,EAAE,0CAEb,0BAAMM,UAAU,mBACd,uBAAGwF,YAAU,YAAYnF,KAAK,SAAQ,IAAEX,EAAE,2CAE5C,kBAAC,EAAuB,CACtBa,YAAaA,EACbwB,6BAA8BA,MCjCvBY", "file": "chunks/chunk.96.js", "sourcesContent": ["import React from 'react';\nimport { useTranslation } from 'react-i18next';\nimport DataElementWrapper from 'components/DataElementWrapper';\nimport Button from 'components/Button';\n\n\nfunction PageRotationControls(props) {\n  const { t } = useTranslation();\n  const { rotateClockwise, rotateCounterClockwise } = props;\n\n\n  return (\n    <>\n      <DataElementWrapper\n        dataElement=\"pageRotationHeader\"\n        className=\"type\"\n      >\n        {t('action.rotate')}\n      </DataElementWrapper>\n      <DataElementWrapper\n        dataElement=\"rotatePageClockwise\"\n        className=\"row\"\n        onClick={rotateClockwise}\n      >\n        <Button\n          title=\"option.thumbnailPanel.rotatePageClockwise\"\n          img=\"icon-header-page-manipulation-page-rotation-clockwise-line\"\n          role=\"option\"\n        />\n        <div className=\"title\">{t('option.thumbnailPanel.rotateClockwise')}</div>\n      </DataElementWrapper>\n      <DataElementWrapper\n        dataElement=\"rotatePageCounterClockwise\"\n        className=\"row\"\n        onClick={rotateCounterClockwise}\n      >\n        <Button\n          title=\"option.thumbnailPanel.rotatePageCounterClockwise\"\n          img=\"icon-header-page-manipulation-page-rotation-counterclockwise-line\"\n          role=\"option\"\n        />\n        <div className=\"title\">{t('option.thumbnailPanel.rotateCounterClockwise')}</div>\n      </DataElementWrapper>\n    </>\n  );\n}\n\nexport default PageRotationControls;", "import React from 'react';\nimport PageRotationControls from './PageRotationControls';\nimport { noPagesSelectedWarning, rotateClockwise, rotateCounterClockwise } from 'helpers/pageManipulationFunctions';\nimport { useDispatch } from 'react-redux';\nimport PropTypes from 'prop-types';\nimport actions from 'actions';\nimport { isMobile } from 'helpers/device';\nimport DataElements from 'constants/dataElement';\n\nconst propTypes = {\n  pageNumbers: PropTypes.arrayOf(PropTypes.number),\n  warn: PropTypes.bool,\n};\n\nfunction PageRotationControlsContainer(props) {\n  const dispatch = useDispatch();\n  const { pageNumbers, warn } = props;\n\n  const onRotateCounterClockwise = () => {\n    if (warn) {\n      !noPagesSelectedWarning(pageNumbers, dispatch) && rotateCounterClockwise(pageNumbers);\n    } else {\n      rotateCounterClockwise(pageNumbers);\n    }\n    isMobile() && dispatch(actions.closeElement(DataElements.PAGE_MANIPULATION_OVERLAY));\n  };\n  const onRotateClockwise = () => {\n    if (warn) {\n      !noPagesSelectedWarning(pageNumbers, dispatch) && rotateClockwise(pageNumbers);\n    } else {\n      rotateClockwise(pageNumbers);\n    }\n    isMobile() && dispatch(actions.closeElement(DataElements.PAGE_MANIPULATION_OVERLAY));\n  };\n  return (\n    <PageRotationControls\n      rotateCounterClockwise={onRotateCounterClockwise}\n      rotateClockwise={onRotateClockwise}\n    />\n  );\n}\n\nPageRotationControlsContainer.propTypes = propTypes;\n\nexport default PageRotationControlsContainer;", "import PageRotationControlsContainer from './PageRotationControlsContainer';\n\nexport default PageRotationControlsContainer;", "import React from 'react';\nimport DataElementWrapper from 'components/DataElementWrapper';\nimport Button from 'components/Button';\n\nfunction CustomPageManipulationOperations(props) {\n  const { dataElement, pageNumbers, header, operations } = props;\n\n  function renderIcon(operation) {\n    if (operation.img) {\n      return (\n        <Button\n          title={operation.title}\n          img={operation.img}\n          role=\"option\"\n        />\n      );\n    }\n  }\n\n  return (\n    <>\n      <DataElementWrapper\n        dataElement={dataElement}\n        className=\"type\"\n      >\n        {header}\n      </DataElementWrapper>\n      {operations.map((operation) => {\n        return (\n          <DataElementWrapper\n            key={operation.dataElement}\n            className=\"row\"\n            dataElement={operation.dataElement}\n            onClick={() => operation.onClick(pageNumbers)}\n          >\n            {renderIcon(operation)}\n            <div className=\"title\">{operation.title}</div>\n          </DataElementWrapper>\n        );\n      })}\n    </>\n  );\n}\n\nexport default CustomPageManipulationOperations;", "import CustomPageManipulationOperations from './CustomPageManipulationOperations';\n\nexport default CustomPageManipulationOperations;", "import actions from 'actions';\nimport React, { useEffect, useCallback } from 'react';\nimport PageRotationControls from './PageRotationControls';\nimport PageManipulationControls from './PageManipulationControls';\nimport PageAdditionalControls from 'components/PageManipulationOverlay/PageAdditionalControls';\nimport CustomPageManipulationOperations from './CustomPageManipulationOperations';\nimport { workerTypes } from 'constants/types';\nimport core from 'core';\n\nimport { useDispatch } from 'react-redux';\nimport DataElements from 'src/constants/dataElement';\n\nfunction InitialPageManipulationOverlay({ children, pageNumbers, pageManipulationOverlayItems }) {\n  const childrenArray = React.Children.toArray(children);\n\n  return pageManipulationOverlayItems.map((item, index) => {\n    const { dataElement, type } = item;\n    let component = childrenArray.find((child) => child.props.dataElement === dataElement);\n    const key = dataElement || `${type}-${index}`;\n\n    if (!component) {\n      if (type === 'divider') {\n        component = <div className=\"divider\" />;\n      }\n\n      if (type === 'customPageOperation') {\n        component = <CustomPageManipulationOperations key={dataElement} pageNumbers={pageNumbers} {...item} />;\n      }\n    }\n\n    return component\n      ? React.cloneElement(component, {\n        key,\n      })\n      : null;\n  });\n}\n\n\nfunction PageManipulationOverlay(props) {\n  const { pageNumbers, pageManipulationOverlayItems } = props;\n\n  const dispatch = useDispatch();\n\n  const closeOverlay = useCallback(() => {\n    dispatch(actions.setPageManipulationOverlayAlternativePosition(null));\n    dispatch(actions.closeElements([DataElements.PAGE_MANIPULATION_OVERLAY]));\n  }, [dispatch]);\n\n  const document = core.getDocument();\n  const documentType = document?.type;\n  const isXod = documentType === workerTypes.XOD;\n  const isOffice = documentType === workerTypes.OFFICE || documentType === workerTypes.LEGACY_OFFICE;\n  let filteredPageManipulationOverlayItems = pageManipulationOverlayItems;\n  if (isXod || isOffice) {\n    const removedIndices = filteredPageManipulationOverlayItems.reduce((acc, { dataElement }, index) => {\n      if (dataElement === 'pageAdditionalControls') {\n        acc.push(index);\n        if (filteredPageManipulationOverlayItems[index + 1]?.type === 'divider') {\n          acc.push(index + 1);\n        }\n      }\n      if (dataElement === 'pageManipulationControls') {\n        acc.push(index);\n        if (filteredPageManipulationOverlayItems[index - 1]?.type === 'divider') {\n          acc.push(index - 1);\n        }\n      }\n      return acc;\n    }, []);\n    filteredPageManipulationOverlayItems = filteredPageManipulationOverlayItems.filter((_, index) => !removedIndices.includes(index));\n  }\n\n  useEffect(() => {\n    core.addEventListener('documentLoaded', closeOverlay);\n    return () => {\n      core.removeEventListener('documentLoaded', closeOverlay);\n    };\n  }, []);\n\n  return (\n    <InitialPageManipulationOverlay pageNumbers={pageNumbers} pageManipulationOverlayItems={filteredPageManipulationOverlayItems}>\n      { !isXod && !isOffice && <PageAdditionalControls pageNumbers={pageNumbers} dataElement=\"pageAdditionalControls\" /> }\n      <PageRotationControls pageNumbers={pageNumbers} dataElement=\"pageRotationControls\" />\n      { !isXod && !isOffice && <PageManipulationControls pageNumbers={pageNumbers} dataElement=\"pageManipulationControls\" /> }\n    </InitialPageManipulationOverlay>\n  );\n}\n\nexport default PageManipulationOverlay;", "import React from 'react';\nimport { shallowEqual, useSelector } from 'react-redux';\nimport PageManipulationOverlay from './PageManipulationOverlay';\nimport FlyoutMenu from '../FlyoutMenu/FlyoutMenu';\nimport selectors from 'selectors';\nimport DataElements from 'constants/dataElement';\nimport { useTranslation } from 'react-i18next';\n\nfunction PageManipulationOverlayContainer() {\n  const [\n    selectedPageIndexes,\n    currentPage,\n    pageManipulationOverlayItems,\n  ] = useSelector((state) => [\n    selectors.getSelectedThumbnailPageIndexes(state),\n    selectors.getCurrentPage(state),\n    selectors.getPageManipulationOverlayItems(state),\n  ], shallowEqual);\n\n  // If we start drilling this prop, maybe create a context\n  const pageNumbers = selectedPageIndexes.length > 0 ? selectedPageIndexes.map((i) => i + 1) : [currentPage];\n\n  const [t] = useTranslation();\n\n  return (\n    <FlyoutMenu\n      menu={DataElements.PAGE_MANIPULATION_OVERLAY}\n      trigger={DataElements.PAGE_MANIPULATION_OVERLAY_BUTTON}\n      ariaLabel={t('option.thumbnailPanel.moreOptionsMenu')}\n    >\n      <span className=\"visually-hidden\">\n        <p aria-live=\"assertive\" role=\"alert\"> {t('option.thumbnailPanel.moreOptionsMenu')}</p>\n      </span>\n      <PageManipulationOverlay\n        pageNumbers={pageNumbers}\n        pageManipulationOverlayItems={pageManipulationOverlayItems}\n      />\n    </ FlyoutMenu>\n  );\n}\n\nexport default PageManipulationOverlayContainer;", "import PageManipulationOverlay from './PageManipulationOverlayContainer';\n\nexport default PageManipulationOverlay;"], "sourceRoot": ""}