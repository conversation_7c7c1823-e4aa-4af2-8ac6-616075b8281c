using System.Collections;
using System.Collections.Concurrent;
using System.Data.Common;
using System.Diagnostics.CodeAnalysis;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using Elastic.Clients.Elasticsearch;
using FluentMigrator;
using Levelbuild.Core.DataStoreInterface;
using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.DataStoreInterface.Dto.Interfaces;
using Levelbuild.Core.DataStoreInterface.Enum;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.DataStoreInterface.Filter;
using Levelbuild.Core.DataStoreInterface.Filter.FilterTypes;
using Levelbuild.Core.FileInterface.Exception;
using Levelbuild.Core.StorageInterface;
using Levelbuild.Core.StorageInterface.Constants;
using Levelbuild.Core.StorageInterface.Dto;
using Levelbuild.Core.StorageInterface.Dto.SearchSuggestions;
using Levelbuild.Domain.BasicFileTextExtractions;
using Levelbuild.Domain.Storage.Db;
using Levelbuild.Domain.Storage.Db.Postgres;
using Levelbuild.Domain.Storage.Helper;
using Levelbuild.Domain.StorageEntities;
using Levelbuild.Domain.StorageEntities.Features.Dto;
using Levelbuild.Domain.StorageEntities.Features.Enum;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Internal;
using Microsoft.EntityFrameworkCore.Storage;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using Serilog;
using Serilog.Context;
using Serilog.Core;
using Serilog.Core.Enrichers;
using SqlKata;
using SqlKata.Execution;

namespace Levelbuild.Domain.Storage;

public class StorageConnection : IStorageConnection
{
	private const string DefaultLanguage = "en";
	private readonly bool _isMainConnection;
	public readonly Db.Db Db;
	private IRevisionHelper? _revisionHelper;
	private readonly ILogger _logger;
	private readonly MigrationHelper _migrationHelper;
	private readonly StorageConnectionFactory _storageConnectionFactory;
	private readonly HashSet<string> _failedMigrationTables;
	private readonly IDisposable logContext;
	private DateTime _dateTimeToCacheFor = DateTime.UtcNow;
	private const int CacheTimeSeconds = 120;
	public int RetryTimeout { get; set; } = 5000;

	public IFileStoreHelper? FileStore { get; set; }

	private static readonly ConcurrentDictionary<string, DateTime> TempFileDeletionCache = new();

	public PostgresMigrator Migrator { get; init; }

	public PostgresDataHelper DataHelper { get; init; }

	public PostgresConnectionHelper ConnectionHelper { get; init; }

	public StorageConnection(FeatureFlags featureFlags, Db.Db db, MigrationHelper migrationHelper, ILogger logger,
							 string connectionString, StorageConnectionFactory storageConnectionFactory, HashSet<string> failedMigrationTables)
	{
		_storageConnectionFactory = storageConnectionFactory;
		_migrationHelper = migrationHelper;
		_logger = logger;
		_failedMigrationTables = failedMigrationTables;
		Db = db;

		ConnectionHelper = new PostgresConnectionHelper(connectionString, logger);
		DataHelper = new PostgresDataHelper(db, failedMigrationTables, logger);
		Migrator = new PostgresMigrator(db, logger);

		if (Db.CustomerContext != null)
		{
			FileStore = new FileStoreHelper(featureFlags, Db.CustomerContext, _logger);

			if (string.IsNullOrWhiteSpace(Db.CustomerContext.MongoDbConnectionString))
				_revisionHelper = new RevisionHelper(this, Db, Db.CustomerContext, FileStore, _logger);
			else
				_revisionHelper = new MongoDbRevisionHelper(this, Db, FileStore, _logger);
		}
		else
		{
			_isMainConnection = true;
		}

		ILogEventEnricher[] enrichers =
		[
			new PropertyEnricher("CurrentDatabase", db.ConnectionHelper.GetDatabaseName())
		];
		logContext = LogContext.Push(enrichers);
	}

	/// <summary>
	/// execute function with StorageDatabaseContext
	/// </summary>
	/// <param name="func">The function to execute</param>
	/// <typeparam name="T">The return type</typeparam>
	/// <returns></returns>
	public T WithDbContext<T>(Func<StorageDatabaseContext, T> func)
	{
		try
		{
			using var context = Db.GetEfCoreDbContext();
			return func(context);
		}
		catch (InvalidOperationException ex)
		{
			if (ex.Message.Contains("ManyServiceProvidersCreatedWarning"))
			{
				Log.Error(ex.Message);
				PrintDbStuff();
			}

			throw;
		}
	}

	/// <inheritdoc cref="WithDbContextTransaction"/>
	public void WithDbContextTransaction(Action<StorageDatabaseContext, IDbContextTransaction, IServiceScope> func)
	{
		WithDbContextTransaction((db, transaction, scope) =>
		{
			func(db, transaction, scope);
			return 0;
		});
	}

	/// <summary>
	/// execute function with StorageDatabaseContext
	/// </summary>
	/// <param name="func">The function to execute</param>
	/// <typeparam name="T">The return type</typeparam>
	/// <returns></returns>
	public T WithDbContextTransaction<T>(Func<StorageDatabaseContext, IDbContextTransaction, IServiceScope, T> func)
	{
		try
		{
			using var context = Db.GetEfCoreDbContext();
			using var transaction = context.Database.BeginTransaction();
			using var serviceProvider = Migrator.CreateMigrateServices(context, transaction);
			using var scope = serviceProvider.CreateScope();
			var t = func(context, transaction, scope);
			transaction.Commit();
			return t;
		}
		catch (InvalidOperationException ex)
		{
			if (ex.Message.Contains("ManyServiceProvidersCreatedWarning"))
			{
				Log.Error(ex.Message);
				PrintDbStuff();
			}

			throw;
		}
	}

	private void PrintDbStuff()
	{
		#pragma warning disable EF1001
		Type type = typeof(ServiceProviderCache);
		#pragma warning restore EF1001

		FieldInfo fieldInfo = type.GetField("_configurations", BindingFlags.NonPublic | BindingFlags.Instance)!;

		#pragma warning disable EF1001
		var value = fieldInfo.GetValue(ServiceProviderCache.Instance) as
						#pragma warning restore EF1001
						ConcurrentDictionary<IDbContextOptions, (IServiceProvider ServiceProvider, IDictionary<string, string> DebugInfo)>;
		Log.Logger.Error($"ConcurrentDictionary value count: {value!.Count}");
		foreach (var keyValuePair in value)
		{
			var connectionString = keyValuePair.Key.Extensions
				.OfType<RelationalOptionsExtension>()
				.FirstOrDefault()?.ConnectionString;
			Log.Logger.Error(keyValuePair.Key + ": " + string.Join("; ", keyValuePair.Value.DebugInfo) + " :: " + connectionString);
		}
	}

	#region Interface

	/// <summary>
	/// Dispose database connections
	/// </summary>
	public void Dispose()
	{
		logContext.Dispose();
	}

	[ExcludeFromCodeCoverage]
	IDataStoreContext IDataStoreConnection.GetContext() => GetContext();

	[ExcludeFromCodeCoverage]
	StorageContext IStorageConnection.CreateContext(StorageContext config) => CreateContext(config);

	[ExcludeFromCodeCoverage]
	StorageContext IStorageConnection.UpdateContext(StorageContext config) => UpdateContext(config);

	[ExcludeFromCodeCoverage]
	IList<StorageSuggestionElement> IStorageConnection.GetSuggestions(
		StorageSuggestionQuery storageSuggestionQuery) =>
		GetSuggestions(storageSuggestionQuery);

	[ExcludeFromCodeCoverage]
	void IStorageConnection.RemoveContext(string contextName, bool forceDeleteCustomerDb) => RemoveContext(contextName, forceDeleteCustomerDb);

	[ExcludeFromCodeCoverage]
	bool IDataStoreConnection.IsGetElementAllowed(
		string dataSourceName,
		string elementId) =>
		IsGetElementAllowed(dataSourceName, elementId);

	[ExcludeFromCodeCoverage]
	Task<bool> IDataStoreConnection.IsGetElementAllowedAsync(string dataSourceName, string elementId) =>
		IsGetElementAllowedAsync(dataSourceName, elementId);

	[ExcludeFromCodeCoverage]
	DataStoreElement? IDataStoreConnection.GetElement(
		string dataSourceName,
		string elementId,
		IList<DataStoreQueryField>? lookupFields) =>
		GetElement(dataSourceName, elementId, lookupFields);

	[ExcludeFromCodeCoverage]
	Task<DataStoreElement?> IDataStoreConnection.GetElementAsync(string dataSourceName, string elementId, IList<DataStoreQueryField>? lookupFields) =>
		GetElementAsync(dataSourceName, elementId, lookupFields);

	[ExcludeFromCodeCoverage]
	DataStoreSuccessInfo IDataStoreConnection.CreateElement(string dataSourceName, DataStoreElementData elementData, DataStoreOperationOrigin origin,
															IList<string>? matchBy,
															DataStoreMatchBehaviour? matchBehaviour,
															IList<DataStoreQueryField>? lookupFields) =>
		CreateElement(dataSourceName, elementData, origin, matchBy, matchBehaviour, lookupFields);

	Task<DataStoreSuccessInfo> IDataStoreConnection.CreateElementAsync(string dataSourceName, DataStoreElementData elementData, DataStoreOperationOrigin origin,
																	   IList<string>? matchBy,
																	   DataStoreMatchBehaviour? matchBehaviour,
																	   IList<DataStoreQueryField>? lookupFields)
		=> CreateElementAsync(dataSourceName, elementData, origin, matchBy, matchBehaviour, lookupFields);

	[ExcludeFromCodeCoverage]
	public IList<DataStoreSuccessInfo> CreateElements(string dataSourceName, IList<DataStoreElementData> elementDatas, DataStoreOperationOrigin origin,
													  IList<string>? matchBy,
													  DataStoreMatchBehaviour? matchBehaviour) => CreateElements(dataSourceName, elementDatas, origin);

	Task<IList<DataStoreSuccessInfo>> IDataStoreConnection.CreateElementsAsync(string dataSourceName, IList<DataStoreElementData> elementDatas,
																			   DataStoreOperationOrigin origin, IList<string>? matchBy,
																			   DataStoreMatchBehaviour? matchBehaviour)
		=> CreateElementsAsync(dataSourceName, elementDatas, origin, matchBy, matchBehaviour);

	[ExcludeFromCodeCoverage]
	DataStoreSuccessInfo IDataStoreConnection.UpdateElement(
		string dataSourceName,
		DataStoreElementData elementData,
		DataStoreOperationOrigin origin, IList<DataStoreQueryField>? lookupFields) => UpdateElement(dataSourceName, elementData, origin, lookupFields);

	Task<DataStoreSuccessInfo> IDataStoreConnection.UpdateElementAsync(string dataSourceName, DataStoreElementData elementData, DataStoreOperationOrigin origin,
																	   IList<DataStoreQueryField>? lookupFields)
		=> UpdateElementAsync(dataSourceName, elementData, origin, lookupFields);

	DataStoreSuccessInfo IDataStoreConnection.SetInactive(string dataSourceName, string elementId)
		=> SetInactive(dataSourceName, elementId);

	Task<DataStoreSuccessInfo> IDataStoreConnection.SetInactiveAsync(string dataSourceName, string elementId)
		=> SetInactiveAsync(dataSourceName, elementId);

	DataStoreSuccessInfo IDataStoreConnection.SetActive(string dataSourceName, string elementId)
		=> SetActive(dataSourceName, elementId);

	Task<DataStoreSuccessInfo> IDataStoreConnection.SetActiveAsync(string dataSourceName, string elementId)
		=> SetActiveAsync(dataSourceName, elementId);

	DataStoreSuccessInfo IDataStoreConnection.AddFavourite(string dataSourceName, string elementId)
		=> AddFavourite(dataSourceName, elementId);

	Task<DataStoreSuccessInfo> IDataStoreConnection.AddFavouriteAsync(string dataSourceName, string elementId)
		=> AddFavouriteAsync(dataSourceName, elementId);

	DataStoreSuccessInfo IDataStoreConnection.RemoveFavourite(string dataSourceName, string elementId)
		=> RemoveFavourite(dataSourceName, elementId);

	Task<DataStoreSuccessInfo> IDataStoreConnection.RemoveFavouriteAsync(string dataSourceName, string elementId)
		=> RemoveFavouriteAsync(dataSourceName, elementId);

	[ExcludeFromCodeCoverage]
	DataStoreSuccessInfo IDataStoreConnection.DeleteElement(string dataSourceName, string elementId,
															DataStoreOperationOrigin origin) => DeleteElement(dataSourceName, elementId, origin);

	Task<DataStoreSuccessInfo> IDataStoreConnection.DeleteElementAsync(string dataSourceName, string elementId, DataStoreOperationOrigin origin)
		=> DeleteElementAsync(dataSourceName, elementId, origin);

	[ExcludeFromCodeCoverage]
	bool IDataStoreConnection.IsGetFileAllowed(string definitionName, string fileId) =>
		IsGetFileAllowed(definitionName, fileId);

	[ExcludeFromCodeCoverage]
	Task<bool> IDataStoreConnection.IsGetFileAllowedAsync(string definitionName, string fileId) =>
		IsGetFileAllowedAsync(definitionName, fileId);

	[ExcludeFromCodeCoverage]
	DataStoreFileStream? IDataStoreConnection.GetFile(string definitionName, string fileId) =>
		GetFile(definitionName, fileId);

	Task<DataStoreFileStream?> IDataStoreConnection.GetFileAsync(string definitionName, string fileId) =>
		GetFileAsync(definitionName, fileId);

	[ExcludeFromCodeCoverage]
	string IDataStoreConnection.UploadFile(string definitionName, DataStoreFileStream fileStream) => UploadFile(definitionName, fileStream);

	Task<string> IDataStoreConnection.UploadFileAsync(string definitionName, DataStoreFileStream fileStream) => UploadFileAsync(definitionName, fileStream);


	[ExcludeFromCodeCoverage]
	DataStoreSuccessInfo IDataStoreConnection.UpdateFile(string definitionName, string elementId, string fileUploadId,
														 DataStoreOperationOrigin origin) => UpdateFile(definitionName, elementId, fileUploadId, origin);


	Task<DataStoreSuccessInfo> IDataStoreConnection.UpdateFileAsync(string definitionName, string elementId, string fileUploadId,
																	DataStoreOperationOrigin origin)
		=> UpdateFileAsync(definitionName, elementId, fileUploadId, origin);

	[ExcludeFromCodeCoverage]
	DataStoreSuccessInfo IDataStoreConnection.RemoveFile(string definitionName, string elementId,
														 DataStoreOperationOrigin origin) => RemoveFile(definitionName, elementId, origin);

	Task<DataStoreSuccessInfo> IDataStoreConnection.RemoveFileAsync(string definitionName, string elementId, DataStoreOperationOrigin origin)
		=> RemoveFileAsync(definitionName, elementId, origin);

	[ExcludeFromCodeCoverage]
	DataStoreSuccessInfo IDataStoreConnection.UpdateFileLink(string dataSourceName, string elementId, DataStoreReference fileLink,
															 DataStoreOperationOrigin origin) =>
		UpdateFileLink(dataSourceName, elementId, fileLink, origin);

	Task<DataStoreSuccessInfo> IDataStoreConnection.UpdateFileLinkAsync(string dataSourceName, string elementId, DataStoreReference fileLink,
																		DataStoreOperationOrigin origin)
		=> UpdateFileLinkAsync(dataSourceName, elementId, fileLink, origin);

	[ExcludeFromCodeCoverage]
	DataStoreSuccessInfo IDataStoreConnection.RemoveFileLink(string dataSourceName, string elementId, DataStoreOperationOrigin origin) =>
		RemoveFileLink(dataSourceName, elementId, origin);

	Task<DataStoreSuccessInfo> IDataStoreConnection.RemoveFileLinkAsync(string dataSourceName, string elementId, DataStoreOperationOrigin origin) =>
		RemoveFileLinkAsync(dataSourceName, elementId, origin);

	[ExcludeFromCodeCoverage]
	DataStoreResultSet<DataStoreElement> IDataStoreConnection.GetElements(DataStoreQuery query) => GetElements(query);

	Task<DataStoreResultSet<DataStoreElement>> IDataStoreConnection.GetElementsAsync(DataStoreQuery query) => GetElementsAsync(query);

	[ExcludeFromCodeCoverage]
	DataStoreResultSet<DataStoreElement> IStorageConnection.GetElements(StorageQuery query) => GetElements(query);

	[ExcludeFromCodeCoverage]
	DataStoreResultSet<DataStoreRevisionInfo> IDataStoreConnection.GetRevisions(DataStoreRevisionQuery query) => GetRevisions(query);

	Task<DataStoreResultSet<DataStoreRevisionInfo>> IDataStoreConnection.GetRevisionsAsync(DataStoreRevisionQuery query) => GetRevisionsAsync(query);


	DataStoreResultSet<DataStoreRevisionInfo> IDataStoreConnection.GetRevisionsByField(string dataSourceName,
																					   string elementId, string fieldName)
		=> GetRevisionsByField(dataSourceName, elementId, fieldName);

	Task<DataStoreResultSet<DataStoreRevisionInfo>> IDataStoreConnection.GetRevisionsByFieldAsync(string dataSourceName, string elementId, string fieldName)
		=> GetRevisionsByFieldAsync(dataSourceName, elementId, fieldName);

	[ExcludeFromCodeCoverage]
	DataStoreRevisionData IDataStoreConnection.GetRevision(string dataSourceName, string elementId, string revisionId) =>
		GetRevision(dataSourceName, elementId, revisionId);

	Task<DataStoreRevisionData?> IDataStoreConnection.GetRevisionAsync(string dataSourceName, string elementId, string revisionId) =>
		GetRevisionAsync(dataSourceName, elementId, revisionId);

	[ExcludeFromCodeCoverage]
	IList<string> IDataStoreConnection.GetDataSources() => GetDataSources();

	[ExcludeFromCodeCoverage]
	IDataStoreDataSource? IDataStoreConnection.GetDataSource(string name) => GetDataSource(name, false);

	[ExcludeFromCodeCoverage]
	StorageDataSource IStorageConnection.CreateDataSource(StorageDataSourceConfig dataSource) =>
		CreateDataSource(dataSource);

	[ExcludeFromCodeCoverage]
	StorageDataSource IStorageConnection.UpdateDataSource(StorageDataSourceConfig dataSource) =>
		UpdateDataSource(dataSource);

	[ExcludeFromCodeCoverage]
	void IStorageConnection.RemoveDataSource(string dataSourceName) => RemoveDataSource(dataSourceName);

	[ExcludeFromCodeCoverage]
	StorageField IStorageConnection.CreateField(string dataSourceName, StorageFieldConfig fieldConfig) =>
		CreateField(dataSourceName, fieldConfig);

	[ExcludeFromCodeCoverage]
	StorageField IStorageConnection.UpdateField(string dataSourceName, StorageFieldConfig fieldConfig) =>
		UpdateField(dataSourceName, fieldConfig);

	[ExcludeFromCodeCoverage]
	StorageField IStorageConnection.RenameField(string dataSourceName, string fieldName, string fieldNameNew) =>
		RenameField(dataSourceName, fieldName, fieldNameNew);

	[ExcludeFromCodeCoverage]
	void IStorageConnection.RemoveField(string dataSourceName, string fieldName) =>
		RemoveField(dataSourceName, fieldName);

	[ExcludeFromCodeCoverage]
	void IDisposable.Dispose() => Dispose();

	#endregion

	#region Impl

	/// <summary>
	/// 
	/// </summary>
	/// <returns></returns>
	public List<IDataStoreContext> GetContexts()
	{
		if (!_isMainConnection)
			throw new InsufficientRightsException(
				"You need to use a different storage connection with administrative privileges. Storage connection should be created without customer context.");

		return WithDbContext(db => db.StorageContext.ToList()).Select(it => (IDataStoreContext)it.ToDto()).ToList();
	}

	/// <summary>
	/// Get context object for name
	/// </summary>
	/// <param name="contextName"></param>
	/// <returns></returns>
	public StorageContext GetContext(string contextName)
	{
		if (!_isMainConnection && (Db.CustomerContext == null || Db.CustomerContext.Identifier == contextName))
			throw new InsufficientRightsException(
				"You need to use a different storage connection with administrative privileges. Storage connection should be created without customer context.");

		_logger.Information($"GetContext '{contextName}'...");

		return WithDbContext(db => db.StorageContext.First(it => it.Identifier == contextName)).ToDto();
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="storageContext"></param>
	/// <returns></returns>
	public StorageContext CreateContext(StorageContext storageContext)
	{
		if (!_isMainConnection)
			throw new InsufficientRightsException(
				"You need to use a different storage connection with administrative privileges. Storage connection should be created without customer context.");

		if (WithDbContext(db => db.StorageContext.FirstOrDefault(it => it.Identifier == storageContext.Identifier)) != null)
		{
			throw new DataStoreOperationException($"Context '{storageContext.Identifier}' already exists");
		}

		StorageContextOrm storageContextOrm = new StorageContextOrm(storageContext);
		CheckConnectionDuplicate(storageContextOrm);

		try
		{
			_migrationHelper.CreateDatabaseAndMigrateStorageTables(storageContextOrm, out _);

			using var customerConnection = _storageConnectionFactory.GetConnection(storageContextOrm);
			_migrationHelper.MigrateInitialTables(this, customerConnection);
			WithDbContext(db =>
			{
				db.StorageContext.Add(storageContextOrm);
				return db.SaveChanges();
			});
		}
		catch (DataStoreOperationException)
		{
			throw;
		}
		catch (Exception e)
		{
			_logger.Error(e, "Error while creating Database");
			try
			{
				_migrationHelper.DeleteCustomerDatabase(storageContextOrm);
			}
			catch (Exception)
			{
				// ignored
			}

			throw new DataStoreOperationException($"Error while migrating new Context: '{storageContextOrm.Identifier}'", e);
		}

		return storageContextOrm.ToDto();
	}

	/// <summary>
	/// checks for connection duplicate to prevent context to access other customers database
	/// </summary>
	/// <param name="storageContext"></param>
	/// <exception cref="DataStoreConfigurationException"></exception>
	private void CheckConnectionDuplicate(StorageContextOrm storageContext)
	{
		string newUrl = "";
		string mainUrl = "";
		DbConnectionStringBuilder newDbConnectionStringBuilder = new DbConnectionStringBuilder();
		newDbConnectionStringBuilder.ConnectionString = storageContext.DatabaseConnectionString;

		DbConnectionStringBuilder existingMainDbConnectionStringBuilder = new DbConnectionStringBuilder();
		existingMainDbConnectionStringBuilder.ConnectionString = Db.ConnectionString;

		string newDatabase = (string)(newDbConnectionStringBuilder["Database"] != null
										  ? newDbConnectionStringBuilder["Database"]
										  : newDbConnectionStringBuilder["database"]);

		string mainDatabase = (string)(existingMainDbConnectionStringBuilder["Database"] != null
										   ? existingMainDbConnectionStringBuilder["Database"]
										   : existingMainDbConnectionStringBuilder["database"]);

		if (storageContext.DatabaseConnectionString.ToLower().Contains("server="))
		{
			mainUrl = (string)(existingMainDbConnectionStringBuilder["Server"] != null
								   ? existingMainDbConnectionStringBuilder["Server"]
								   : existingMainDbConnectionStringBuilder["server"]);
			newUrl = (string)(newDbConnectionStringBuilder["Server"] != null ? newDbConnectionStringBuilder["Server"] : newDbConnectionStringBuilder["server"]);

			if (mainUrl.ToLower().Equals(newUrl.ToLower()) && mainDatabase.ToLower().Equals(newDatabase.ToLower()))
				throw new DataStoreConfigurationException($"Skipped context creation: " +
														  $"Already existing connection preferences for new context '{storageContext.Identifier}'" +
														  $" in main database!");
		}
		else if (storageContext.DatabaseConnectionString.ToLower().Contains("host="))
		{
			mainUrl = (string)(existingMainDbConnectionStringBuilder["Host"] != null
								   ? existingMainDbConnectionStringBuilder["Host"]
								   : existingMainDbConnectionStringBuilder["Host"]);
			newUrl = (string)(newDbConnectionStringBuilder["Host"] != null ? newDbConnectionStringBuilder["Host"] : newDbConnectionStringBuilder["host"]);

			if (mainUrl.ToLower().Equals(newUrl.ToLower()) && mainDatabase.ToLower().Equals(newDatabase.ToLower()))
				throw new DataStoreConfigurationException($"Skipped context creation: " +
														  $"Already existing connection preferences for new context '{storageContext.Identifier}'" +
														  $" in main database!");
		}

		foreach (var dataStoreContext in WithDbContext(db => db.StorageContext.ToList()).ToList())
		{
			var existingContext = (StorageContextOrm)dataStoreContext;
			DbConnectionStringBuilder existingDbConnectionStringBuilder = new DbConnectionStringBuilder();
			existingDbConnectionStringBuilder.ConnectionString = existingContext.DatabaseConnectionString;

			string url, database;
			if (storageContext.DatabaseConnectionString.ToLower().Contains("server="))
			{
				if (!existingContext.DatabaseConnectionString.ToLower().Contains("server="))
					continue;

				url = (string)(existingDbConnectionStringBuilder["Server"] != null
								   ? existingDbConnectionStringBuilder["Server"]
								   : existingDbConnectionStringBuilder["server"]);
				database = (string)(existingDbConnectionStringBuilder["Database"] != null
										? existingDbConnectionStringBuilder["Database"]
										: existingDbConnectionStringBuilder["database"]);

				if (url.ToLower().Equals(newUrl.ToLower()) && database.ToLower().Equals(newDatabase.ToLower()))
					throw new DataStoreConfigurationException($"Skipped context creation: " +
															  $"Already existing connection preferences for new context '{storageContext.Identifier}'" +
															  $" in existing context  '{existingContext.Identifier}'");
			}
			else if (storageContext.DatabaseConnectionString.ToLower().Contains("host="))
			{
				if (!existingContext.DatabaseConnectionString.ToLower().Contains("host="))
					continue;

				url = (string)(existingDbConnectionStringBuilder["Host"] != null
								   ? existingDbConnectionStringBuilder["Host"]
								   : existingDbConnectionStringBuilder["host"]);
				database = (string)(existingDbConnectionStringBuilder["Database"] != null
										? existingDbConnectionStringBuilder["Database"]
										: existingDbConnectionStringBuilder["database"]);

				if (url.ToLower().Equals(newUrl.ToLower()) && database.ToLower().Equals(newDatabase.ToLower()))
					throw new DataStoreConfigurationException($"Skipped context creation: " +
															  $"Already existing connection preferences for new context '{storageContext.Identifier}'" +
															  $" in existing context  '{existingContext.Identifier}'");
			}
		}
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="storageContext"></param>
	/// <returns></returns>
	public StorageContext UpdateContext(StorageContext storageContext)
	{
		if (!_isMainConnection)
			throw new InsufficientRightsException(
				"You need to use a different storage connection with administrative privileges. Storage connection should be created without customer context.");

		_logger.Information($"UpdateContext .");

		var oldContext = WithDbContext(db => db.StorageContext.FirstOrDefault(it => it.Identifier == storageContext.Identifier));

		if (oldContext == null)
			throw new DataStoreOperationException("No existing context for given identifier " + storageContext.Identifier);

		StorageContextOrm storageContextOrm = oldContext;

		var language = storageContextOrm.Language;
		var newLanguage = (string)storageContext.Config[StorageConfigurationConstants.Language];
		var languageChanged = newLanguage != storageContextOrm.Language;
		if (languageChanged)
		{
			try
			{
				language = Db.ConnectionHelper.ValidateLanguage(newLanguage);
			}
			catch (KeyNotFoundException knfe)
			{
				throw new ContextConfigurationException("Language could not be found", knfe);
			}
		}

		storageContextOrm.FromConfig(storageContext.Config!);
		storageContextOrm.Language = language;

		WithDbContextTransaction((db, transaction, scope) =>
		{
			db.StorageContext.Update(storageContextOrm);
			db.SaveChanges();
		});

		if (languageChanged)
		{
			foreach (var dataSourceName in GetDataSources())
			{
				var dataSource = GetIndexDefinition(dataSourceName);
				if (dataSource != null && dataSource.FulltextSearch)
				{
					WithDbContextTransaction((db, transaction, scope) =>
					{
						Db.Migrator.RemoveFulltextSearch(dataSource, scope);
						Db.Migrator.AddFulltextSearch(dataSource, dataSource.Fields, scope);
					});
					var task = Fullsync(dataSource);
					task.ConfigureAwait(false);
					task.Wait(TimeSpan.FromHours(1));
				}
			}
		}

		Storage.ResetConnection(storageContextOrm.DatabaseConnectionString);
		return storageContextOrm.ToDto();
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="contextName"></param>
	/// <param name="forceDeleteCustomerDb">if the database should be deleted</param>
	/// <returns></returns>
	public bool RemoveContext(string contextName, bool forceDeleteCustomerDb = false)
	{
		if (!_isMainConnection)
			throw new InsufficientRightsException(
				"You need to use a different storage connection with administrative privileges. Storage connection should be created without customer context.");

		_logger.Information($"removeContext {contextName}.");

		var storageContextOrm = WithDbContext(db => db.StorageContext.FirstOrDefault(it => it.Identifier == contextName));

		if (storageContextOrm == null)
			throw new ContextNotFoundException($"Context '{contextName}' does not exists");

		WithDbContext(db =>
		{
			db.StorageContext.Remove(storageContextOrm);
			return db.SaveChanges();
		});

		if (forceDeleteCustomerDb && (Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Development" ||
									  Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Ci" ||
									  // unit tests
									  Environment.GetEnvironmentVariable("DOTNET_ENVIRONMENT") == "Development"))
		{
			_migrationHelper.DeleteCustomerDatabase(storageContextOrm);
		}

		return true;
	}


	/// <summary>
	/// 
	/// </summary>
	/// <returns></returns>
	public StorageContext GetContext()
	{
		return Db.CustomerContext!.ToDto();
	}

	public IList<StorageSuggestionElement> GetSuggestions(StorageSuggestionQuery storageSuggestionQuery)
	{
		if (_isMainConnection)
			throw new DataStoreConnectionException("Cannot get suggestions on main database without context!");

		if (_failedMigrationTables.Contains(storageSuggestionQuery.DataSourceName))
			throw new DataStoreQueryException($"A migration for table {storageSuggestionQuery.DataSourceName} failed. Table can currently not be used.");

		return new FilterParserElastic(Db, _logger).ParseQuery(GetIndexDefinition(storageSuggestionQuery.DataSourceName)!, storageSuggestionQuery);
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="dataSourceName"></param>
	/// <param name="elementId"></param>
	/// <exception cref="DataStoreConnectionException"></exception>
	/// <exception cref="DataSourceNotFoundException"></exception>
	public bool IsGetElementAllowed(string dataSourceName, string elementId)
	{
		if (_isMainConnection)
			throw new DataStoreConnectionException("Cannot get element on main database without context!");

		StorageIndexDefinition? storageIndexDefinition = WithDbContext(
			db => db.StorageIndexDefinition.Include(it => it.Fields)
				.FirstOrDefault(it => it.Name == dataSourceName));

		if (storageIndexDefinition == null)
			throw new DataSourceNotFoundException($"Could not find DataSource for name {dataSourceName}");
		
		if (!Guid.TryParse(elementId, out var uuid))
			return false;

		var values = Db.DataHelper.GetById(storageIndexDefinition, elementId, Db.Authentication?.Language ?? "en", GetIndexDefinition, null,
										   false, true);
		return values != null && values.Count != 0;
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="dataSourceName"></param>
	/// <param name="elementId"></param>
	/// <returns></returns>
	public Task<bool> IsGetElementAllowedAsync(string dataSourceName, string elementId)
	{
		return Task.Run(() => IsGetElementAllowed(dataSourceName, elementId));
	}

	/// <summary>
	/// Get data store element of data source by element id
	/// </summary>
	/// <param name="dataSourceName"></param>
	/// <param name="elementId"></param>
	/// <param name="lookupFields"></param>
	/// <param name="withDeleted"></param>
	/// <returns></returns>
	/// <exception cref="DataStoreConnectionException"></exception>
	/// <exception cref="DataSourceNotFoundException"></exception>
	public DataStoreElement? GetElement(string dataSourceName, string elementId, IList<DataStoreQueryField>? lookupFields = null, bool withDeleted = false)
	{
		if (_isMainConnection)
			throw new DataStoreConnectionException("Cannot get element on main database without context!");

		StorageIndexDefinition? storageIndexDefinition = WithDbContext(
			db => db.StorageIndexDefinition.Include(it => it.Fields)
				.FirstOrDefault(it => it.Name == dataSourceName));

		if (storageIndexDefinition == null)
			throw new DataSourceNotFoundException($"Could not find DataSource for name {dataSourceName}");

		if (!Guid.TryParse(elementId, out var uuid))
			return null;
		
		var values = Db.DataHelper.GetById(storageIndexDefinition, elementId, Db.Authentication?.Language ?? "en", GetIndexDefinition, lookupFields,
										   withDeleted, true);
		if (values == null || values.Count == 0)
			return null;

		bool isFavourite = GetBoolForKey(values, StorageSystemField.SysIsFavourite.ToString());
		bool isInactive = GetBoolForKey(values, StorageSystemField.SysInactiveDate.ToString());
		values.Remove(StorageSystemField.SysIsFavourite.ToString());

		// create return object initially
		DataStoreElement dataStoreElement =
			new(elementId, values, values[StorageSystemField.SysGroups.ToString()]!.ToArray().Select(it => (string)it).ToList(),
				isInactive, isFavourite);
		dataStoreElement = AddFileInfo(dataStoreElement, storageIndexDefinition);

		return dataStoreElement;
	}


	/// <summary>
	///
	/// </summary>
	/// <param name="dataSourceName"></param>
	/// <param name="elementId"></param>
	/// <param name="lookupFields"></param>
	/// <param name="withDeleted"></param>
	/// <returns></returns>
	Task<DataStoreElement?> GetElementAsync(string dataSourceName, string elementId, IList<DataStoreQueryField>? lookupFields = null, bool withDeleted = false)
	{
		return Task.Run(() => { return GetElement(dataSourceName, elementId, lookupFields, withDeleted); })!;
	}


	/// <summary>
	/// add file info to DataStoreElement, if has file or links to file element
	/// </summary>
	/// <param name="dataStoreElement"></param>
	/// <param name="storageIndexDefinition"></param>
	/// <returns></returns>
	private DataStoreElement AddFileInfo(DataStoreElement dataStoreElement, StorageIndexDefinition storageIndexDefinition)
	{
		var elementId = dataStoreElement.Id;
		var values = dataStoreElement.Values;

		if (!storageIndexDefinition.IsView &&
			(values[StorageSystemField.SysFileId.ToString()] != null || values[StorageSystemField.SysIsLink.ToString()] != null))
		{
			string? fileId = (values[StorageSystemField.SysFileId.ToString()] != null) ? values[StorageSystemField.SysFileId.ToString()]!.ToString() : null;
			string? fileName = (values[StorageSystemField.SysFileName.ToString()] != null)
								   ? values[StorageSystemField.SysFileName.ToString()]!.ToString()
								   : null;
			string fileElementId = elementId;
			long fileSize = 0;

			// part for linked files ---------------------------------------------------------------------
			if (bool.Parse(values[StorageSystemField.SysIsLink.ToString()]!.ToString()!))
			{
				// get referenced data set and link data set
				var linkedElement = GetLinkedElement(storageIndexDefinition, elementId, out StorageLink? storageLink);
				if (linkedElement != null)
				{
					fileName = (string?)linkedElement.Values[StorageSystemField.SysFileName.ToString()];
					fileSize = Int64.Parse(linkedElement.Values[StorageSystemField.SysFileSize.ToString()]!.ToString()!);
					fileElementId = linkedElement.Id;

					// maybe get specific revision
					if (storageLink != null && storageLink.LinkedRevisionNumber != null)
						fileId = FileStore!.GetFileId(fileElementId, (long)storageLink.LinkedRevisionNumber, false);
					else
						fileId = linkedElement.Values[StorageSystemField.SysFileId.ToString()]!.ToString();
				}
			}

			// overwrite and add FileInfo if has file
			if (fileName != null)
			{
				// just fallback, if fileId was not (yet) set to db entry
				if (fileId == null)
				{
					if (!storageIndexDefinition.StoreRevisions)
					{
						fileId = FileStore!.GetFileId(fileElementId, 1, false);
					}
					else
					{
						Int64.TryParse(values[StorageSystemField.SysCurrentRevision.ToString()]?.ToString(), out var currentRevision);
						long revisionNumber = _revisionHelper!.GetCurrentRevisionNumberWithFile(storageIndexDefinition, elementId);
						if (revisionNumber > -1)
							currentRevision = revisionNumber;
						fileId = FileStore!.GetFileId(fileElementId, currentRevision, false);
					}

					values[StorageSystemField.SysFileId.ToString()] = fileId;
					// save FileId...
					_ = UpdateFileIdAtRead(storageIndexDefinition, elementId, fileId);
				}

				if (fileSize == 0)
					fileSize = Int64.Parse(values[StorageSystemField.SysFileSize.ToString()]!.ToString()!);

				DataStoreFileInfo dataStoreFileInfo = new DataStoreFileInfo(fileId, fileName, fileSize);
				dataStoreElement.FileInfo = dataStoreFileInfo;
			}
		}

		return dataStoreElement;
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="dataSourceName"></param>
	/// <param name="elementData"></param>
	/// <param name="origin"></param>
	/// <param name="matchBy"></param>
	/// <param name="matchBehaviour"></param>
	/// <param name="lookupFields"></param>
	/// <returns></returns>
	/// <exception cref="DataSourceNotFoundException"></exception>
	/// <exception cref="DataStoreOperationException"></exception>
	public DataStoreSuccessInfo CreateElement(
		string dataSourceName,
		DataStoreElementData elementData,
		DataStoreOperationOrigin origin,
		IList<string>? matchBy = null,
		DataStoreMatchBehaviour? matchBehaviour = DataStoreMatchBehaviour.Skip,
		IList<DataStoreQueryField>? lookupFields = null)
	{
		var createGuid = Guid.NewGuid();
		var currentContext = (Db.CustomerContext != null) ? Db.CustomerContext.Identifier : "[no context]";
		_logger.Information($"Create element {createGuid} for context {currentContext} ...");

		if (_isMainConnection)
			throw new DataStoreConnectionException("Cannot create element on main database without context!");

		StorageIndexDefinition? storageIndexDefinition = GetDefinitionWithPaths(dataSourceName);
		CheckDefinitionAndData(storageIndexDefinition, dataSourceName, elementData);

		if (storageIndexDefinition.IsView)
			throw new DataStoreQueryException("Cannot create element on a view!");

		StorageTempFile? storageTempFile = null;
		if (elementData.FileUploadId != null)
		{
			storageTempFile = GetTempFileEntry(elementData.FileUploadId);
			if (storageTempFile == null)
				throw new ElementNotFoundException($"Temp file {elementData.FileUploadId} for element not found!");
		}

		IDictionary<string, object?> values = elementData.Values.ToDictionary();
		var fields = storageIndexDefinition.Fields.ToDictionary(field => field.Name.ToLower());
		var unknownFields = values.Keys.Where(it => !fields.ContainsKey(it.ToLower())).ToList();
		if (!unknownFields.IsNullOrEmpty())
			throw new DataStoreOperationException($"Fields '{string.Join("', '", unknownFields)}' do not exist in table {storageIndexDefinition.Name}");

		IDictionary<string, object?> valuesCorrectKeys =
			values.Select(value => new KeyValuePair<string, object?>(fields[value.Key.ToLower()].Name, value.Value)).ToDictionary();
		values = PrepareValues(valuesCorrectKeys, storageIndexDefinition, storageTempFile, null, origin,
							   elementData.Groups, true);

		IDictionary<string, object?> newValues = new Dictionary<string, object?>();
		newValues = Db.ConnectionHelper.WithQueryFactory(it =>
		{
			// generate hash for change check
			string creationHash = GetHashForCreate(values, storageIndexDefinition.Fields);
			values[StorageSystemField.SysRevisionHash.ToString()] = creationHash;

			// InsertWithConnection
			newValues = Db.DataHelper.Insert(it, storageIndexDefinition, values, GetIndexDefinition, Db.Authentication?.Language ?? "en", lookupFields);
			var elementId = newValues[StorageSystemField.Id.ToString()]!.ToString()!;
			long newRevNumber = Int64.Parse(newValues[StorageSystemField.SysCurrentRevision.ToString()]!.ToString()!);

			if (elementData.FileUploadId != null)
			{
				storageTempFile!.ElementId = elementId;
				storageTempFile.StorageIndexDefinition = storageIndexDefinition;
				storageTempFile.RevisionNumber = newRevNumber;
				UpdateTempFileEntry(storageTempFile);
				newValues[StorageSystemField.SysFileId.ToString()] = FileStore!.GetFileId(elementId, newRevNumber, false);
			}

			if (storageIndexDefinition.StoreRevisions)
				_revisionHelper!.CreateRevision(it, storageIndexDefinition, elementId, newValues, origin, (elementData.FileUploadId != null),
												DataStoreOperationType.Create, newRevNumber);

			_logger.Information($"Element created: {createGuid} for context {currentContext}");
			return newValues;
		});

		var elementId = newValues[StorageSystemField.Id.ToString()]!.ToString()!;
		long newRevNumber = Int64.Parse(newValues[StorageSystemField.SysCurrentRevision.ToString()]!.ToString()!);

		// remove fake field SysIsFavourite
		newValues.Remove(StorageSystemField.SysIsFavourite.ToString());

		if (elementData.FileUploadId != null)
			_ = MoveTempFileToFinalDestinationAsync(storageIndexDefinition, newValues, storageTempFile!, elementId, newRevNumber, elementData.FileUploadId);

		DataStoreFileInfo? fileInfo = null;
		if (elementData.FileUploadId != null)
		{
			string newFileUploadId = FileStore!.GetFileId(elementId, newRevNumber, false);
			fileInfo = new DataStoreFileInfo(newFileUploadId, storageTempFile!.FileName, storageTempFile.FileSize);
		}

		DataStoreSuccessInfo dataStoreSuccessInfo = new DataStoreSuccessInfo(elementId, DataStoreOperationType.Create)
		{
			ElementData = new DataStoreElement(elementId, newValues,
											   newValues[StorageSystemField.SysGroups.ToString()]!.ToArray().Select(it => (string)it).ToList(),
											   false, false)
			{
				FileInfo = fileInfo
			}
		};

		_logger.Information($"Element {createGuid} for context {currentContext} created.");
		return dataStoreSuccessInfo;
	}

	private string GetHashForCreate(IDictionary<string, object?> values, List<StorageFieldDefinitionOrm> fields)
	{
		IDictionary<string, object?> newValues = new Dictionary<string, object?>();
		foreach (StorageFieldDefinitionOrm field in fields)
		{
			if (!field.SystemField)
			{
				if (values.TryGetValue(field.Name, out var value))
					newValues[field.Name] = value;
				else if (field.DefaultValue != null)
					newValues[field.Name] = field.DefaultValue;
			}
		}

		return GetMd5RevisionHash(newValues, fields);
	}


	/// <summary>
	/// 
	/// </summary>
	/// <param name="dataSourceName"></param>
	/// <param name="elementData"></param>
	/// <param name="origin"></param>
	/// <param name="matchBy"></param>
	/// <param name="matchBehaviour"></param>
	/// <param name="lookupFields"></param>
	/// <returns></returns>
	/// <exception cref="DataSourceNotFoundException"></exception>
	/// <exception cref="DataStoreOperationException"></exception>
	public Task<DataStoreSuccessInfo> CreateElementAsync(string dataSourceName, DataStoreElementData elementData, DataStoreOperationOrigin origin,
														 IList<string>? matchBy = null, DataStoreMatchBehaviour? matchBehaviour = DataStoreMatchBehaviour.Skip,
														 IList<DataStoreQueryField>? lookupFields = null)
	{
		return Task.Run(() => { return CreateElement(dataSourceName, elementData, origin, matchBy, matchBehaviour, lookupFields); });
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="dataSourceName"></param>
	/// <param name="elementDatas"></param>
	/// <param name="origin"></param>
	/// <returns></returns>
	public IList<DataStoreSuccessInfo> CreateElements(string dataSourceName, IList<DataStoreElementData> elementDatas, DataStoreOperationOrigin origin)
	{
		IList<DataStoreSuccessInfo> dataStoreSuccessInfos = new List<DataStoreSuccessInfo>();
		StorageIndexDefinition? storageIndexDefinition = GetDefinitionWithPaths(dataSourceName);

		if (_isMainConnection)
			throw new DataStoreConnectionException("Cannot create element on main database without context!");

		_logger.Information("Create Elements...");
		if (storageIndexDefinition == null)
			throw new DataSourceNotFoundException($"Could not find DataSource for name {dataSourceName}");

		IList<IDictionary<string, object?>> elementDictionaryList = new List<IDictionary<string, object?>>();
		var fields = storageIndexDefinition.Fields.ToDictionary(field => field.Name.ToLower());
		foreach (var elementData in elementDatas)
		{
			if (elementData.Values.Count != 0)
			{
				IDictionary<string, object?> values = elementData.Values.ToDictionary();
				var unknownFields = values.Keys.Where(it => !fields.ContainsKey(it.ToLower())).ToList();
				if (!unknownFields.IsNullOrEmpty())
					throw new DataStoreOperationException(
						$"Fields '{string.Join("', '", unknownFields)}' do not exist in table {storageIndexDefinition.Name}");

				var valuesCorrectKeys = values.Select(value =>
					{
						var field = fields[value.Key.ToLower()];
						return new KeyValuePair<string, object?>(field.Name, SqlDataHelper.ToType(value.Value, field));
					})
					.ToDictionary();

				// generate hash for change check
				string creationHash = GetHashForCreate(valuesCorrectKeys, storageIndexDefinition.Fields);
				valuesCorrectKeys[StorageSystemField.SysRevisionHash.ToString()] = creationHash;

				// copy values
				elementDictionaryList.Add(
					PrepareValues(valuesCorrectKeys, storageIndexDefinition, null, null, origin, elementData.Groups, true));
			}
		}

		if (Db.Type == SqlType.Postgres && elementDictionaryList.Count > 0)
		{
			IList<string> newElementIdList = Db.ConnectionHelper.WithTransaction(it => Db.DataHelper.InsertMulti(
																					 it, storageIndexDefinition, elementDictionaryList))
				.Select(id => id.ToString()).ToList();

			if (storageIndexDefinition.StoreRevisions)
				CreateRevisionsForElements(storageIndexDefinition, newElementIdList, origin);


			foreach (var newElementId in newElementIdList)
			{
				DataStoreSuccessInfo dataStoreSuccessInfo = new DataStoreSuccessInfo(newElementId, DataStoreOperationType.Create);
				dataStoreSuccessInfos.Add(dataStoreSuccessInfo);
			}
		}

		return dataStoreSuccessInfos;
	}

	private void CreateRevisionsForElements(StorageIndexDefinition storageIndexDefinition, IList<string> newElementIdList, DataStoreOperationOrigin origin)
	{
		var elements = GetElements(new DataStoreQuery(storageIndexDefinition.Name, null)
									   .WithFilter(new QueryFilterGroup()
													   .AddFilter(new InFilter(new QueryFilterField(StorageSystemField.Id.ToString()),
																			   newElementIdList.ToArray()))));

		Db.ConnectionHelper.WithTransaction(it =>
		{
			_revisionHelper!.CreateMultipleRevisionsAfterInsert(it, storageIndexDefinition, elements, origin, false, DataStoreOperationType.Create);
			return 0;
		});
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="dataSourceName"></param>
	/// <param name="elementDatas"></param>
	/// <param name="origin"></param>
	/// <param name="matchBy"></param>
	/// <param name="matchBehaviour"></param>
	/// <returns></returns>
	public Task<IList<DataStoreSuccessInfo>> CreateElementsAsync(string dataSourceName, IList<DataStoreElementData> elementDatas,
																 DataStoreOperationOrigin origin,
																 IList<string>? matchBy = null,
																 DataStoreMatchBehaviour? matchBehaviour = DataStoreMatchBehaviour.Skip)
	{
		return Task.Run(() => { return CreateElements(dataSourceName, elementDatas, origin, matchBy, matchBehaviour); });
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="dataSourceName"></param>
	/// <param name="elementData"></param>
	/// <param name="update"></param>
	/// <exception cref="InsufficientRightsException"></exception>
	/// <exception cref="ElementNotFoundException">If the ElementId is null</exception>
	/// <exception cref="DataSourceNotFoundException"></exception>
	/// <exception cref="DataStoreOperationException"></exception>
	private void CheckDefinitionAndData([NotNull] StorageIndexDefinition? storageIndexDefinition, String dataSourceName, DataStoreElementData elementData,
										bool update = false)
	{
		if (storageIndexDefinition == null)
			throw new DataSourceNotFoundException($"Could not find DataSource for name {dataSourceName}");

		if (_failedMigrationTables.Contains(storageIndexDefinition.Name))
			throw new DataStoreOperationException($"DataSource '{storageIndexDefinition.Name}' has failed migrations and is currently not available.");

		if (update && elementData.ElementId == null)
			throw new ElementNotFoundException($"Can not update element with ElementId {elementData.ElementId}");

		if (!update && (elementData.Groups == null || elementData.Groups.Count == 0))
			throw new InsufficientRightsException("Element cannot be created without any groups.");

		if (elementData.Values.Count == 0)
			throw new DataStoreOperationException("Can not create element with empty data");
	}

	/// <summary>
	/// Move temporary file asynchronously to final destination on storage
	/// </summary>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="fieldValues"></param>
	/// <param name="storageTempFile"></param>
	/// <param name="elementId"></param>
	/// <param name="revisionId"></param>
	/// <param name="fileUploadId"></param>
	private async Task MoveTempFileToFinalDestinationAsync(StorageIndexDefinition storageIndexDefinition, IDictionary<string, object?> fieldValues,
														   StorageTempFile storageTempFile, string elementId,
														   long revisionId, string? fileUploadId)
	{
		await Task.Run(async () =>
		{
			var retryTimeout = RetryTimeout;
			if (fileUploadId != null)
			{
				try
				{
					if (!FileStore!.GetTempFileInfo(fileUploadId).Exists())
						return;

					FileStore.SaveFile(storageIndexDefinition, fieldValues, elementId, revisionId, fileUploadId, GetContext().Identifier, true);
					DeleteTempFileEntry(storageTempFile, storageIndexDefinition, elementId, revisionId);
					FileStore.DeleteTempFile(storageTempFile);
				}
				catch (Exception e)
				{
					await RetryTempFileMove(storageIndexDefinition, fieldValues, storageTempFile, elementId, revisionId, fileUploadId, e,
											retryTimeout);
				}
			}
		});
	}


	/// <summary>
	/// Set fileId at read time, if empty in database
	/// </summary>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="elementId"></param>
	/// <param name="fileId"></param>
	/// <exception cref="DataStoreOperationException"></exception>
	private async Task UpdateFileIdAtRead(StorageIndexDefinition storageIndexDefinition, string elementId, string fileId)
	{
		await Task.Run(() =>
		{
			try
			{
				WithDbContext(
					db =>
					{
						IDictionary<string, object?> updateValues = new Dictionary<string, object?>();
						updateValues.Add(StorageSystemField.SysFileId.ToString(), fileId);
						Db.DataHelper.Update(storageIndexDefinition, elementId, updateValues, GetIndexDefinition);
						return db.SaveChanges();
					});
			}
			catch (Exception e)
			{
				throw new DataStoreOperationException("Temporary file entry could not be deleted in database!", e);
			}
		});
	}

	// TODO: use it - https://levelbuild.atlassian.net/browse/LC-1754?atlOrigin=eyJpIjoiMGQ2YjYzMGFhOTYyNDIwNDlkMjZiYmMxNTcwN2ZiNTYiLCJwIjoiaiJ9
	public void MoveTempFileAtInitialization()
	{
		_logger.Information("Move temp file at initialization...");
		try
		{
			var storageTempFiles = WithDbContext(
				db => db.StorageTempFile
					.Where(it => it.ElementId != null).ToList());

			foreach (var storageTempFile in storageTempFiles)
			{
				var tempFileUploadId = storageTempFile.FileId;
				StorageIndexDefinition? storageIndexDefinition = GetIndexDefinitionById(storageTempFile.StorageIndexDefinitionId);
				var element = GetElement(storageIndexDefinition!.Name, storageTempFile.ElementId!);

				_ = MoveTempFileToFinalDestinationAsync(storageIndexDefinition, element!.Values, storageTempFile, storageTempFile.ElementId!,
														(long)storageTempFile.RevisionNumber!, tempFileUploadId);
			}
		}
		catch (Exception e)
		{
			throw new DataStoreOperationException("Exception at MoveTempFileAtInitialization()", e);
		}
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="fieldValues"></param>
	/// <param name="storageTempFile"></param>
	/// <param name="elementId"></param>
	/// <param name="revisionId"></param>
	/// <param name="fileUploadId"></param>
	/// <param name="currentException"></param>
	/// <param name="retryTimeout"></param>
	private async Task RetryTempFileMove(StorageIndexDefinition storageIndexDefinition, IDictionary<string, object?> fieldValues,
										 StorageTempFile storageTempFile, string elementId, long revisionId,
										 string fileUploadId, Exception currentException, int retryTimeout)
	{
		try
		{
			if (!FileStore!.GetTempFileInfo(fileUploadId).Exists())
				return;

			await Task.Delay(retryTimeout);

			if (currentException is FileSaveException)
				FileStore.SaveFile(storageIndexDefinition, fieldValues, elementId, revisionId, fileUploadId, GetContext().Identifier, true);

			if ((currentException is FileSaveException) || (currentException is DataStoreOperationException))
				DeleteTempFileEntry(storageTempFile, storageIndexDefinition, elementId, revisionId);

			if ((currentException is FileSaveException) || (currentException is DataStoreOperationException) || (currentException is FileDeleteException))
				FileStore.DeleteTempFile(storageTempFile);
		}
		catch (Exception newException)
		{
			if (retryTimeout < 3600000) // max. 1 hour
				retryTimeout *= 2;      // double timeout
			_logger.Warning(newException, $"Temp file move failed. Retrying after {retryTimeout}ms.");
			_ = RetryTempFileMove(storageIndexDefinition, fieldValues, storageTempFile, elementId, revisionId, fileUploadId, newException,
								  retryTimeout);
		}
	}

	/// <summary>
	/// Add system defined values to result values
	/// </summary>
	/// <param name="values"></param>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="storageTempFile"></param>
	/// <param name="oldValues"></param>
	/// <param name="origin"></param>
	/// <param name="groups">Only users in at least one of these groups can access this element</param>
	/// <param name="isCreate"></param>
	/// <param name="isDelete"></param>
	/// <param name="isRemoveFile"></param>
	/// <returns></returns>
	private IDictionary<string, object?> PrepareValues(IDictionary<string, object?> values, StorageIndexDefinition storageIndexDefinition,
													   StorageTempFile? storageTempFile,
													   IDictionary<string, object?>? oldValues, DataStoreOperationOrigin origin, IList<string>? groups,
													   bool isCreate = false, bool isDelete = false, bool isRemoveFile = false)
	{
		values = ReplaceCustomerKeys(values);

		foreach (StorageSystemField systemField in Enum.GetValues(typeof(StorageSystemField)))
		{
			switch (systemField)
			{
				case StorageSystemField.SysCreateDate:
					if (isCreate)
						values[StorageSystemField.SysCreateDate.ToString()] = DateTime.UtcNow;
					break;
				case StorageSystemField.SysCreateUser:
					if (isCreate)
						values[StorageSystemField.SysCreateUser.ToString()] = origin.InitiatorName;
					break;
				case StorageSystemField.SysModifyDate:
					values[StorageSystemField.SysModifyDate.ToString()] = DateTime.UtcNow;
					break;
				case StorageSystemField.SysModifyUser:
					values[StorageSystemField.SysModifyUser.ToString()] = origin.InitiatorName;
					break;
				case StorageSystemField.SysIsDeleted:
					if (isDelete)
						values[StorageSystemField.SysIsDeleted.ToString()] = true;
					break;
				case StorageSystemField.SysDeleteDate:
					if (isDelete)
						values[StorageSystemField.SysDeleteDate.ToString()] = DateTime.UtcNow;
					break;
				case StorageSystemField.SysCurrentRevision:
					if (isCreate || !storageIndexDefinition.StoreRevisions)
						values[StorageSystemField.SysCurrentRevision.ToString()] = 1;
					else
						values[StorageSystemField.SysCurrentRevision.ToString()] =
							Int64.Parse(oldValues![StorageSystemField.SysCurrentRevision.ToString()]!.ToString()!) + 1;
					break;
				case StorageSystemField.SysStoredRevision:
					if (isCreate)
						values[StorageSystemField.SysStoredRevision.ToString()] = 0;
					break;
				case StorageSystemField.SysPathId:
					if (storageIndexDefinition.CurrentStoragePath != null)
						values[StorageSystemField.SysPathId.ToString()] = storageIndexDefinition.CurrentStoragePath.Id;
					break;
				case StorageSystemField.SysFileName:
					if (storageTempFile != null)
						values[StorageSystemField.SysFileName.ToString()] = storageTempFile.FileName;
					if (isRemoveFile)
						values[StorageSystemField.SysFileName.ToString()] = null;
					break;
				case StorageSystemField.SysFileSize:
					if (storageTempFile != null)
						values[StorageSystemField.SysFileSize.ToString()] = storageTempFile.FileSize;
					if (isRemoveFile)
						values[StorageSystemField.SysFileSize.ToString()] = null;
					break;
				case StorageSystemField.SysFileHash:
					if (storageTempFile != null)
						values[StorageSystemField.SysFileHash.ToString()] = storageTempFile.FileHash;
					if (isRemoveFile)
						values[StorageSystemField.SysFileHash.ToString()] = null;
					break;
				case StorageSystemField.SysFileType:
					if (storageTempFile != null)
						values[StorageSystemField.SysFileType.ToString()] = storageTempFile.FileType;
					if (isRemoveFile)
						values[StorageSystemField.SysFileType.ToString()] = null;
					break;
				case StorageSystemField.SysFileDate:
					if (storageTempFile != null)
						values[StorageSystemField.SysFileDate.ToString()] = storageTempFile.FileDate;
					if (isRemoveFile)
						values[StorageSystemField.SysFileDate.ToString()] = null;
					break;
				case StorageSystemField.SysFileContent:
					if (storageTempFile != null)
						values[StorageSystemField.SysFileContent.ToString()] = GetFileContent(storageTempFile);
					if (isRemoveFile)
						values[StorageSystemField.SysFileContent.ToString()] = null;
					break;
				case StorageSystemField.SysGroups:
					if (groups != null)
						values[StorageSystemField.SysGroups.ToString()] = groups;
					break;
			}
		}

		return values;
	}

	/// <summary>
	/// Used to set RevisionHash to current values and save complete former values
	/// </summary>
	/// <param name="values"></param>
	/// <param name="oldValues"></param>
	/// <param name="it"></param>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="elementId"></param>
	/// <param name="origin"></param>
	/// <param name="newRevNumber"></param>
	private void SetRevisionHash(IDictionary<string, object?> values, IDictionary<string, object?> oldValues,
								 QueryFactory it, StorageIndexDefinition storageIndexDefinition, string elementId,
								 DataStoreOperationOrigin origin, long newRevNumber)
	{
		IDictionary<string, object?> newValues = new Dictionary<string, object?>();
		IDictionary<string, object?> oldValuesForHash = new Dictionary<string, object?>();

		foreach (StorageFieldDefinitionOrm field in storageIndexDefinition.Fields)
		{
			if (!field.SystemField)
			{
				if (values.TryGetValue(field.Name, out var value))
					newValues[field.Name] = value;
				else if (oldValues.TryGetValue(field.Name, out var oldValue))
					newValues[field.Name] = oldValue;
				else if (field.DefaultValue != null)
					newValues[field.Name] = field.DefaultValue;

				if (oldValues.TryGetValue(field.Name, out var value1))
					oldValuesForHash[field.Name] = value1;
			}
		}

		if (storageIndexDefinition.StoreRevisions)
		{
			string? oldRevisionHash = oldValues[StorageSystemField.SysRevisionHash.ToString()]!.ToString();
			if (string.IsNullOrWhiteSpace(oldRevisionHash))
			{
				_revisionHelper!.CreateRevision(it, storageIndexDefinition, elementId, oldValues, origin, false,
												DataStoreOperationType.Create, newRevNumber - 1);
			}
			else
			{
				string oldRevisionHashGenerated = GetMd5RevisionHash(oldValuesForHash, storageIndexDefinition.Fields);
				if (!oldRevisionHash.Equals(oldRevisionHashGenerated))
				{
					long oldRevisionNumber = Int64.Parse(oldValues[StorageSystemField.SysCurrentRevision.ToString()]!.ToString()!);
					IDictionary<string, object?>? oldValuesFromFile = null;
					try
					{
						var revisionData = _revisionHelper!.GetRevision(storageIndexDefinition, elementId, oldRevisionNumber);
						oldValuesFromFile = revisionData!.Values.Select(val => new KeyValuePair<string, object?>(val.Name, val.Value!)).ToDictionary();
					}
					catch (ElementNotFoundException)
					{
						// ignore
					}

					_revisionHelper!.AddRevision(it, storageIndexDefinition, elementId, oldValues, oldValuesFromFile, origin, false, newRevNumber);
					newRevNumber++;
					values[StorageSystemField.SysCurrentRevision.ToString()] = newRevNumber;
					newValues[StorageSystemField.SysCurrentRevision.ToString()] = newRevNumber;
				}
			}
		}

		values[StorageSystemField.SysRevisionHash.ToString()] = GetMd5RevisionHash(newValues, storageIndexDefinition.Fields);
	}

	private static string GetMd5RevisionHash(IDictionary<string, object?> values, List<StorageFieldDefinitionOrm> fields, bool isLowercase = false)
	{
		var sortedValues = new SortedDictionary<string, object?>(values);

		// keys explicitly excluded for hash 
		List<string> noHashKeys = new List<string>();
		noHashKeys.Add(StorageSystemField.SysRevisionHash.ToString());
		noHashKeys.Add(StorageSystemField.SysStoredRevision.ToString());
		noHashKeys.Add(StorageSystemField.SysFavourites.ToString());

		StringBuilder stringBuilder = new StringBuilder();
		foreach (var (key, value) in sortedValues)
		{
			StorageFieldDefinitionOrm? field = fields.FirstOrDefault(it => it.Name.Equals(key));
			if (field != null && !field.SystemField && value != null)
			{
				// not excluded hash keys
				if (!noHashKeys.Contains(key))
				{
					Type valueType = value.GetType();
					if (value is IList listValue)
					{
						foreach (object obj in listValue)
						{
							Log.Debug("GetMd5RevisionHash: " + field.Name + " - " + obj.ToString());
							stringBuilder.Append(GetValueByType(obj, field));
						}
					}
					else if (valueType.IsArray)
					{
						Object[] arrayValue = (Object[])value;
						foreach (object obj in arrayValue)
						{
							Log.Debug("GetMd5RevisionHash: " + field.Name + " - " + obj.ToString());
							stringBuilder.Append(GetValueByType(obj, field));
						}
					}
					else
					{
						Log.Debug("GetMd5RevisionHash: " + field.Name + " - " + value.ToString());
						stringBuilder.Append(GetValueByType(value, field));
					}
				}
			}
		}

		Log.Debug("GetMd5RevisionHash: " + stringBuilder.ToString());
		using (var md5 = MD5.Create())
		{
			var byteHash = md5.ComputeHash(Encoding.UTF8.GetBytes(stringBuilder.ToString()));
			var hash = BitConverter.ToString(byteHash).Replace("-", "");
			Log.Debug("GetMd5RevisionHash: " + hash);
			return (isLowercase) ? hash.ToLower() : hash;
		}
	}

	private static string GetValueByType(object value, StorageFieldDefinitionOrm field)
	{
		if (value is string)
			return (string)value;

		if (field.Type == DataStoreFieldType.Time)
		{
			var timeValue = (DateTime)value;
			// only possibility - TimeOfDay differs in current milliseconds
			return $"{timeValue.Hour}.{timeValue.Minute}.{timeValue.Second}";
		}
		else if (field.Type == DataStoreFieldType.Date)
		{
			var timeValue = (DateTime)value;
			// only possibility - Date differs in time values
			return $"{timeValue.Day}.{timeValue.Month}.{timeValue.Year}";
		}
		else if (field.Type == DataStoreFieldType.DateTime)
		{
			var timeValue = (DateTime)value;
			// only possibility - Date differs in time values and TimeOfDay differs in current milliseconds
			return $"{timeValue.Day}.{timeValue.Month}.{timeValue.Year} {timeValue.Hour}.{timeValue.Minute}.{timeValue.Second}";
		}
		else
			return value.ToString()!;
	}

	private IDictionary<string, object?> ReplaceCustomerKeys(IDictionary<string, object?> values)
	{
		if (Db.CustomerContext != null)
		{
			IDictionary<string, object?> newValues = new Dictionary<string, object?>();
			foreach (var (key, value) in values)
			{
				if (key.Contains("customer#"))
				{
					var keys = key.Split("customer#");
					newValues.Add("customer__" + keys[1], value);
				}
				else
				{
					newValues.Add(key, value);
				}
			}

			values = newValues;
		}

		return values;
	}

	private string? GetFileContent(StorageTempFile storageTempFile)
	{
		var maxContentLenth = 4000;
		var fileInfo = FileStore!.GetTempFileInfo(storageTempFile.FileId + "");
		using (var stream = fileInfo.ReadFile())
		{
			var text = BasicTextProvider.GetText(storageTempFile.FileName, storageTempFile.FileType, stream, maxContentLenth);

			if (text == null)
				return text;

			if (text.Length > maxContentLenth)
				return text.Substring(0, maxContentLenth);

			return text;
		}
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="dataSourceName"></param>
	/// <param name="elementData"></param>
	/// <param name="origin"></param>
	/// <param name="lookupFields"></param>
	/// <returns></returns>
	/// <exception cref="DataSourceNotFoundException"></exception>
	/// <exception cref="ElementNotFoundException"></exception>
	public DataStoreSuccessInfo UpdateElement(string dataSourceName, DataStoreElementData elementData,
											  DataStoreOperationOrigin origin, IList<DataStoreQueryField>? lookupFields = null)
	{
		_logger.Information("Update Element: " + elementData.ElementId);

		if (_isMainConnection)
			throw new DataStoreConnectionException("Cannot update element on main database without context!");

		StorageIndexDefinition? storageIndexDefinition = GetDefinitionWithPaths(dataSourceName);

		if (storageIndexDefinition == null)
			throw new DataSourceNotFoundException($"Could not find DataSource for name {dataSourceName}");

		if (storageIndexDefinition.IsView)
			throw new DataStoreQueryException("Cannot update element on a view!");

		CheckDefinitionAndData(storageIndexDefinition, dataSourceName, elementData, true);

		if (!Guid.TryParse(elementData.ElementId!, out var elementId))
			throw new ElementNotFoundException($"Could not parse element id as guid '{elementData.ElementId!}'");
		
		Query query = new Query(storageIndexDefinition.Name).Where(StorageSystemField.Id.ToString(), elementId)
			.Where(StorageSystemField.SysIsDeleted.ToString(), false);
		var oldValues = Db.DataHelper.Get(storageIndexDefinition, query, null).FirstOrDefault();

		if (oldValues == null)
			throw new ElementNotFoundException(
				$"Could not update nonexisting element with {StorageSystemField.Id.ToString()}='{elementData.ElementId}' for datasource {dataSourceName}");

		StorageTempFile? storageTempFile = null;
		if (elementData.FileUploadId != null)
		{
			storageTempFile = GetTempFileEntry(elementData.FileUploadId);
			if (storageTempFile == null)
				throw new ElementNotFoundException($"The no File could be found for the FileUploadId '{elementData.FileUploadId}'");

			// we'll just leave it to be cleaned up a few hours later
			if ((string)oldValues[StorageSystemField.SysFileHash.ToString()]! == storageTempFile.FileHash &&
				(string)oldValues[StorageSystemField.SysFileName.ToString()]! == storageTempFile.FileName)
				storageTempFile = null;
		}

		IDictionary<string, object?> values = elementData.Values.ToDictionary();
		var fields = storageIndexDefinition.Fields.ToDictionary(field => field.Name.ToLower());
		var unknownFields = values.Keys.Where(it => !fields.ContainsKey(it.ToLower())).ToList();

		if (!unknownFields.IsNullOrEmpty())
			throw new DataStoreOperationException($"Fields '{string.Join("', '", unknownFields)}' do not exist in table {storageIndexDefinition.Name}");

		var valuesCorrectKeys = values.Select(value => new KeyValuePair<string, object?>(fields[value.Key.ToLower()].Name, value.Value)).ToDictionary();
		values = PrepareValues(valuesCorrectKeys, storageIndexDefinition, storageTempFile, oldValues, origin, elementData.Groups);
		long newRevNumber = Int64.Parse(values[StorageSystemField.SysCurrentRevision.ToString()]!.ToString()!);

		IDictionary<string, object?> resData = new Dictionary<string, object?>();
		string? newFileUploadId = null;
		resData = Db.ConnectionHelper.WithTransaction(it =>
		{
			SetRevisionHash(values, oldValues, it, storageIndexDefinition, elementData.ElementId!, origin, newRevNumber);
			newRevNumber = Int64.Parse(values[StorageSystemField.SysCurrentRevision.ToString()]!.ToString()!);

			if (storageTempFile != null)
			{
				storageTempFile.ElementId = elementData.ElementId;
				storageTempFile.StorageIndexDefinition = storageIndexDefinition;
				storageTempFile.RevisionNumber = (storageIndexDefinition.StoreRevisions) ? newRevNumber : 1;
				UpdateTempFileEntry(storageTempFile);
				newFileUploadId = FileStore!.GetFileId(elementData.ElementId!, newRevNumber, false);
				values[StorageSystemField.SysFileId.ToString()] = newFileUploadId;
			}

			// UpdateWithConnection
			resData = Db.DataHelper.Update(it, storageIndexDefinition, elementData.ElementId!, values!, GetIndexDefinition, Db.Authentication?.Language ?? "en",
										   lookupFields);

			// remove fake field SysIsFavourite
			resData.Remove(StorageSystemField.SysIsFavourite.ToString());

			if (storageIndexDefinition.StoreRevisions)
			{
				IDictionary<string, object?> updateValuesForRevision = new Dictionary<string, object?>();
				foreach (var (key, value) in resData)
				{
					updateValuesForRevision[key] = value;
				}

				foreach (var (key, value) in oldValues)
				{
					updateValuesForRevision.TryAdd(key, value);
				}

				_revisionHelper!.AddRevision(it, storageIndexDefinition, elementData.ElementId!, updateValuesForRevision, oldValues, origin,
											 (storageTempFile != null), newRevNumber);
			}

			return resData;
		});

		if (storageTempFile != null)
			_ = MoveTempFileToFinalDestinationAsync(storageIndexDefinition, values, storageTempFile, elementData.ElementId!, newRevNumber,
													storageTempFile.FileId);

		DataStoreFileInfo? fileInfo = null;
		if (storageTempFile != null)
		{
			fileInfo = new DataStoreFileInfo(newFileUploadId!, storageTempFile.FileName, storageTempFile.FileSize);
		}
		else if (oldValues.TryGetValue(StorageSystemField.SysFileId.ToString(), out object? sysFileId) && sysFileId != null && sysFileId.ToString() != "")
		{
			fileInfo = new DataStoreFileInfo(sysFileId.ToString()!,
											 (string)oldValues[StorageSystemField.SysFileName.ToString()]!,
											 (long)oldValues[StorageSystemField.SysFileSize.ToString()]!);
		}

		bool isFavourite = GetBoolForKey(values, StorageSystemField.SysIsFavourite.ToString());
		bool isInactive = GetBoolForKey(values, StorageSystemField.SysInactiveDate.ToString());
		// remove fake field SysIsFavourite
		elementData.Values.Remove(StorageSystemField.SysIsFavourite.ToString());

		var res = new DataStoreSuccessInfo(elementData.ElementId!, DataStoreOperationType.Update)
		{
			ElementData = new DataStoreElement(
					elementData.ElementId!,
					resData,
					resData[StorageSystemField.SysGroups.ToString()]!.ToArray().Select(it => (string)it).ToList(),
					isInactive, isFavourite)
				{ FileInfo = fileInfo }
		};

		_logger.Information("Element updated: " + elementData.ElementId);
		return res;
	}

	private bool GetBoolForKey(IDictionary<string, object?> values, string key)
	{
		bool isTrue = (values.ContainsKey(key)
					   && values[key] != null
					   && ((bool.TryParse(values[key]!.ToString(), out var newBool) ? newBool : false)
						   || (DateTime.TryParse(values[key]!.ToString(), out _))));
		return isTrue;
	}

	public Task<DataStoreSuccessInfo> UpdateElementAsync(string dataSourceName, DataStoreElementData elementData, DataStoreOperationOrigin origin,
														 IList<DataStoreQueryField>? lookupFields = null)
	{
		return Task.Run(() => { return UpdateElement(dataSourceName, elementData, origin, lookupFields); });
	}

	public Task<DataStoreSuccessInfo> DeleteElementAsync(string dataSourceName, string elementId, DataStoreOperationOrigin origin)
	{
		return Task.Run(() => { return DeleteElement(dataSourceName, elementId, origin); });
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="dataSourceName"></param>
	/// <param name="elementId"></param>
	/// <param name="origin"></param>
	/// <returns></returns>
	/// <exception cref="DataSourceNotFoundException"></exception>
	/// <exception cref="ElementNotFoundException"></exception>
	public DataStoreSuccessInfo DeleteElement(string dataSourceName, string elementId,
											  DataStoreOperationOrigin origin)
	{
		_logger.Information("Delete Element: " + elementId);

		if (_isMainConnection)
			throw new DataStoreConnectionException("Cannot delete element on main database without context!");

		StorageIndexDefinition? storageIndexDefinition = GetDefinitionWithPaths(dataSourceName);

		if (storageIndexDefinition == null)
			throw new DataSourceNotFoundException($"Could not find DataSource for name {dataSourceName}");

		if (storageIndexDefinition.IsView)
			throw new DataStoreQueryException("Cannot remove element from a view!");

		if (_failedMigrationTables.Contains(dataSourceName))
			throw new DataStoreOperationException($"DataSource '{dataSourceName}' has failed migrations and is currently not available.");

		if (storageIndexDefinition.StoreRevisions)
		{
			// set deletion date + create deleted revision
			return DeleteRevisedElement(dataSourceName, elementId, false, true, origin);
		}
		else
		{
			DataStoreElement? dataStoreElement = GetElement(dataSourceName, elementId);

			if (dataStoreElement == null)
			{
				throw new ElementNotFoundException(
					$"An element with {StorageSystemField.Id.ToString()} = {elementId} has not been found for definition {dataSourceName}");
			}

			bool isLink = bool.Parse(dataStoreElement.Values[StorageSystemField.SysIsLink.ToString()]!.ToString()!);
			bool isLinked = bool.Parse(dataStoreElement.Values[StorageSystemField.SysIsLinked.ToString()]!.ToString()!);

			if (isLinked) // no move her, there can be multiple link documents!
				CopyLinkedFileToLinkDocuments(storageIndexDefinition, dataStoreElement);

			// delete files on disk, if it is not a link document
			if (dataStoreElement.Values[StorageSystemField.SysFileHash.ToString()] != null && !isLink)
			{
				// file has at least a default revision id as a long stored in index entry
				var currentRevisionId = Int64.Parse(dataStoreElement.Values[StorageSystemField.SysCurrentRevision.ToString()]!.ToString()!);
				var currentPathId = Int64.Parse(dataStoreElement.Values[StorageSystemField.SysPathId.ToString()]!.ToString()!);
				string fileId = FileStore!.GetFileId(elementId, currentRevisionId, false);
				StorageFile storageFile = FileStore.ParseFileId(fileId)!;

				// search for current file on storage
				FileStore.DeleteFile(storageIndexDefinition, dataStoreElement.Values, GetContext().Identifier, storageFile, currentPathId);
			}

			// delete data set after file, if file is not found or exception
			if (!Db.DataHelper.Delete(storageIndexDefinition, elementId))
				throw new ElementNotFoundException(
					$"An element with {StorageSystemField.Id.ToString()} = {elementId} has not been found for definition {dataSourceName}");
		}

		return new DataStoreSuccessInfo(elementId, DataStoreOperationType.Delete);
	}

	private void CopyLinkedFileToLinkDocuments(StorageIndexDefinition storageIndexDefinition, DataStoreElement dataStoreElement)
	{
		var storageLinks = WithDbContext(
			db => db.StorageLink
				.Where(it => it.LinkedIndexDefinitionId == storageIndexDefinition.Id
							 && it.LinkedElementId == dataStoreElement.Id).ToList());

		foreach (var storageLink in storageLinks)
		{
			long requiredRevisionNumber = (storageLink.LinkedRevisionNumber != null)
											  ? (long)storageLink.LinkedRevisionNumber
											  : long.Parse(dataStoreElement.Values[StorageSystemField.SysCurrentRevision.ToString()]!.ToString()!);

			string fileId = FileStore!.GetFileId(storageLink.LinkedElementId, requiredRevisionNumber, false);

			DataStoreFileStream dataStoreFileStream = GetFile(storageIndexDefinition.Name, fileId);
			dataStoreFileStream.SetLength(long.Parse(dataStoreElement.Values[StorageSystemField.SysFileSize.ToString()]!.ToString()!));
			string tempFileId = UploadFile(storageIndexDefinition.Name, dataStoreFileStream);
			dataStoreFileStream.Close();

			DataStoreElement element = GetElement(storageIndexDefinition.Name, storageLink.ElementId)!;
			element.Values[StorageSystemField.SysIsLink.ToString()] = false;
			var dataStoreElementData = new DataStoreElementData(element.Values, element.Groups, tempFileId)
			{
				ElementId = storageLink.ElementId
			};
			DataStoreOperationOrigin origin = new DataStoreOperationOrigin(DataStoreOperationOriginType.System, 0, "system");
			UpdateElement(storageIndexDefinition.Name, dataStoreElementData, origin);

			// delete link entry
			WithDbContext(db =>
			{
				db.StorageLink.Remove(storageLink);
				return db.SaveChanges();
			});
		}
	}

	/// <summary>
	/// Mark element as deleted and add revision for deletion
	/// </summary>
	/// <param name="dataSourceName"></param>
	/// <param name="elementId"></param>
	/// <param name="fileOnly"></param>
	/// <param name="withRevision"></param>
	/// <param name="origin"></param>
	/// <returns></returns>
	/// <exception cref="DataSourceNotFoundException"></exception>
	/// <exception cref="ElementNotFoundException"></exception>
	protected DataStoreSuccessInfo DeleteRevisedElement(string dataSourceName, string elementId, bool fileOnly, bool withRevision,
														DataStoreOperationOrigin origin)
	{
		if (fileOnly)
			_logger.Information("Remove file for: " + elementId);
		else
			_logger.Information("Delete Element: " + elementId);

		StorageIndexDefinition? storageIndexDefinition = GetDefinitionWithPaths(dataSourceName);

		if (storageIndexDefinition == null)
			throw new DataSourceNotFoundException($"Could not find DataSource for name {dataSourceName}");

		var oldValues = GetElement(storageIndexDefinition.Name, elementId)?.Values;

		if (oldValues == null)
			throw new ElementNotFoundException(
				$"Could not update nonexisting element with {StorageSystemField.Id.ToString()}='{elementId}' for datasource {dataSourceName}");

		IDictionary<string, object?> values = new Dictionary<string, object?>(oldValues.ToDictionary());
		values = PrepareValues(values, storageIndexDefinition, null, oldValues, origin,
							   oldValues[StorageSystemField.SysGroups.ToString()]!.ToArray().Select(it => (string)it).ToList(), false, !fileOnly, fileOnly);

		if (!withRevision)
			values.Remove(StorageSystemField.SysCurrentRevision.ToString());

		// remove fake field SysIsFavourite
		values.Remove(StorageSystemField.SysIsFavourite.ToString());

		if (withRevision)
		{
			Db.ConnectionHelper.WithTransaction(it =>
			{
				var resData = Db.DataHelper.Update(it, storageIndexDefinition, elementId, values, GetIndexDefinition);
				long newRevId = Int64.Parse(values[StorageSystemField.SysCurrentRevision.ToString()]!.ToString()!);
				_revisionHelper!.AddRevision(it, storageIndexDefinition, elementId, values, oldValues, origin, false, newRevId);
				return resData;
			});
		}
		else
		{
			Db.DataHelper.Update(storageIndexDefinition, elementId, values, GetIndexDefinition);
		}

		return new DataStoreSuccessInfo(elementId, DataStoreOperationType.Delete);
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="dataSourceName"></param>
	/// <param name="fileId"></param>
	/// <exception cref="DataStoreQueryException"></exception>
	/// <exception cref="ElementNotFoundException"></exception>
	/// <exception cref="FileNotFoundException"></exception>
	public bool IsGetFileAllowed(string dataSourceName, string fileId)
	{
		var fileMeta = FileStore!.ParseFileId(fileId);

		if (fileMeta == null)
			throw new DataStoreQueryException($"FileId {fileId} was in the wrong format");

		DataStoreElement? dataStoreElement = GetElement(dataSourceName, fileMeta.ElementId!);

		if (dataStoreElement == null)
			throw new ElementNotFoundException($"Could not find element for File with Id {fileId}");

		return dataStoreElement.FileInfo?.Id == fileId;
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="dataSourceName"></param>
	/// <param name="fileId"></param>
	/// <returns></returns>
	public Task<bool> IsGetFileAllowedAsync(string dataSourceName, string fileId)
	{
		return Task.Run(() => IsGetFileAllowed(dataSourceName, fileId));
	}

	/// <summary>
	/// Get file of element by file id 
	/// </summary>
	/// <param name="dataSourceName"></param>
	/// <param name="fileId"></param>
	/// <returns></returns>
	public DataStoreFileStream? GetFile(string dataSourceName, string fileId)
	{
		if (_isMainConnection)
			throw new DataStoreConnectionException("Cannot get file on main database without context!");

		StorageIndexDefinition? storageIndexDefinition = GetDefinitionWithPaths(dataSourceName);

		if (storageIndexDefinition == null)
			throw new DataSourceNotFoundException($"Could not find storage index definition for {dataSourceName}");

		if (storageIndexDefinition.IsView)
			throw new DataStoreQueryException("Cannot get file from a view!");

		var tempFile = GetTempFile(storageIndexDefinition, fileId);
		if (tempFile != null)
			return tempFile;

		long? pathId;
		StorageFile? storageFile = FileStore!.ParseFileId(fileId);

		if (storageFile == null)
			throw new DataStoreQueryException($"Could not parse fileId {fileId}");

		DataStoreElement? dataStoreElement = GetElement(dataSourceName, storageFile.ElementId!, null, true);

		if (dataStoreElement == null)
			throw new ElementNotFoundException($"Could not find element with Id {storageFile.ElementId}");

		var currentRevisionId = dataStoreElement.Values[StorageSystemField.SysCurrentRevision.ToString()]!;

		_logger.Debug($"storageIndexDefinition.StoreRevisions: {storageIndexDefinition.StoreRevisions} - Current Revision: {currentRevisionId} - " +
					  $"dataStoreElement.Values[StorageSystemField.SysFileId.ToString()]: {dataStoreElement.Values[StorageSystemField.SysFileId.ToString()]}" +
					  $" - fileId: {fileId}");

		// search for current file on storage
		if (!storageIndexDefinition.StoreRevisions || dataStoreElement.Values[StorageSystemField.SysFileId.ToString()]?.ToString() == fileId)
		{
			if (dataStoreElement.Values[StorageSystemField.SysFileId.ToString()]?.ToString() != fileId)
				throw new FileNotFoundException($"Could not find file with id {fileId} for element {dataStoreElement.Id} in data source {dataSourceName}.");

			pathId = (dataStoreElement.Values.ContainsKey(StorageSystemField.SysPathId.ToString()))
						 ? (long)dataStoreElement.Values[StorageSystemField.SysPathId.ToString()]!
						 : null;
			storageFile.FileExtension = dataStoreElement.Values[StorageSystemField.SysFileType.ToString()]!.ToString()!;
			storageFile.FileDate = (DateTime)dataStoreElement.Values[StorageSystemField.SysFileDate.ToString()]!;
			storageFile.FileLength = (long)dataStoreElement.Values[StorageSystemField.SysFileSize.ToString()]!;
			storageFile.FileName = dataStoreElement.Values[StorageSystemField.SysFileName.ToString()]!.ToString()!;
			storageFile.FileHash = dataStoreElement.Values[StorageSystemField.SysFileHash.ToString()]!.ToString()!;
			storageFile = FileStore.LoadFile(storageIndexDefinition, dataStoreElement.Values, GetContext().Identifier, storageFile, pathId);
		}
		else // search for revision on disk, if current revision is higher than requested revision
		{
			DataStoreRevisionData? dataStoreRevisionData =
				_revisionHelper!.GetRevision(storageIndexDefinition, storageFile.ElementId!, (long)storageFile.RevisionNumber!);

			if (dataStoreRevisionData == null)
				throw new ElementNotFoundException($"Could not find revision with id {storageFile.RevisionNumber} for element {dataStoreElement.Id}");

			IDictionary<string, object?> revisionValuesAsDict = dataStoreRevisionData.Values
				.Select(it => new KeyValuePair<string, object?>(it.Name, it.Value))
				.ToDictionary();

			if (!revisionValuesAsDict.ContainsKey(StorageSystemField.SysFileId.ToString()))
				throw new ElementNotFoundException($"RevisionFile does not contain SysFileId for file with id {fileId}");
			if (revisionValuesAsDict[StorageSystemField.SysFileId.ToString()]?.ToString() != fileId)
				_logger.Error(
					$"SysFileId of RevisionFile {revisionValuesAsDict[StorageSystemField.SysFileId.ToString()]} not equal to fileId {fileId} in data source {dataSourceName}.");

			pathId = (revisionValuesAsDict.ContainsKey(StorageSystemField.SysPathId.ToString()))
						 ? Int64.Parse(revisionValuesAsDict[StorageSystemField.SysPathId.ToString()]!.ToString()!)
						 : null;
			storageFile.FileExtension = revisionValuesAsDict[StorageSystemField.SysFileType.ToString()]!.ToString()!;
			storageFile.FileDate = (DateTime)revisionValuesAsDict[StorageSystemField.SysFileDate.ToString()]!;
			storageFile.FileLength = Int64.Parse(revisionValuesAsDict[StorageSystemField.SysFileSize.ToString()]!.ToString()!);
			storageFile.FileName = revisionValuesAsDict[StorageSystemField.SysFileName.ToString()]!.ToString()!;
			storageFile.FileHash = revisionValuesAsDict[StorageSystemField.SysFileHash.ToString()]!.ToString()!;

			try
			{
				storageFile = FileStore.LoadFile(storageIndexDefinition, revisionValuesAsDict, GetContext().Identifier, storageFile, pathId);
			}
			catch (Exception e)
			{
				_logger.Error(e,
					$"Could not find file with id {fileId} for element {dataStoreElement.Id} with revision {storageFile.RevisionNumber} in data source {dataSourceName}.");
				return null;
			}
		}

		return storageFile.ToDto();
	}

	public Task<DataStoreFileStream?> GetFileAsync(string definitionName, string fileId)
	{
		// why async with return value? removed async
		return Task.Run(() => { return GetFile(definitionName, fileId); })!;
	}

	private DataStoreElement? GetLinkedElement(StorageIndexDefinition storageIndexDefinition, string elementId, out StorageLink? storageLink)
	{
		storageLink = WithDbContext(
			db => db.StorageLink
				.FirstOrDefault(it
									=> it.LinkedIndexDefinitionId == storageIndexDefinition.Id
									   && it.ElementId == elementId));

		if (storageLink != null)
		{
			var referenceIndexDefinition = GetIndexDefinitionById(storageLink.LinkedIndexDefinitionId);
			DataStoreElement? referenceElement = GetElement(referenceIndexDefinition!.Name, storageLink.LinkedElementId, null, true);
			return referenceElement;
		}

		throw new DataStoreQueryException("No reference element found for link data set!");
	}

	/// <summary>
	/// Get file from temp path, if new file is not finally stored yet
	/// </summary>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="fileId"></param>
	/// <returns></returns>
	private DataStoreFileStream? GetTempFile(StorageIndexDefinition storageIndexDefinition, string fileId)
	{
		// try to parse, if fileId is tempFile
		if (!fileId.Contains("_data"))
		{
			try
			{
				var tempFileEntry = GetTempFileEntry(fileId);
				if (tempFileEntry == null || tempFileEntry.StorageIndexDefinitionId != storageIndexDefinition.Id)
					throw new ElementNotFoundException($"File for index definition {storageIndexDefinition.Name} with id {fileId} not found!");

				StorageFile tempStorageFile = FileStore!.GetTempFile(tempFileEntry ?? throw new InvalidOperationException());
				return tempStorageFile.ToDto();
			}
			catch (Exception e)
			{
				if (e is ElementNotFoundException)
					throw;

				// ex. if temp file is moved in meantime
			}
		}
		else
		{
			var storageMetaFile = FileStore!.ParseFileId(fileId);

			if (storageMetaFile == null)
				throw new DataStoreQueryException($"Could not parse FileId '{fileId}'!");

			StorageTempFile? tempFileEntry = GetTempFileEntry(storageIndexDefinition.Id, storageMetaFile.ElementId!, (long)storageMetaFile.RevisionNumber!);
			if (tempFileEntry != null)
			{
				try
				{
					StorageFile tempStorageFile = FileStore.GetTempFile(tempFileEntry);
					return tempStorageFile.ToDto();
				}
				catch (Exception)
				{
					// ex. if temp file is moved in meantime
				}
			}
		}

		return null;
	}

	/// <summary>
	/// Upload file to temp path
	/// </summary>
	/// <param name="dataSourceName"></param>
	/// <param name="dataStoreFileStream"></param>
	/// <returns></returns>
	public string UploadFile(string dataSourceName, DataStoreFileStream dataStoreFileStream)
	{
		if (_isMainConnection)
			throw new DataStoreConnectionException("Cannot upload file on main database without context!");

		StorageIndexDefinition? storageIndexDefinition = GetDefinitionWithPaths(dataSourceName);
		if (storageIndexDefinition == null)
			throw new DataSourceNotFoundException($"No storage index definition found for name: {dataSourceName}");

		if (storageIndexDefinition.IsView)
			throw new DataStoreQueryException("Cannot upload file to a view!");

		string nextGuid = Guid.NewGuid().ToString();
		string fileHash = FileStore!.SaveTemporaryFile(dataStoreFileStream, nextGuid, out long fileSize);
		dataStoreFileStream.SetLength(fileSize);
		_logger.Information($"Create temp file {nextGuid} for {fileHash} ...");
		CreateTempFileEntry(storageIndexDefinition, nextGuid, dataStoreFileStream, fileHash);
		_ = ClearTempFileStorage();
		_logger.Information($"Temp file {nextGuid} for {fileHash} created.");
		return nextGuid;
	}

	public Task<string> UploadFileAsync(string definitionName, DataStoreFileStream fileStream)
	{
		// Why async with needed return value of temporary upload id? removed async
		return Task.Run(() => { return UploadFile(definitionName, fileStream); });
	}

	/// <summary>
	/// Clears temp file storage deleting all temp files older than 12 hours
	/// </summary>
	private async Task ClearTempFileStorage()
	{
		await Task.Run(() =>
		{
			var now = DateTime.UtcNow;

			// only clear temp file storage once every hour
			lock (TempFileDeletionCache)
			{
				var lastDateTime = TempFileDeletionCache.GetOrAdd(Db.ConnectionString, DateTime.MinValue.ToUniversalTime());

				if (now - lastDateTime < TimeSpan.FromHours(1))
					return;
				TempFileDeletionCache[Db.ConnectionString] = now;
			}

			var selectLessThan = now - TimeSpan.FromHours(12);
			var storageTempFiles = WithDbContext(
				db => db.StorageTempFile
					.Where(it => it.CreateDate < selectLessThan).ToList());

			foreach (var storageTempFile in storageTempFiles)
			{
				try
				{
					FileStore!.DeleteTempFile(storageTempFile);
				}
				catch (FileDeleteException fileDeleteException)
				{
					_logger.Warning(fileDeleteException, fileDeleteException.Message);
				}

				try
				{
					WithDbContext(db =>
					{
						db.StorageTempFile.Remove(storageTempFile);
						return db.SaveChanges();
					});
				}
				catch (DbUpdateConcurrencyException)
				{
					// clear change tracker
					WithDbContext(db => 0);
				}
			}
		});
	}

	/// <summary>
	/// Get temp file by fileId, if it is already not connected to a certain element entry
	/// </summary>
	/// <param name="tempFileId"></param>
	/// <returns></returns>
	private StorageTempFile? GetTempFileEntry(string tempFileId)
	{
		StorageTempFile? storageTempFile = WithDbContext(
			db => db.StorageTempFile
				.FirstOrDefault(it => it.FileId == tempFileId));

		return storageTempFile;
	}

	/// <summary>
	/// Get temp file by elementId and revisionId, if it is connected to a certain element entry
	/// </summary>
	/// <param name="storageIndexDefinitionId"></param>
	/// <param name="elementId"></param>
	/// <param name="revisionNumber"></param>
	/// <returns></returns>
	private StorageTempFile? GetTempFileEntry(long storageIndexDefinitionId, string elementId, long revisionNumber)
	{
		StorageTempFile? storageTempFile = (StorageTempFile?)WithDbContext(
			db => db.StorageTempFile
				.Where(it => it.StorageIndexDefinitionId == storageIndexDefinitionId)
				.Where(it => it.ElementId == elementId)
				.Where(it => it.RevisionNumber == revisionNumber).FirstOrDefault()
		);

		return storageTempFile;
	}

	/// <summary>
	/// Delete temp file entry in temp file table in database
	/// </summary>
	/// <param name="storageTempFile"></param>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="elementId"></param>
	/// <param name="revisionId"></param>
	private void DeleteTempFileEntry(StorageTempFile storageTempFile, StorageIndexDefinition storageIndexDefinition, string elementId, long revisionId)
	{
		try
		{
			WithDbContext(
				db =>
				{
					string newFileId = FileStore!.GetFileId(elementId, revisionId, false);
					IDictionary<string, object?> updateValues = new Dictionary<string, object?>();
					updateValues.Add(StorageSystemField.SysFileId.ToString(), newFileId);
					Db.DataHelper.Update(storageIndexDefinition, elementId, updateValues, GetIndexDefinition);

					db.StorageTempFile.Remove(storageTempFile);
					return db.SaveChanges();
				});
		}
		catch (Exception e)
		{
			throw new DataStoreOperationException("Temporary file entry could not be deleted in database!", e);
		}
	}

	/// <summary>
	/// Creates temp file entry in temp file table in database
	/// </summary>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="tempFileId"></param>
	/// <param name="dataStoreFileStream"></param>
	/// <param name="fileHash"></param>
	private void CreateTempFileEntry(StorageIndexDefinition storageIndexDefinition, string tempFileId, DataStoreFileStream dataStoreFileStream, string fileHash)
	{
		StorageTempFile storageTempFile = new StorageTempFile(tempFileId,
															  dataStoreFileStream.Name,
															  DateTime.UtcNow,
															  dataStoreFileStream.Length,
															  fileHash, storageIndexDefinition.Id);

		WithDbContext(db =>
		{
			db.StorageTempFile.Add(storageTempFile);
			return db.SaveChanges();
		});
	}

	/// <summary>
	/// Update temp file entry in temp file table in database
	/// </summary>
	/// <param name="storageTempFile"></param>
	private string UpdateTempFileEntry(StorageTempFile storageTempFile)
	{
		WithDbContext(db =>
		{
			db.StorageTempFile.Update(storageTempFile);
			return db.SaveChanges();
		});

		return storageTempFile.FileId;
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="dataSourceName"></param>
	/// <param name="elementId"></param>
	/// <param name="fileUploadId"></param>
	/// <param name="origin"></param>
	/// <returns></returns>
	/// <exception cref="ElementNotFoundException"></exception>
	/// <exception cref="DataSourceNotFoundException"></exception>
	public DataStoreSuccessInfo UpdateFile(string dataSourceName, string elementId, string fileUploadId,
										   DataStoreOperationOrigin origin)
	{
		return UpdateElement(dataSourceName, new DataStoreElementData(elementId, new Dictionary<string, object?>()
		{
			[StorageSystemField.Id.ToString()] = elementId
		}, null, fileUploadId), origin);
	}

	public Task<DataStoreSuccessInfo> UpdateFileAsync(string definitionName, string elementId, string fileUploadId, DataStoreOperationOrigin origin)
	{
		return Task.Run(() => { return UpdateFile(definitionName, elementId, fileUploadId, origin); });
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="dataSourceName"></param>
	/// <param name="elementId"></param>
	/// <param name="origin"></param>
	/// <returns></returns>
	/// <exception cref="DataSourceNotFoundException"></exception>
	public DataStoreSuccessInfo RemoveFile(string dataSourceName, string elementId, DataStoreOperationOrigin origin)
	{
		if (_isMainConnection)
			throw new DataStoreConnectionException("Cannot remove file on main database without context!");

		_logger.Information("Remove file: " + elementId);
		StorageIndexDefinition? storageIndexDefinition = GetDefinitionWithPaths(dataSourceName);

		if (storageIndexDefinition == null)
			throw new DataSourceNotFoundException($"Could not find DataSource for name {dataSourceName}");

		if (storageIndexDefinition.StoreRevisions)
		{
			// create deleted revision + update data set
			return DeleteRevisedElement(dataSourceName, elementId, true, true, origin);
		}
		else
		{
			DataStoreElement? dataStoreElement = GetElement(dataSourceName, elementId);
			
			if (dataStoreElement == null)
				throw new ElementNotFoundException($"Could not find element with id {elementId}");

			// delete file on disk
			// file has at least a default revision id as a long stored in index entry
			var currentRevisionId = Int64.Parse(dataStoreElement.Values[StorageSystemField.SysCurrentRevision.ToString()]!.ToString()!);
			var currentPathId = Int64.Parse(dataStoreElement.Values[StorageSystemField.SysPathId.ToString()]!.ToString()!);
			string fileId = FileStore!.GetFileId(elementId, currentRevisionId, false);
			StorageFile storageFile = FileStore.ParseFileId(fileId)!;

			// search for current file on storage
			FileStore.DeleteFile(storageIndexDefinition, dataStoreElement.Values, GetContext().Identifier, storageFile, currentPathId);
			return DeleteRevisedElement(dataSourceName, elementId, true, false, origin);
		}
	}

	public Task<DataStoreSuccessInfo> RemoveFileAsync(string definitionName, string elementId, DataStoreOperationOrigin origin)
	{
		return Task.Run(() => { return RemoveFile(definitionName, elementId, origin); });
	}

	/// <summary>
	/// Add all available storage paths to index definition object
	/// </summary>
	/// <param name="dataSourceName"></param>
	/// <returns></returns>
	private StorageIndexDefinition? GetDefinitionWithPaths(string dataSourceName)
	{
		StorageIndexDefinition? storageIndexDefinition = WithDbContext(
			db =>
			{
				var definition = db.StorageIndexDefinition.Include(it => it.Fields)
					.Include(it => it.StoragePaths)
					.FirstOrDefault(it => it.Name == dataSourceName);
				return definition;
			});
		return storageIndexDefinition;
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="dataSourceName"></param>
	/// <param name="elementId"></param>
	/// <param name="dataStoreReference"></param>
	/// <param name="origin"></param>
	/// <returns></returns>
	public DataStoreSuccessInfo UpdateFileLink(string dataSourceName, string elementId, DataStoreReference dataStoreReference,
											   DataStoreOperationOrigin origin)
	{
		if (_isMainConnection)
			throw new DataStoreConnectionException("Cannot update file link on main database without context!");
		
		if (!Guid.TryParse(elementId, out var uuid))
			throw new ElementNotFoundException($"Could not parse element id as guid '{elementId}'");

		var storageIndexDefinition = GetIndexDefinition(dataSourceName)!;
		DataStoreElement element = GetElement(dataSourceName, elementId)!;
		element.Values[StorageSystemField.SysIsLink.ToString()] = true;

		long? linkedRevisionNumber = null;
		if (dataStoreReference.FileId != null)
		{
			StorageFile storageFile = FileStore!.ParseFileId(dataStoreReference.FileId)!;
			linkedRevisionNumber = storageFile.RevisionNumber;
		}

		var referenceIndexDefinition = GetIndexDefinition(dataStoreReference.DataSourceName)!;
		DataStoreElement referenceElement = GetElement(dataStoreReference.DataSourceName, dataStoreReference.ElementId)!;
		referenceElement.Values[StorageSystemField.SysIsLinked.ToString()] = true;

		// remove fake field SysIsFavourite
		element.Values.Remove(StorageSystemField.SysIsFavourite.ToString());
		referenceElement.Values.Remove(StorageSystemField.SysIsFavourite.ToString());

		StorageLink storageLink =
			new StorageLink(storageIndexDefinition.Id, element.Id, referenceIndexDefinition.Id, referenceElement.Id, linkedRevisionNumber);

		var resData = Db.ConnectionHelper.WithTransaction(it =>
		{
			WithDbContext(db =>
			{
				db.StorageLink.Add(storageLink);
				return db.SaveChanges();
			});

			_ = Db.DataHelper.Update(it, storageIndexDefinition, element.Id, element.Values, GetIndexDefinition);
			return Db.DataHelper.Update(it, referenceIndexDefinition, referenceElement.Id, referenceElement.Values, GetIndexDefinition);
		});

		return new DataStoreSuccessInfo(element.Id, DataStoreOperationType.Update);
	}

	public Task<DataStoreSuccessInfo> UpdateFileLinkAsync(string dataSourceName, string elementId, DataStoreReference fileLink, DataStoreOperationOrigin origin)
	{
		return Task.Run(() => { return UpdateFileLink(dataSourceName, elementId, fileLink, origin); });
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="dataSourceName"></param>
	/// <param name="elementId"></param>
	/// <param name="origin"></param>
	/// <returns></returns>
	public DataStoreSuccessInfo RemoveFileLink(string dataSourceName, string elementId, DataStoreOperationOrigin origin)
	{
		if (_isMainConnection)
			throw new DataStoreConnectionException("Cannot remove file link on main database without context!");
		
		if (!Guid.TryParse(elementId, out var uuid))
			throw new ElementNotFoundException($"Could not parse element id as guid '{elementId}'");

		StorageLink? storageLink = WithDbContext(
			db => db.StorageLink
				.FirstOrDefault(it => it.ElementId == elementId));

		if (storageLink != null)
		{
			var storageIndexDefinition = GetIndexDefinition(dataSourceName)!;
			DataStoreElement element = GetElement(dataSourceName, elementId)!;

			var referenceIndexDefinition = GetIndexDefinitionById(storageLink.LinkedIndexDefinitionId)!;
			DataStoreElement referenceElement = GetElement(referenceIndexDefinition.Name, storageLink.LinkedElementId)!;

			Db.ConnectionHelper.WithTransaction(it =>
			{
				// delete link entry
				WithDbContext(db =>
				{
					db.StorageLink.Remove(storageLink);
					return db.SaveChanges();
				});

				// remove IsLink flag of former link data set
				element.Values[StorageSystemField.SysIsLink.ToString()] = false;
				// remove fake field SysIsFavourite
				element.Values.Remove(StorageSystemField.SysIsFavourite.ToString());
				referenceElement.Values.Remove(StorageSystemField.SysIsFavourite.ToString());

				var resData = Db.DataHelper.Update(it, storageIndexDefinition, element.Id, element.Values, GetIndexDefinition);

				// test, if there are other link entries!
				var res = WithDbContext(
					db => db.StorageLink
						.Where(link => link.LinkedElementId == referenceElement.Id).Count());

				// if no other entries in Link table, then delete IsLinked flag at original data set
				if (res == 0)
				{
					referenceElement.Values[StorageSystemField.SysIsLinked.ToString()] = false;
					resData = Db.DataHelper.Update(it, referenceIndexDefinition, referenceElement.Id, referenceElement.Values, GetIndexDefinition);
				}

				return resData;
			});
		}

		return new DataStoreSuccessInfo(elementId, DataStoreOperationType.Delete);
	}

	public Task<DataStoreSuccessInfo> RemoveFileLinkAsync(string dataSourceName, string elementId, DataStoreOperationOrigin origin)
	{
		return Task.Run(() => { return RemoveFileLink(dataSourceName, elementId, origin); });
	}

	/// <summary>
	/// Get list of elements in database tably by query
	/// </summary>
	/// <param name="query"></param>
	/// <returns></returns>
	/// <exception cref="DataStoreQueryException"></exception>
	public DataStoreResultSet<DataStoreElement> GetElements(DataStoreQuery query)
	{
		if (_isMainConnection)
			throw new DataStoreConnectionException("Cannot get elements on main database without context!");

		StorageIndexDefinition storageIndexDefinition = WithDbContext(
			db => db.StorageIndexDefinition.Include(it => it.Fields)
				.First(it => it.Name == query.DataSourceName));

		bool hasAggregationOrGrouping = AddMissingSystemFields(query, out List<string> addedFields);
		Query sqlKataQuery =
			new FilterParser(Db, GetIndexDefinition, Db.Authentication?.Language ?? "en", Db.CustomerContext?.Language ?? DefaultLanguage).ParseQuery(
				storageIndexDefinition, query, out var usedDefinitions, out var changedFieldAliases);

		var subSet = _failedMigrationTables.Intersect(usedDefinitions).ToList();
		if (subSet.Count != 0)
			throw new DataStoreOperationException(
				$"The following tables have failed migrations and can currently not be used: {string.Join(", ", _failedMigrationTables)}. The following tables are requested: {string.Join(", ", subSet)}");

		try
		{
			var rawRes = Db.DataHelper.Get(storageIndexDefinition, sqlKataQuery, query.Fields, GetIndexDefinition);
			var resList = rawRes.ToList();
			var resCount = resList.Count;

			if (query.CountAll)
			{
				resCount = GetCountAll(storageIndexDefinition, query);
			}

			var result = new DataStoreResultSet<DataStoreElement>(resCount);
			var removeInactive = query.Fields != null && query.Fields.All(it => it.Name != StorageSystemField.SysInactiveDate.ToString());
			var collection = resList.Select(queryResultEntry =>
			{
				foreach (var changedFieldAlias in changedFieldAliases)
				{
					if (queryResultEntry.Remove(changedFieldAlias.Key, out var value))
						queryResultEntry[changedFieldAlias.Value] = value;
				}

				var idValue = (queryResultEntry.TryGetValue(StorageSystemField.Id.ToString(), out var id) ? id ?? "" : "").ToString()!;
				var groupsValue = queryResultEntry.TryGetValue(StorageSystemField.SysGroups.ToString(), out var groups) && groups != null
									  ? groups.ToArray().Select(iter => (string)iter).ToList()
									  : [];

				bool isFavourite = GetBoolForKey(queryResultEntry, StorageSystemField.SysIsFavourite.ToString());
				bool isInactive = GetBoolForKey(queryResultEntry, StorageSystemField.SysInactiveDate.ToString());
				queryResultEntry.Remove(StorageSystemField.SysIsFavourite.ToString());

				if (removeInactive)
					queryResultEntry.Remove(StorageSystemField.SysInactiveDate.ToString());
				
				foreach (var entry in queryResultEntry)
				{
					if (entry.Value is Guid guid)
					{
						queryResultEntry[entry.Key] = guid.ToString();
					}
					else if (entry.Value is IEnumerable<Guid> guidList)
					{
						queryResultEntry[entry.Key] = guidList.Select(it => it.ToString());
					}
				}

				return new DataStoreElement(idValue, queryResultEntry, groupsValue, isInactive, isFavourite);
			});

			foreach (var dataStoreElement in collection)
			{
				DataStoreElement newDataStoreElement = dataStoreElement;
				if (!hasAggregationOrGrouping)
				{
					newDataStoreElement = AddFileInfo(dataStoreElement, storageIndexDefinition);
				}

				// remove added fields for internal purposes
				foreach (var addedField in addedFields)
				{
					newDataStoreElement.Values.Remove(addedField);
				}

				result.Add(newDataStoreElement);
			}

			// remove added query fields
			foreach (var addedField in addedFields)
			{
				DataStoreQueryField? addedFieldDto = query.Fields!.FirstOrDefault(it => it.Name == addedField);
				if (addedFieldDto != null)
					query.Fields!.Remove(addedFieldDto);
			}

			return result;
		}
		catch (Exception e)
		{
			var queryString = "";
			try
			{
				queryString = Db.ConnectionHelper.GetCompiler().Compile(sqlKataQuery).RawSql;
			}
			catch (Exception)
			{
				// ignored
			}

			throw new DataStoreQueryException($"error while executing query: '{queryString}' - {e.Message}", e);
		}
	}

	private bool AddMissingSystemFields(DataStoreQuery query, out List<string> addedFields)
	{
		addedFields = new List<string>();
		// for aggregate functions, the added system fields would need to be grouped, but for what?
		// so: if aggregation, no system fields added 
		// just needed, if not all fields are selected
		if (query.Fields != null && query.Fields.Count > 0)
		{
			foreach (var dataStoreQueryField in query.Fields)
			{
				if (Regex.IsMatch(dataStoreQueryField.Name.ToLower(), "^(avg|sum|min|max|count)\\(.+"))
				{
					return true;
				}
			}

			if (query.GroupBy != null && query.GroupBy.Count > 0)
				return true;

			List<string> systemFieldsWhiteList = new List<string>
			{
				StorageSystemField.SysIsLink.ToString(),
				StorageSystemField.SysFileId.ToString(),
				StorageSystemField.SysFileName.ToString(),
				StorageSystemField.SysFileSize.ToString(),
				StorageSystemField.SysCurrentRevision.ToString(),
				StorageSystemField.SysGroups.ToString()
			};

			foreach (string systemFieldName in systemFieldsWhiteList)
			{
				var field = query.Fields.Where(it => it.Name == systemFieldName).FirstOrDefault();
				if (field == null)
				{
					DataStoreQueryField sysQueryField = new DataStoreQueryField(systemFieldName);
					query.Fields.Add(sysQueryField);
					addedFields.Add(systemFieldName);
				}
			}
		}

		return false;
	}

	public Task<DataStoreResultSet<DataStoreElement>> GetElementsAsync(DataStoreQuery query)
	{
		// Why get elements and don't use it? Removed async
		return Task.Run(() => { return GetElements(query); });
	}

	/// <summary>
	/// Get independent full count for original query including filters
	/// </summary>
	/// <param name="storageIndexDefinition"></param>
	/// <param name="query"></param>
	/// <returns></returns>
	/// <exception cref="DataStoreQueryException"></exception>
	private int GetCountAll(StorageIndexDefinition storageIndexDefinition, DataStoreQuery query)
	{
		Query sqlKataQuery =
			new FilterParser(Db, GetIndexDefinition, Db.Authentication?.Language ?? "en", Db.CustomerContext?.Language ?? DefaultLanguage).ParseQuery(
				storageIndexDefinition, query, out var usedDefinitions, out _, true);

		var subSet = _failedMigrationTables.Intersect(usedDefinitions).ToList();
		if (subSet.Count != 0)
			throw new DataStoreOperationException(
				$"The following tables have failed migrations and can currently not be used: {string.Join(", ", _failedMigrationTables)}. The following tables are requested: {string.Join(", ", subSet)}");

		try
		{
			var rawRes = Db.DataHelper.Get(storageIndexDefinition, sqlKataQuery, query.Fields, GetIndexDefinition);
			return int.Parse(rawRes.First()["CountAll"]!.ToString()!);
		}
		catch (Exception e)
		{
			throw new DataStoreQueryException($"error while executing query - {e.Message}", e);
		}
	}

	/// <summary>
	/// Get revisions for query
	/// </summary>
	/// <param name="dataStoreRevisionQuery"></param>
	/// <returns></returns>
	public DataStoreResultSet<DataStoreRevisionInfo> GetRevisions(DataStoreRevisionQuery dataStoreRevisionQuery)
	{
		if (_isMainConnection)
			throw new DataStoreConnectionException("Cannot get revisions on main database without context!");

		return _revisionHelper!.GetRevisions(dataStoreRevisionQuery);
	}

	public Task<DataStoreResultSet<DataStoreRevisionInfo>> GetRevisionsAsync(DataStoreRevisionQuery query)
	{
		// Why get revisions and don't use them? Removed async
		return Task.Run(() => { return GetRevisions(query); });
	}

	/// <summary>
	/// Search in revisions for special changed field
	/// </summary>
	/// <param name="dataSourceName"></param>
	/// <param name="elementId"></param>
	/// <param name="fieldName"></param>
	/// <returns></returns>
	/// <exception cref="DataSourceNotFoundException"></exception>
	public DataStoreResultSet<DataStoreRevisionInfo> GetRevisionsByField(string dataSourceName, string elementId,
																		 string fieldName)
	{
		if (_isMainConnection)
			throw new DataStoreConnectionException("Cannot get revisions by field on main database without context!");

		var storageIndexDefinition = WithDbContext(db =>
													   db.StorageIndexDefinition
														   .Include(it => it.Fields)
														   .FirstOrDefault(it => it.Name == dataSourceName));

		if (storageIndexDefinition == null)
			throw new DataSourceNotFoundException($"Could not find DataSource for name {dataSourceName}");
		
		if (!Guid.TryParse(elementId, out var uuid))
			throw new ElementNotFoundException($"Could not parse element id as guid '{elementId}'");

		return _revisionHelper!.GetRevisionsByField(storageIndexDefinition, elementId, fieldName);
	}

	public Task<DataStoreResultSet<DataStoreRevisionInfo>> GetRevisionsByFieldAsync(string dataSourceName, string elementId, string fieldName)
	{
		// Why get revisions and don't use them? Removed async
		return Task.Run(() => { return GetRevisionsByField(dataSourceName, elementId, fieldName); });
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="dataSourceName"></param>
	/// <param name="elementId"></param>
	/// <param name="revisionId"></param>
	/// <returns></returns>
	/// <exception cref="DataSourceNotFoundException"></exception>
	public DataStoreRevisionData? GetRevision(string dataSourceName, string elementId, string revisionId)
	{
		if (_isMainConnection)
			throw new DataStoreConnectionException("Cannot get revision on main database without context!");

		var storageIndexDefinition = GetIndexDefinition(dataSourceName);

		if (storageIndexDefinition == null)
			throw new DataSourceNotFoundException($"Could not find DataSource for name {dataSourceName}");
		
		if (!Guid.TryParse(elementId, out var uuid))
			throw new ElementNotFoundException($"Could not parse element id as guid '{elementId}'");

		if (storageIndexDefinition.IsView)
			throw new DataStoreQueryException("Cannot get revision from a view!");

		var rev = _revisionHelper!.GetRevision(storageIndexDefinition, elementId, revisionId);

		return rev;
	}

	public Task<DataStoreRevisionData?> GetRevisionAsync(string dataSourceName, string elementId, string revisionId)
	{
		// Why get revision and don't use it? Removed async
		return Task.Run(() => { return GetRevision(dataSourceName, elementId, revisionId); })!;
	}

	public void RemoveRevisions(string dataSourceName, string elementId)
	{
		if (_isMainConnection)
			throw new DataStoreConnectionException("Cannot remove revisions on main database without context!");

		var storageIndexDefinition = GetIndexDefinition(dataSourceName);

		if (storageIndexDefinition == null)
			throw new DataSourceNotFoundException($"Could not find DataSource for name {dataSourceName}");

		_revisionHelper!.DeleteRevisionsForElement(storageIndexDefinition, elementId);
	}

	public void RemoveRevision(string dataSourceName, string elementId, string revisionId)
	{
		if (_isMainConnection)
			throw new DataStoreConnectionException("Cannot remove revision on main database without context!");

		var storageIndexDefinition = GetIndexDefinition(dataSourceName);

		if (storageIndexDefinition == null)
			throw new DataSourceNotFoundException($"Could not find DataSource for name {dataSourceName}");

		_revisionHelper!.DeleteRevision(storageIndexDefinition, elementId, revisionId);
	}

	/// <summary>
	/// set element inactive
	/// </summary>
	/// <param name="dataSourceName"></param>
	/// <param name="elementId"></param>
	/// <returns></returns>
	public DataStoreSuccessInfo SetInactive(string dataSourceName, string elementId)
	{
		return DeOrActivate(dataSourceName, elementId, true);
	}

	/// <summary>
	/// sel element inactive async
	/// </summary>
	/// <param name="dataSourceName"></param>
	/// <param name="elementId"></param>
	/// <returns></returns>
	public Task<DataStoreSuccessInfo> SetInactiveAsync(string dataSourceName, string elementId)
	{
		return Task.Run(() => { return SetInactive(dataSourceName, elementId); });
	}

	/// <summary>
	/// activate element
	/// </summary>
	/// <param name="dataSourceName"></param>
	/// <param name="elementId"></param>
	/// <returns></returns>
	public DataStoreSuccessInfo SetActive(string dataSourceName, string elementId)
	{
		_logger.Information("Element set active: " + elementId);
		return DeOrActivate(dataSourceName, elementId, false);
	}

	/// <summary>
	/// activale element async
	/// </summary>
	/// <param name="dataSourceName"></param>
	/// <param name="elementId"></param>
	/// <returns></returns>
	public Task<DataStoreSuccessInfo> SetActiveAsync(string dataSourceName, string elementId)
	{
		return Task.Run(() => { return SetActive(dataSourceName, elementId); });
	}

	/// <summary>
	/// implementation to de-/activate element
	/// </summary>
	/// <param name="dataSourceName"></param>
	/// <param name="elementId"></param>
	/// <param name="setInactive"></param>
	/// <returns></returns>
	/// <exception cref="DataStoreConnectionException"></exception>
	/// <exception cref="DataSourceNotFoundException"></exception>
	private DataStoreSuccessInfo DeOrActivate(string dataSourceName, string elementId, bool setInactive)
	{
		if (_isMainConnection)
			throw new DataStoreConnectionException("Cannot de-/activate entry on main database without context!");

		StorageIndexDefinition? storageIndexDefinition = GetIndexDefinition(dataSourceName);
		if (storageIndexDefinition == null)
			throw new DataSourceNotFoundException($"Could not find DataSource for name {dataSourceName}");
		
		if (!Guid.TryParse(elementId, out var uuid))
			throw new ElementNotFoundException($"Could not parse element id as guid '{elementId}'");

		if (storageIndexDefinition.IsView)
			throw new DataStoreQueryException("Cannot set (in-)active on a view!");

		DateTime? inactiveDate = setInactive ? DateTime.Now : null;
		var resData = Db.ConnectionHelper.WithTransaction(it =>
		{
			// update without new revision or hash
			IDictionary<string, object?> values = new Dictionary<string, object?>();
			values.Add(StorageSystemField.SysInactiveDate.ToString()!, inactiveDate);
			return Db.DataHelper.Update(it, storageIndexDefinition, elementId, values, GetIndexDefinition, Db.Authentication?.Language ?? "en");
		});

		var res = new DataStoreSuccessInfo(elementId, DataStoreOperationType.Update)
		{
			ElementData = new DataStoreElement(elementId, resData,
											   resData[StorageSystemField.SysGroups.ToString()]!.ToArray().Select(it => (string)it).ToList())
		};

		_logger.Information("Element set active: " + elementId);
		return res;
	}

	/// <summary>
	/// add entry as favourite for user 
	/// </summary>
	/// <param name="dataSourceName"></param>
	/// <param name="elementId"></param>
	/// <returns></returns>
	public DataStoreSuccessInfo AddFavourite(string dataSourceName, string elementId)
	{
		var res = AddOrRemoveFavourite(dataSourceName, elementId, true);
		return res;
	}

	/// <summary>
	/// add entry as favourite for user async
	/// </summary>
	/// <param name="dataSourceName"></param>
	/// <param name="elementId"></param>
	/// <returns></returns>
	public Task<DataStoreSuccessInfo> AddFavouriteAsync(string dataSourceName, string elementId)
	{
		return Task.Run(() => { return AddFavourite(dataSourceName, elementId); });
	}

	/// <summary>
	/// remove entry as favourite of user
	/// </summary>
	/// <param name="dataSourceName"></param>
	/// <param name="elementId"></param>
	/// <returns></returns>
	public DataStoreSuccessInfo RemoveFavourite(string dataSourceName, string elementId)
	{
		var res = AddOrRemoveFavourite(dataSourceName, elementId, false);
		return res;
	}

	/// <summary>
	/// implementation of add/remove favourite
	/// </summary>
	/// <param name="dataSourceName"></param>
	/// <param name="elementId"></param>
	/// <param name="addUser"></param>
	/// <returns></returns>
	/// <exception cref="DataStoreConnectionException"></exception>
	/// <exception cref="DataSourceNotFoundException"></exception>
	/// <exception cref="ElementNotFoundException"></exception>
	private DataStoreSuccessInfo AddOrRemoveFavourite(string dataSourceName, string elementId, bool addUser)
	{
		if (_isMainConnection)
			throw new DataStoreConnectionException("Cannot add/remove favourite on entry of main database without context!");
		Guid userId = Db.Authentication.UserId;
		StorageIndexDefinition? storageIndexDefinition = GetIndexDefinition(dataSourceName);
		if (storageIndexDefinition == null)
			throw new DataSourceNotFoundException($"Could not find DataSource for name {dataSourceName}");
		
		if (!Guid.TryParse(elementId, out var uuid))
			throw new ElementNotFoundException($"Could not parse element id as guid '{elementId}'");

		if (storageIndexDefinition.IsView)
			throw new DataStoreQueryException("Cannot set favourite on a view!");

		var resData = Db.DataHelper.GetById(storageIndexDefinition, elementId, Db.Authentication?.Language ?? "en", GetIndexDefinition,
											null, false, true, true);

		if (resData == null || resData.Count == 0)
			throw new ElementNotFoundException($"No element found for elementid {elementId}");

		List<string> userIds =
			(resData.ContainsKey(StorageSystemField.SysFavourites.ToString()!) && resData[StorageSystemField.SysFavourites.ToString()!] != null)
				? resData[StorageSystemField.SysFavourites.ToString()]!.ToArray().ToList().Select(it => it.ToString()).ToList()!
				: new List<string>();

		var data = resData;
		resData = Db.ConnectionHelper.WithTransaction(it =>
		{
			// update without new revision or hash
			IDictionary<string, object?> valuesToSave = new Dictionary<string, object?>();

			if (addUser && !userIds.Contains(userId.ToString()))
			{
				userIds.Add(userId.ToString());
				valuesToSave.Add(StorageSystemField.SysFavourites.ToString(), userIds);
				return Db.DataHelper.Update(it, storageIndexDefinition, elementId, valuesToSave, GetIndexDefinition, Db.Authentication?.Language ?? "en");
			}
			else if (!addUser && userIds.Contains(userId.ToString()))
			{
				userIds.Remove(userId.ToString());
				valuesToSave.Add(StorageSystemField.SysFavourites.ToString(), userIds);
				return Db.DataHelper.Update(it, storageIndexDefinition, elementId, valuesToSave, GetIndexDefinition, Db.Authentication?.Language ?? "en");
			}

			// if Add + already contains or
			// if Remove + already not contains
			// do nothing
			return data;
		});

		var res = new DataStoreSuccessInfo(elementId, DataStoreOperationType.Update)
		{
			ElementData = new DataStoreElement(elementId, resData,
											   resData[StorageSystemField.SysGroups.ToString()]!.ToArray().Select(it => (string)it).ToList())
		};

		_logger.Information($"Element {elementId} " + ((addUser) ? "added" : "removed") + $" as favourite for user {userId}");
		return res;
	}

	/// <summary>
	/// remove entry as favourite of user async
	/// </summary>
	/// <param name="dataSourceName"></param>
	/// <param name="elementId"></param>
	/// <returns></returns>
	public Task<DataStoreSuccessInfo> RemoveFavouriteAsync(string dataSourceName, string elementId)
	{
		return Task.Run(() => { return RemoveFavourite(dataSourceName, elementId); });
	}


	/// <summary>
	/// List all DataSources
	/// </summary>
	/// <returns></returns>
	public List<string> GetDataSources()
	{
		if (_isMainConnection)
		{
			var localCacheDateTime = _dateTimeToCacheFor.Add(TimeSpan.FromSeconds(CacheTimeSeconds));
			if (localCacheDateTime < DateTime.UtcNow)
			{
				ViewHelper.MigrateViews(this, _logger);
				_dateTimeToCacheFor = DateTime.UtcNow;
			}
		}

		return WithDbContext(db => db.StorageIndexDefinition.Select(it => it.Name).ToList());
	}


	/// <summary>
	/// Get DataSource by given name
	/// </summary>
	/// <param name="name"></param>
	/// <param name="allFields"></param>
	/// <returns></returns>
	public StorageDataSource? GetDataSource(string name, bool allFields = true)
	{
		var definition = GetIndexDefinition(name);

		// remove favourites field
		if (!allFields && definition != null && !definition.Fields.IsNullOrEmpty())
			definition.Fields = definition.Fields.Where(it => it.Name != StorageSystemField.SysFavourites.ToString()).ToList();

		return definition?.ToDto();
	}

	/// <summary>
	/// Load index definition and paths
	/// </summary>
	/// <param name="name"></param>
	/// <returns></returns>
	public StorageIndexDefinition? GetIndexDefinition(string name)
	{
		name = name.Replace("customer#", "customer__");
		var def = WithDbContext(db =>
		{
			var definition = db.StorageIndexDefinition
				.Include(it => it.Fields)
				.Include(it => it.StoragePaths)
				.FirstOrDefault(it => it.Name == name);
			if (definition == null)
				return null;

			return definition;
		});
		return def;
	}

	/// <summary>
	/// Load index definition and paths
	/// </summary>
	/// <param name="id"></param>
	/// <returns></returns>
	private StorageIndexDefinition? GetIndexDefinitionById(long? id)
	{
		var def = WithDbContext(db =>
		{
			var definition = db.StorageIndexDefinition
				.Include(it => it.Fields)
				.Include(it => it.StoragePaths)
				.FirstOrDefault(it => it.Id == id);
			if (definition == null)
				return null;

			return definition;
		});
		return def;
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="definition"></param>
	/// <returns></returns>
	public StorageDataSource CreateDataSource(StorageDataSourceConfig definition)
	{
		return CreateDataSource(definition, false);
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="definition"></param>
	/// <param name="migrate"></param>
	/// <returns></returns>
	/// <exception cref="DataStoreOperationException"></exception>
	public StorageDataSource CreateDataSource(StorageDataSourceConfig definition, bool migrate)
	{
		string datasourceRegex = "^[\\p{L}_][\\p{L}\\p{N}@$#_]{0,62}$";
		if (!new Regex(datasourceRegex).IsMatch(definition.Name))
			throw new DataStoreOperationException($"The datasource name '{definition.Name}' does not comply to the regex {datasourceRegex}");
		StorageIndexDefinition storageIndexDefinition = new(definition);
		storageIndexDefinition.InitFields();

		// true at any time but main connection
		if ((Db.CustomerContext != null) && !migrate)
		{
			storageIndexDefinition.CustomerSpecific = true;
			storageIndexDefinition.ExternalName = storageIndexDefinition.Name;
			storageIndexDefinition.Name = storageIndexDefinition.Name.Replace("customer#", "customer__");
		}

		var storagePath = string.IsNullOrWhiteSpace(definition.StoragePath)
							  ? new StoragePath(storageIndexDefinition.Name)
							  : new StoragePath(definition.StoragePath.Trim());

		storageIndexDefinition.FulltextSearch = definition.FulltextSearch;

		var changeEntry = WithDbContextTransaction((db, transaction, scope) =>
		{
			// https://stackoverflow.com/a/40075232 handle circular reference 
			db.StorageIndexDefinition.Add(storageIndexDefinition);

			storageIndexDefinition.StoragePaths.Add(storagePath);
			db.SaveChanges();
			storageIndexDefinition.CurrentStoragePath = storagePath;

			db.SaveChanges();

			Db.Migrator.Create(storageIndexDefinition, scope);

			if (_isMainConnection)
			{
				return FillChangeEntry(db, SchemaChangeOperation.CreateDataSource, Db.Authentication?.Username ?? "no auth provided",
									   storageIndexDefinition.Name,
									   storageIndexDefinition);
			}

			return null;
		});

		if (storageIndexDefinition.FulltextSearch)
		{
			var task = Fullsync(storageIndexDefinition);
			task.ConfigureAwait(false);
			task.Wait(TimeSpan.FromHours(1));
		}

		if (changeEntry != null)
		{
			var migrationTask = _migrationHelper.RunMigrationsInCustomerDatabases(this, _storageConnectionFactory, changeEntry);
			migrationTask.ConfigureAwait(false);
			if (!migrationTask.Wait(30000))
				_logger.Error("Timeout while waiting for {methodName}, {storageIndexDefinition}", nameof(MigrationHelper.RunMigrationsInCustomerDatabases),
							  storageIndexDefinition);
		}

		// remove favourites field
		storageIndexDefinition!.Fields = storageIndexDefinition.Fields.Where(it => it.Name != StorageSystemField.SysFavourites.ToString()).ToList();
		return storageIndexDefinition.ToDto();
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="dataSourceConfig"></param>
	/// <returns></returns>
	public StorageDataSource UpdateDataSource(StorageDataSourceConfig dataSourceConfig)
	{
		return UpdateDataSource(dataSourceConfig, false);
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="dataSourceConfig"></param>
	/// <param name="migrate"></param>
	/// <returns></returns>
	/// <exception cref="DataSourceNotFoundException"></exception>
	public StorageDataSource UpdateDataSource(StorageDataSourceConfig dataSourceConfig, bool migrate)
	{
		// true at any time but main connection
		if ((Db.CustomerContext != null) && !migrate)
		{
			var tempStorageIndexDefinition = new StorageIndexDefinition(dataSourceConfig);
			tempStorageIndexDefinition.CustomerSpecific = true;
			tempStorageIndexDefinition.ExternalName = dataSourceConfig.Name;
			tempStorageIndexDefinition.Name = dataSourceConfig.Name.Replace("customer#", "customer__");

			if (!string.IsNullOrWhiteSpace(dataSourceConfig.StoragePath))
			{
				StoragePath storagePath = new StoragePath(dataSourceConfig.StoragePath.Trim());
				tempStorageIndexDefinition.CurrentStoragePath = storagePath;
			}

			dataSourceConfig = tempStorageIndexDefinition.ToDto();
		}

		StorageIndexDefinition? storageIndexDefinition = null;
		bool addFulltextSearch = false;
		var changeEntry = WithDbContextTransaction((db, transaction, scope) =>
		{
			storageIndexDefinition = db.StorageIndexDefinition.Include(it => it.Fields)
				.Include(it => it.StoragePaths)
				.Include(it => it.CurrentStoragePath)
				.FirstOrDefault(it => it.Name == dataSourceConfig.Name);
			if (storageIndexDefinition == null)
				throw new DataSourceNotFoundException($"Could not find DataSource for name {dataSourceConfig.Name}");

			storageIndexDefinition.StoreRevisions = dataSourceConfig.StoreRevisions;
			storageIndexDefinition.StoreFileContent = dataSourceConfig.StoreFileContent;
			storageIndexDefinition.IsView = dataSourceConfig.IsView;
			storageIndexDefinition.ViewDefinition = dataSourceConfig.ViewDefinition;

			bool removeFulltextSearch = storageIndexDefinition.FulltextSearch && !dataSourceConfig.FulltextSearch;
			addFulltextSearch = !storageIndexDefinition.FulltextSearch && dataSourceConfig.FulltextSearch;
			storageIndexDefinition.FulltextSearch = dataSourceConfig.FulltextSearch;

			var newStoragePath = dataSourceConfig.StoragePath?.Trim();

			if (!string.IsNullOrWhiteSpace(newStoragePath) &&
				(storageIndexDefinition.CurrentStoragePath == null || !storageIndexDefinition.CurrentStoragePath!.Path.Equals(newStoragePath)))
			{
				var path = db.StoragePath.FirstOrDefault(it => it.StorageIndexDefinitionId == storageIndexDefinition.Id && it.Path == newStoragePath);
				if (path == null)
				{
					StoragePath storagePath = new StoragePath(newStoragePath);
					storageIndexDefinition.StoragePaths.Add(storagePath);
					db.SaveChanges();
					storageIndexDefinition.CurrentStoragePath = storagePath;
				}
				else
				{
					storageIndexDefinition.CurrentStoragePath = path;
				}
			}

			db.SaveChanges();

			if (removeFulltextSearch)
			{
				Db.Migrator.RemoveFulltextSearch(storageIndexDefinition, scope);
			}

			if (addFulltextSearch)
			{
				Db.Migrator.AddFulltextSearch(storageIndexDefinition, storageIndexDefinition.Fields, scope);
			}

			if (_isMainConnection)
			{
				return FillChangeEntry(db, SchemaChangeOperation.UpdateDataSource, Db.Authentication?.Username ?? "no auth provided",
									   storageIndexDefinition.Name,
									   storageIndexDefinition);
			}

			return null;
		});

		if (addFulltextSearch)
		{
			var task = Fullsync(storageIndexDefinition!);
			task.ConfigureAwait(false);
			task.Wait(TimeSpan.FromHours(1));
		}

		if (changeEntry != null)
		{
			var migrationTask = _migrationHelper.RunMigrationsInCustomerDatabases(this, _storageConnectionFactory, changeEntry);
			migrationTask.ConfigureAwait(false);
			if (!migrationTask.Wait(30000))
				_logger.Error("Timeout while waiting for {methodName}, {storageIndexDefinition}", nameof(MigrationHelper.RunMigrationsInCustomerDatabases),
							  storageIndexDefinition);
		}

		return storageIndexDefinition!.ToDto();
	}

	public bool RemoveDataSource(string definitionName)
	{
		return RemoveDataSource(definitionName, false);
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="definitionName"></param>
	/// <param name="migrate"></param>
	/// <returns></returns>
	/// <exception cref="DataSourceNotFoundException"></exception>
	public bool RemoveDataSource(string definitionName, bool migrate)
	{
		// true at any time but main connection
		if ((Db.CustomerContext != null) && !migrate)
		{
			definitionName = definitionName.Replace("customer#", "customer__");
		}

		StorageIndexDefinition? def = null;
		var schemaChange = WithDbContextTransaction((db, transaction, scope) =>
		{
			def = db.StorageIndexDefinition.Include(it => it.Fields)
				.FirstOrDefault(it => it.Name == definitionName);

			if (def == null)
				throw new DataSourceNotFoundException($"Could not find DataSource for name {definitionName}");

			db.StorageIndexDefinition.Remove(def);
			db.SaveChanges();

			if (def.StoreRevisions)
			{
				var revDef = def.GetRevisionDefinition(true);
				if (revDef != null)
					Db.Migrator.Delete(revDef, scope);
			}

			if (def.HasMultiValueFields())
			{
				var mvfDef = def.GetMvfDefinition(true);
				if (mvfDef != null)
					Db.Migrator.Delete(mvfDef, scope);
			}

			Db.Migrator.Delete(def, scope);

			if (_isMainConnection)
				return FillChangeEntry(db, SchemaChangeOperation.RemoveDataSource, Db.Authentication?.Username ?? "no auth provided", def.Name, def);
			return null;
		});

		if (schemaChange != null)
		{
			var migrationTask = _migrationHelper.RunMigrationsInCustomerDatabases(this, _storageConnectionFactory, schemaChange);
			migrationTask.ConfigureAwait(false);
			if (!migrationTask.Wait(30000))
				_logger.Error("Timeout while waiting for {methodName}, {storageIndexDefinition}", nameof(MigrationHelper.RunMigrationsInCustomerDatabases),
							  def);
		}

		return true;
	}

	/// <summary>
	/// internal to validate fields name
	/// </summary>
	/// <param name="fieldName"></param>
	/// <returns></returns>
	private List<string> CheckFieldName(string fieldName)
	{
		List<string> errors = new();
		if (fieldName.Contains("."))
			errors.Add("Field cannot contain '.'");
		if (fieldName.Contains("__"))
			errors.Add("Field cannot contain '__'");

		errors.AddRange(from calcOperation in "*/-+"
						where fieldName.Contains(calcOperation)
						select $"Field cannot contain '{calcOperation}' as this is reserved for algebraic operations.");

		if (fieldName.Contains("(") || fieldName.Contains(")"))
			errors.Add("Field cannot contain '(' or ')'");

		if (fieldName.Length > 63)
		{
			errors.Add("Field name too long (max 63 characters)");
		}

		var regex = "^(?!.*__)(customer#)?[a-zA-Z0-9_]+$";
		if (!Regex.IsMatch(fieldName, regex))
			errors.Add("Field does not match regex: " + regex);

		return errors;
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="definitionName"></param>
	/// <param name="fieldDefinition"></param>
	/// <returns></returns>
	public StorageField CreateField(string definitionName, StorageFieldConfig fieldDefinition)
	{
		return CreateField(definitionName, fieldDefinition, false);
	}

	/// <summary>
	/// Creates data field in datasource depending on migrate flag
	/// </summary>
	/// <param name="definitionName"></param>
	/// <param name="fieldDefinition"></param>
	/// <param name="migrate"></param>
	/// <returns></returns>
	/// <exception cref="DataSourceNotFoundException"></exception>
	/// <exception cref="DataStoreOperationException"></exception>
	public StorageField CreateField(string definitionName, StorageFieldConfig fieldDefinition, bool migrate)
	{
		var errors = CheckFieldName(fieldDefinition.Name);
		if (errors.Count > 0)
			throw new DataStoreOperationException($"Unable to create new field: {fieldDefinition.Name}; FieldName invalid: {string.Join("; ", errors)}");

		StorageFieldDefinitionOrm storageFieldDefinitionOrm = new(fieldDefinition);

		// true at any time but main connection
		if ((Db.CustomerContext != null) && !migrate)
		{
			storageFieldDefinitionOrm.CustomerSpecific = true;
			storageFieldDefinitionOrm.ExternalName = storageFieldDefinitionOrm.Name;
			storageFieldDefinitionOrm.Name = storageFieldDefinitionOrm.Name.Replace("customer#", "customer__");
			definitionName = definitionName.Replace("customer#", "customer__");
		}

		var fields = new List<StorageFieldDefinitionOrm>() { storageFieldDefinitionOrm };

		TransposeTranslatableField(storageFieldDefinitionOrm, fields);

		StorageIndexDefinition? def = WithDbContext(db =>
		{
			var def = db.StorageIndexDefinition
				.Include(it => it.Fields)
				.FirstOrDefault(it => it.Name == definitionName);
			return def;
		});

		if (def == null)
			throw new DataSourceNotFoundException($"Could not find DataSource for name {definitionName}");

		if (storageFieldDefinitionOrm.LookupSource != null)
		{
			var lookupSource = GetDataSource(storageFieldDefinitionOrm.LookupSource);
			if (lookupSource == null)
				throw new DataStoreConfigurationException(
					$"LookupSource {storageFieldDefinitionOrm.LookupSource} does not exist. Cannot create field {storageFieldDefinitionOrm.Name}");

			storageFieldDefinitionOrm.Type = DataStoreFieldType.Guid;
		}

		if (storageFieldDefinitionOrm.LookupSource != null && storageFieldDefinitionOrm.MultiValue)
		{
			storageFieldDefinitionOrm.LookupSourceMappingTable = GetLookupSourceTableName(definitionName, storageFieldDefinitionOrm.LookupSource);
		}

		StorageSchemaChange? changeEntry = WithDbContextTransaction((db, transaction, scope) =>
		{
			if (!def.IsView)
				Db.Migrator.Create(def, fields, scope);
			var definition = db.StorageIndexDefinition
				.Include(it => it.Fields)
				.FirstOrDefault(it => it.Name == definitionName)!;
			definition.Fields.AddRange(fields);
			db.SaveChanges();

			if (_isMainConnection)
				return FillChangeEntry(db, SchemaChangeOperation.CreateField, Db.Authentication?.Username ?? "no auth provided", definitionName,
									   fieldDefinition);
			return null;
		});

		if (changeEntry != null)
		{
			var migrationTask =
				_migrationHelper.RunMigrationsInCustomerDatabases(this, _storageConnectionFactory, changeEntry);
			migrationTask.ConfigureAwait(false);
			if (!migrationTask.Wait(30000))
				_logger.Error(
					"Timeout while waiting for {methodName}, {definitionName}, {fieldDefinition}", nameof(MigrationHelper.RunMigrationsInCustomerDatabases),
					definitionName, fieldDefinition);
		}

		return storageFieldDefinitionOrm.ToDto();
	}

	private string GetLookupSourceTableName(string tableA, string tableB)
	{
		var comparisonResult = String.CompareOrdinal(tableA, tableB);
		var firstTable = comparisonResult < 0 ? tableA : tableB;
		var secondTable = comparisonResult < 0 ? tableB : tableA;
		return FilterParser.ShortenAlias($"{firstTable}_{secondTable}");
	}

	private StorageSchemaChange FillChangeEntry(StorageDatabaseContext db, SchemaChangeOperation schemaChangeOperation, string changeUser,
												string dataSourceName, object sourceObject,
												object? destinationObject = null)
	{
		StorageSchemaChange storageSchemaChange =
			new StorageSchemaChange(schemaChangeOperation, dataSourceName, sourceObject, destinationObject, changeUser);

		db.Add(storageSchemaChange);
		db.SaveChanges();
		return storageSchemaChange;
	}

	private void TransposeTranslatableField(StorageFieldDefinitionOrm storageFieldDefinitionOrm, List<StorageFieldDefinitionOrm> fields)
	{
		if (storageFieldDefinitionOrm.Translatable)
		{
			if (storageFieldDefinitionOrm.Languages.Count == 0)
				throw new DataStoreOperationException($"{nameof(storageFieldDefinitionOrm.Languages)} can not be empty for translatable fields.");
			if (!storageFieldDefinitionOrm.Languages.Contains(Db.CustomerContext?.Language ?? DefaultLanguage))
				throw new DataStoreOperationException(
					$"{nameof(storageFieldDefinitionOrm.Languages)} has to contain the language of the context: {Db.CustomerContext?.Language ?? DefaultLanguage} for translatable fields.");
			if (storageFieldDefinitionOrm.MultiValue)
				throw new DataStoreOperationException(
					$"{nameof(storageFieldDefinitionOrm.Translatable)} fields cannot be {nameof(storageFieldDefinitionOrm.MultiValue)}");
			if (storageFieldDefinitionOrm.Type is not (DataStoreFieldType.String or DataStoreFieldType.Text))
				throw new DataStoreOperationException(
					$"{nameof(storageFieldDefinitionOrm.Translatable)} fields can only be of type {DataStoreFieldType.String} or {DataStoreFieldType.Text}");

			foreach (var language in storageFieldDefinitionOrm.Languages)
			{
				StorageFieldDefinitionOrm langField = new(storageFieldDefinitionOrm.ToDto());
				langField.Name = $"{storageFieldDefinitionOrm.Name}__{language}";
				langField.ExternalName = langField.Name;
				langField.SystemField = true;
				langField.Translatable = false;
				langField.Readonly = false;
				if (language != (Db.CustomerContext?.Language ?? DefaultLanguage))
				{
					langField.Nullable = true;
				}

				fields.Add(langField);
			}

			storageFieldDefinitionOrm.Readonly = true;
		}

		SetFieldProperties(fields);
	}

	private static void SetFieldProperties(List<StorageFieldDefinitionOrm> fields)
	{
		foreach (var field in fields)
		{
			if (field is not { PrimaryKey: false, MultiValue: false, SystemField: false, Translatable: false, LookupSource: null } || field.Name.Contains("__"))
				continue;

			if (field.Type is DataStoreFieldType.Integer or DataStoreFieldType.Long or DataStoreFieldType.Double
							  or DataStoreFieldType.String or DataStoreFieldType.Text)
			{
				field.Nullable = false;
			}

			field.DefaultValue ??= field.Type switch
			{
				DataStoreFieldType.Boolean => false,
				DataStoreFieldType.Integer or DataStoreFieldType.Long or DataStoreFieldType.Double => 0,
				DataStoreFieldType.String or DataStoreFieldType.Text => "",
				DataStoreFieldType.Date or DataStoreFieldType.DateTime or DataStoreFieldType.Time =>
					((field.Nullable) ? null : SystemMethods.CurrentDateTime.ToString()),
				_ => null
			};
		}
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="dataSourceName"></param>
	/// <param name="fieldDefinition"></param>
	/// <returns></returns>
	public StorageField UpdateField(string dataSourceName, StorageFieldConfig fieldDefinition)
	{
		return UpdateField(dataSourceName, fieldDefinition, false);
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="dataSourceName"></param>
	/// <param name="fieldDefinition"></param>
	/// <param name="migrate"></param>
	/// <returns></returns>
	/// <exception cref="DataSourceNotFoundException"></exception>
	/// <exception cref="DataStoreOperationException"></exception>
	/// <exception cref="ArgumentException"></exception>
	public StorageField UpdateField(string dataSourceName, StorageFieldConfig fieldDefinition, bool migrate)
	{
		var updateBoolToString = false;
		StorageFieldDefinitionOrm fieldDefinitionOrm = new StorageFieldDefinitionOrm(fieldDefinition);
		SetFieldProperties([fieldDefinitionOrm]);

		// true at any time but main connection
		if ((Db.CustomerContext != null) && !migrate)
		{
			fieldDefinitionOrm.CustomerSpecific = true;
			fieldDefinitionOrm.ExternalName = fieldDefinitionOrm.Name;
			fieldDefinitionOrm.Name = fieldDefinitionOrm.Name.Replace("customer#", "customer__");
			dataSourceName = dataSourceName.Replace("customer#", "customer__");
		}

		StorageFieldDefinitionOrm? field = null;
		StorageSchemaChange? storageSchemaChange = WithDbContextTransaction((db, transaction, scope) =>
		{
			var def = db.StorageIndexDefinition.Include(it => it.Fields).FirstOrDefault(it => it.Name == dataSourceName);
			if (def == null)
				throw new DataSourceNotFoundException($"Could not find DataSource for name {dataSourceName}");

			field = def.Fields.FirstOrDefault(it => it.Name == fieldDefinitionOrm.Name);

			if (field == null)
				throw new DataStoreOperationException($"Cannot update unknown field {fieldDefinitionOrm.Name} for datasource {dataSourceName}");

			if (field.SystemField || fieldDefinitionOrm.PrimaryKey)
			{
				var str = "Wow, not so fast, cowboy. This field is mine!";
				throw new ArgumentException(str +
											" If you really, really want to change this field, you will have to notify your trusted storage developer so that this lock might be removed. Best of luck.");
			}

			if (fieldDefinition.LookupSource != field.LookupSource || fieldDefinition.LookupSourceField != field.LookupSourceField)
			{
				throw new DataStoreConfigurationException(
					$"{nameof(fieldDefinition.LookupSource)} and {nameof(fieldDefinition.LookupSourceField)} cannot be changed." +
					$" Field: '{fieldDefinition.Name}', previous values: '{field.LookupSource}'.'{field.LookupSourceField}', new values: '{fieldDefinition.LookupSource}'.'{fieldDefinition.LookupSourceField}'");
			}

			if (field.MultiValue && !fieldDefinition.MultiValue)
				throw new DataStoreConfigurationException(
					$"{nameof(fieldDefinition.MultiValue)} cannot be changed from multivalue to normal field. Field: '{fieldDefinition.Name}'.");

			if (fieldDefinition.LookupSource != null)
				fieldDefinitionOrm.Type = DataStoreFieldType.Guid;

			LosslesCheck(field, fieldDefinitionOrm);

			// end type Text needs additional step to string type before
			if (fieldDefinitionOrm.Type != field.Type && fieldDefinition.Type == DataStoreFieldType.Text && field.Type != DataStoreFieldType.String)
			{
				StorageFieldDefinitionOrm tempField = fieldDefinitionOrm;
				tempField.Type = DataStoreFieldType.String;
				tempField.Length = 50;
				UpdateField(dataSourceName, tempField.ToDto());
			}

			if (!field.Translatable && fieldDefinition.Translatable)
			{
				var newFields = new List<StorageFieldDefinitionOrm>();
				TransposeTranslatableField(new(fieldDefinition), newFields);
				var defaultField = $"{fieldDefinition.Name}__{Db.CustomerContext?.Language ?? DefaultLanguage}";
				Db.Migrator.Rename(def, new Dictionary<string, string>() { { field.Name, defaultField } }, scope);
				Db.Migrator.Create(def, newFields.Where(it => it.Name != defaultField).ToList(), scope);
				def.Fields.AddRange(newFields);
				def.Fields.First(it => it.Name == field.Name).Translatable = true;
			}
			else if (field.Translatable && !fieldDefinition.Translatable)
			{
				var oldFields = new List<StorageFieldDefinitionOrm>();
				TransposeTranslatableField(field, oldFields);
				var oldFieldNames = oldFields.Select(it => it.Name).ToList();
				var defaultField = $"{field.Name}__{Db.CustomerContext?.Language ?? DefaultLanguage}";
				Db.Migrator.Rename(def, new Dictionary<string, string>() { { defaultField, field.Name } }, scope);
				Db.Migrator.Delete(def, oldFields.Where(it => it.Name != defaultField).ToList(), scope);
				def.Fields.RemoveAll(it => oldFieldNames.Contains(it.Name));
			}
			else if (field.Translatable && fieldDefinition.Translatable)
			{
				var oldFields = new List<StorageFieldDefinitionOrm>();
				TransposeTranslatableField(field, oldFields);
				var oldFieldNames = oldFields.Select(it => it.Name).ToList();

				updateBoolToString = (field.Type == DataStoreFieldType.Boolean && fieldDefinition.Type == DataStoreFieldType.String);
				field.FromStorageFieldConfig(fieldDefinition);

				var newFields = new List<StorageFieldDefinitionOrm>();
				TransposeTranslatableField(field, newFields);
				var newFieldNames = newFields.Select(it => it.Name).ToList();

				var toUpdate = newFields.Where(it => oldFieldNames.Contains(it.Name)).ToList();
				newFields.RemoveAll(it => oldFieldNames.Contains(it.Name));
				oldFields.RemoveAll(it => newFieldNames.Contains(it.Name));

				oldFieldNames = oldFields.Select(it => it.Name).ToList();
				def.Fields.RemoveAll(it => oldFieldNames.Contains(it.Name));
				def.Fields.AddRange(newFields);
				Db.Migrator.Update(def, toUpdate.ToDictionary(it => it.Name, it => it), scope);
				Db.Migrator.Delete(def, oldFields, scope);
				Db.Migrator.Create(def, newFields, scope);
			}

			updateBoolToString = (field.Type == DataStoreFieldType.Boolean && fieldDefinition.Type == DataStoreFieldType.String);
			field.FromStorageFieldConfig(fieldDefinitionOrm.ToDto());
			field.Name = fieldDefinitionOrm.Name;

			db.SaveChanges();

			try
			{
				if (!def.IsView)
					Db.Migrator.Update(GetIndexDefinition(dataSourceName)!,
									   new Dictionary<string, StorageFieldDefinitionOrm>() { { field.Name, fieldDefinitionOrm } }, scope);
			}
			catch (Exception e)
			{
				throw new DataStoreOperationException($"Error while updating field for datasource {dataSourceName}: {fieldDefinition.Name} - {e.Message}", e);
			}

			if (_isMainConnection)
				return FillChangeEntry(db, SchemaChangeOperation.UpdateField, Db.Authentication?.Username ?? "no auth provided", dataSourceName,
									   fieldDefinition);
			return null;
		});


		if (_isMainConnection)
		{
			if (storageSchemaChange != null)
			{
				var migrationTask =
					_migrationHelper.RunMigrationsInCustomerDatabases(this, _storageConnectionFactory, storageSchemaChange);
				migrationTask.ConfigureAwait(false);
				if (!migrationTask.Wait(30000))
					_logger.Error(
						"Timeout while waiting for {methodName}, {dataSourceName}, {fieldDefinition}", nameof(MigrationHelper.RunMigrationsInCustomerDatabases),
						dataSourceName, fieldDefinition);
			}
		}

		return field!.ToDto();
	}

	/// <summary>
	/// checks possible loss of data and maybe rejects
	/// </summary>
	/// <param name="fromField"></param>
	/// <param name="toField"></param>
	/// <returns></returns>
	private bool LosslesCheck(StorageFieldDefinitionOrm fromField, StorageFieldDefinitionOrm toField)
	{
		// if no change in data type, no problem and return
		if (fromField.Type == toField.Type)
			return true;

		var str = $"Type conversion from {fromField.Type} to {toField.Type} is not possible without possible loss of data!";

		if (fromField.MultiValue)
			str = "Multi value field types cannot be changed.";

		if (
			(fromField.Type == DataStoreFieldType.Integer && toField.Type == DataStoreFieldType.Long)
			|| (fromField.Type == DataStoreFieldType.Long && toField.Type == DataStoreFieldType.Double)
			|| (fromField.Type == DataStoreFieldType.Integer && toField.Type == DataStoreFieldType.Double)
			|| (fromField.Type == DataStoreFieldType.Long && toField.Type == DataStoreFieldType.String)
			|| (fromField.Type == DataStoreFieldType.Double && toField.Type == DataStoreFieldType.String)
			|| (fromField.Type == DataStoreFieldType.DateTime && toField.Type == DataStoreFieldType.String)
			|| (fromField.Type == DataStoreFieldType.Integer && toField.Type == DataStoreFieldType.String)
			|| (fromField.Type == DataStoreFieldType.Boolean && toField.Type == DataStoreFieldType.String)
			|| (fromField.Type == DataStoreFieldType.Date && toField.Type == DataStoreFieldType.String)
			|| (fromField.Type == DataStoreFieldType.Time && toField.Type == DataStoreFieldType.String)
			|| (fromField.Type == DataStoreFieldType.String && toField.Type == DataStoreFieldType.Text)
			|| (fromField.Type == DataStoreFieldType.DateTime && toField.Type == DataStoreFieldType.Text)
			|| (fromField.Type == DataStoreFieldType.Integer && toField.Type == DataStoreFieldType.Text)
			|| (fromField.Type == DataStoreFieldType.Long && toField.Type == DataStoreFieldType.Text)
			|| (fromField.Type == DataStoreFieldType.Double && toField.Type == DataStoreFieldType.Text)
			|| (fromField.Type == DataStoreFieldType.Boolean && toField.Type == DataStoreFieldType.Text)
			|| (fromField.Type == DataStoreFieldType.Date && toField.Type == DataStoreFieldType.Text)
			|| (fromField.Type == DataStoreFieldType.Time && toField.Type == DataStoreFieldType.Text)
			// allow modification of translatable fields... enable, disable, change defaultLanguage or Languages
			|| (fromField.Type == DataStoreFieldType.String && toField.Type == DataStoreFieldType.String)
			|| (fromField.Type == DataStoreFieldType.Text && toField.Type == DataStoreFieldType.Text)
		)
		{
			return true;
		}

		_logger.Error(str);
		throw new ArgumentException(str);
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="dataSourceName"></param>
	/// <param name="fieldName"></param>
	/// <param name="fieldNameNew"></param>
	/// <returns></returns>
	public StorageField RenameField(string dataSourceName, string fieldName, string fieldNameNew)
	{
		return RenameField(dataSourceName, fieldName, fieldNameNew, false);
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="dataSourceName"></param>
	/// <param name="fieldName"></param>
	/// <param name="fieldNameNew"></param>
	/// <param name="migrate"></param>
	/// <returns></returns>
	/// <exception cref="DataSourceNotFoundException"></exception>
	/// <exception cref="DataStoreOperationException"></exception>
	public StorageField RenameField(string dataSourceName, string fieldName, string fieldNameNew, bool migrate)
	{
		var errors = CheckFieldName(fieldNameNew);
		if (errors.Count > 0)
			throw new DataStoreOperationException(
				$"Unable to rename field: {fieldName} to {fieldNameNew} for datasource {dataSourceName}; FieldName invalid: {string.Join("; ", errors)}");

		// Do not delete! Needed with OLD fields!
		StorageIndexDefinition definition = GetIndexDefinition(dataSourceName)!;

		var externalFieldNameNew = fieldNameNew;
		// true at any time but main connection
		if ((Db.CustomerContext != null) && !migrate)
		{
			fieldName = fieldName.Replace("customer#", "customer__");
			fieldNameNew = fieldNameNew.Replace("customer#", "customer__");
			dataSourceName = dataSourceName.Replace("customer#", "customer__");
		}

		StorageFieldDefinitionOrm? field = null;
		var changeEntry = WithDbContextTransaction((db, transaction, scope) =>
		{
			var def = db.StorageIndexDefinition.Include(it => it.Fields).FirstOrDefault(it => it.Name == dataSourceName);
			if (def == null)
				throw new DataSourceNotFoundException($"Could not find DataSource for name {dataSourceName}");
			field = def.Fields.FirstOrDefault(it => it.Name == fieldName);
			if (field == null)
				throw new DataStoreOperationException($"Cannot rename unknown field {fieldName} for definition {dataSourceName}");

			if (field.SystemField || field.PrimaryKey)
			{
				var str = "Wow, not so fast, cowboy. This field is mine!";
				throw new ArgumentException(str +
											" If you really, really want to change this field, you will have to notify your trusted storage developer so that this lock might be removed. Best of luck.");
			}

			field.Name = fieldNameNew;
			field.ExternalName = externalFieldNameNew;
			db.SaveChanges();
			Db.Migrator.Rename(definition, new Dictionary<string, string> { { fieldName, fieldNameNew } }, scope);

			if (_isMainConnection)
				return FillChangeEntry(db, SchemaChangeOperation.RenameField, Db.Authentication?.Username ?? "no auth provided", dataSourceName,
									   fieldName, fieldNameNew);
			return null;
		});

		if (changeEntry != null)
		{
			var migrationTask =
				_migrationHelper.RunMigrationsInCustomerDatabases(this, _storageConnectionFactory, changeEntry);
			migrationTask.ConfigureAwait(false);
			if (!migrationTask.Wait(30000))
				_logger.Error(
					"Timeout while waiting for {methodName}, {definition}, {fieldName}, {fieldNameNew}",
					nameof(MigrationHelper.RunMigrationsInCustomerDatabases), definition, fieldName, fieldNameNew);
		}

		return field!.ToDto();
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="dataSourceName"></param>
	/// <param name="fieldName"></param>
	/// <returns></returns>
	public bool RemoveField(string dataSourceName, string fieldName)
	{
		return RemoveField(dataSourceName, fieldName, false);
	}

	/// <summary>
	/// 
	/// </summary>
	/// <param name="dataSourceName"></param>
	/// <param name="fieldName"></param>
	/// <param name="migrate"></param>
	/// <returns></returns>
	/// <exception cref="DataSourceNotFoundException"></exception>
	/// <exception cref="DataStoreOperationException"></exception>
	public bool RemoveField(string dataSourceName, string fieldName, bool migrate)
	{
		// true at any time but main connection
		if ((Db.CustomerContext != null) && !migrate)
		{
			fieldName = fieldName.Replace("customer#", "customer__");
			dataSourceName = dataSourceName.Replace("customer#", "customer__");
		}

		var changeEntry = WithDbContextTransaction((db, transaction, scope) =>
		{
			var definition = db.StorageIndexDefinition.Include(it => it.Fields).FirstOrDefault(it => it.Name == dataSourceName);
			if (definition == null)
				throw new DataSourceNotFoundException($"Could not find DataSource for name {dataSourceName}");

			// needs to be before deletion of field definition because of the possible LookupSource field
			var field = definition.Fields.FirstOrDefault(it => (it.Name == fieldName));

			if (field == null)
				throw new DataStoreOperationException($"Could not remove nonexisting field {fieldName} from datasource {dataSourceName}");

			if (field.SystemField || field.PrimaryKey)
			{
				var str = "Wow, not so fast, cowboy. This field is mine!";
				throw new ArgumentException(str +
											" If you really, really want to change this field, you will have to notify your trusted storage developer so that this lock might be removed. Best of luck.");
			}

			if (field.Translatable)
			{
				var toRemove = definition.Fields.Where(it => it.Name.StartsWith(field.Name + "__")).ToList();
				var toRemoveNames = toRemove.Select(it => it.Name).ToList();
				Db.Migrator.Delete(definition, toRemove, scope);
				definition.Fields.RemoveAll(it => toRemoveNames.Contains(it.Name));
				definition.Fields.Remove(field);
			}
			else
			{
				if (!definition.IsView)
					Db.Migrator.Delete(definition, new List<StorageFieldDefinitionOrm>() { field }, scope);
				definition.Fields.Remove(field);
			}

			db.SaveChanges();

			if (_isMainConnection)
				return FillChangeEntry(db, SchemaChangeOperation.RemoveField, Db.Authentication?.Username ?? "no auth provided", dataSourceName,
									   fieldName);
			return null;
		});

		if (changeEntry != null)
		{
			var migrationTask = _migrationHelper.RunMigrationsInCustomerDatabases(this, _storageConnectionFactory, changeEntry);
			migrationTask.ConfigureAwait(false);
			if (!migrationTask.Wait(30000))
				_logger.Error(
					"Timeout while waiting for {methodName}, {dataSourceName}, {fieldName}", nameof(MigrationHelper.RunMigrationsInCustomerDatabases),
					dataSourceName, fieldName);
		}

		return true;
	}

	#endregion

	public async Task<bool> Fullsync(StorageIndexDefinition sid)
	{
		if (Db.ElasticClient == null)
			return true;

		var fields = sid.Fields.ToDictionary(field => field.Name);
		var iteration = 0;
		var limit = 10000;
		while (true)
		{
			var query = new DataStoreQuery(sid.Name, null);
			query.WithPaging(limit, limit * iteration);
			var data = await GetElementsAsync(query);

			// remove fake field SysIsFavourite
			foreach (DataStoreElement dataStoreElement in data)
			{
				dataStoreElement.Values.Remove(StorageSystemField.SysIsFavourite.ToString());
			}

			if (data.Count == 0)
				break;
			var values = data.Select(d => d.Values.Where(it => SqlDataHelper.ElasticFieldInclude(fields[it.Key])).ToDictionary());
			await Db.ElasticClient.BulkAsync(
				descriptor => descriptor.IndexMany(values, (operationDescriptor, c) =>
													   operationDescriptor.Index(sid.ElasticName(GetContext()))
														   .Id(c[MultiValueFieldDefinition.Id]!.ToString()!)
														   .Version(long.Parse(c[StorageSystemField.SysCurrentRevision.ToString()] + ""))
														   .VersionType(VersionType.External))).ConfigureAwait(false);
			iteration++;
			if (data.Count < limit)
				break;
		}

		return true;
	}
}