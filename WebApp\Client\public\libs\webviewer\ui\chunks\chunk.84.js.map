{"version": 3, "sources": ["webpack:///./src/ui/src/components/ContentEditLinkModal/ContentEditLinkModal.js", "webpack:///./src/ui/src/components/ContentEditLinkModal/ContentEditLinkModalContainer.js", "webpack:///./src/ui/src/components/ContentEditLinkModal/index.js"], "names": ["ContentEditLinkModal", "closeModal", "addLinkHandler", "existingLink", "useState", "url", "setURL", "t", "useTranslation", "addURLLink", "DataElementWrapper", "className", "data-element", "DataElements", "CONTENT_EDIT_LINK_MODAL", "onMouseDown", "e", "stopPropagation", "<PERSON><PERSON>", "img", "onClick", "title", "onSubmit", "htmlFor", "id", "value", "autoFocus", "onChange", "target", "dataElement", "label", "disabled", "length", "ContentEditLinkModalContainer", "useSelector", "state", "selectors", "isElementOpen", "getContentBoxEditor", "isOpen", "contentBoxEditor", "dispatch", "useDispatch", "existingLinkUrl", "hyperlink", "useCallback", "actions", "closeElement", "blur", "addLink", "insertHyperlink"], "mappings": "mwCAQA,IA2DeA,EA3Dc,SAAH,GAIpB,IAHJC,EAAU,EAAVA,WACAC,EAAc,EAAdA,eACAC,EAAY,EAAZA,aAE4C,IAAtBC,mBAASD,GAAa,GAArCE,EAAG,KAAEC,EAAM,KACXC,EAAqB,EAAhBC,cAAgB,GAApB,GAEFC,EAAa,WACjBP,EAAeG,GACfJ,KAGF,OACE,kBAACS,EAAA,EAAkB,CACjBC,UAAU,kBACVC,eAAcC,IAAaC,wBAC3BC,YAAad,GAEb,yBAAKU,UAAU,YAAYI,YAAa,SAACC,GAAC,OAAKA,EAAEC,oBAC/C,yBAAKN,UAAU,oBACb,yBAAKA,UAAU,UACb,+BAAQJ,EAAE,oBACV,kBAACW,EAAA,EAAM,CAACC,IAAI,aAAaC,QAASnB,EAAYoB,MAAM,mBAGxD,yBAAKV,UAAU,aACb,yBAAKA,UAAU,cACb,yBAAKA,UAAU,gBACb,0BAAMW,SAAUb,GACd,2BAAOc,QAAQ,WAAWZ,UAAU,cAAcJ,EAAE,qBACpD,yBAAKI,UAAU,aACb,2BACEa,GAAG,WACHb,UAAU,WACVc,MAAOpB,EACPqB,WAAS,EACTC,SAAU,SAACX,GAAC,OAAKV,EAAOU,EAAEY,OAAOH,eAO7C,yBAAKd,UAAU,YACf,yBAAKA,UAAU,UACb,kBAACO,EAAA,EAAM,CACLP,UAAU,YACVkB,YAAY,mBACZC,MAAOvB,EAAE,eACTa,QAASX,EACTsB,UAAW1B,EAAI2B,a,ojCCpD3B,IAiCeC,EAjCuB,WACpC,IAME,IAHEC,aAAY,SAACC,GAAK,MAAK,CACzBC,IAAUC,cAAcF,EAAOtB,IAAaC,yBAC5CsB,IAAUE,oBAAoBH,OAC9B,GALAI,EAAM,KACNC,EAAgB,KAMZC,EAAWC,cAEbC,EAAkB,GAClBH,IACFG,EAAkBH,EAAiBI,WAGrC,IAAM3C,EAAa4C,uBAAY,WAC7BJ,EAASK,IAAQC,aAAalC,IAAaC,0BACvC0B,GACFA,EAAiBQ,SAElB,CAACR,IAEES,EAAUJ,uBAAY,SAACxC,GACvBmC,IACFA,EAAiBU,gBAAgB7C,GACjCmC,EAAiBQ,UAElB,CAACR,IAEJ,OAAOD,EAAU,kBAAC,EAAoB,CAACtC,WAAYA,EAAYC,eAAgB+C,EAAS9C,aAAcwC,IAAuB,MCnChH3C", "file": "chunks/chunk.84.js", "sourcesContent": ["import React, { useState } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport Button from 'components/Button';\nimport DataElements from 'constants/dataElement';\nimport DataElementWrapper from 'components/DataElementWrapper';\n\nimport '../LinkModal/LinkModal.scss';\n\nconst ContentEditLinkModal = ({\n  closeModal,\n  addLinkHandler,\n  existingLink,\n}) => {\n  const [url, setURL] = useState(existingLink);\n  const [t] = useTranslation();\n\n  const addURLLink = () => {\n    addLinkHandler(url);\n    closeModal();\n  };\n\n  return (\n    <DataElementWrapper\n      className=\"Modal LinkModal\"\n      data-element={DataElements.CONTENT_EDIT_LINK_MODAL}\n      onMouseDown={closeModal}\n    >\n      <div className=\"container\" onMouseDown={(e) => e.stopPropagation()}>\n        <div className=\"header-container\">\n          <div className=\"header\">\n            <label>{t('link.insertLink')}</label>\n            <Button img=\"icon-close\" onClick={closeModal} title=\"action.close\" />\n          </div>\n        </div>\n        <div className=\"tab-panel\">\n          <div className=\"panel-body\">\n            <div className=\"add-url-link\">\n              <form onSubmit={addURLLink}>\n                <label htmlFor=\"urlInput\" className=\"inputLabel\">{t('link.enterUrlAlt')}</label>\n                <div className=\"linkInput\">\n                  <input\n                    id=\"urlInput\"\n                    className=\"urlInput\"\n                    value={url}\n                    autoFocus\n                    onChange={(e) => setURL(e.target.value)}\n                  />\n                </div>\n              </form>\n            </div>\n          </div>\n        </div>\n        <div className=\"divider\"></div>\n        <div className=\"footer\">\n          <Button\n            className=\"ok-button\"\n            dataElement=\"linkSubmitButton\"\n            label={t('action.link')}\n            onClick={addURLLink}\n            disabled={!url.length}\n          />\n        </div>\n      </div>\n    </DataElementWrapper>\n  );\n};\n\nexport default ContentEditLinkModal;", "import React, { useCallback } from 'react';\nimport ContentEditLinkModal from './ContentEditLinkModal';\nimport selectors from 'selectors';\nimport DataElements from 'constants/dataElement';\nimport { useSelector, useDispatch } from 'react-redux';\nimport actions from 'src/redux/actions';\n\nconst ContentEditLinkModalContainer = () => {\n  const [\n    isOpen,\n    contentBoxEditor,\n  ] = useSelector((state) => [\n    selectors.isElementOpen(state, DataElements.CONTENT_EDIT_LINK_MODAL),\n    selectors.getContentBoxEditor(state),\n  ]);\n\n  const dispatch = useDispatch();\n\n  let existingLinkUrl = '';\n  if (contentBoxEditor) {\n    existingLinkUrl = contentBoxEditor.hyperlink;\n  }\n\n  const closeModal = useCallback(() => {\n    dispatch(actions.closeElement(DataElements.CONTENT_EDIT_LINK_MODAL));\n    if (contentBoxEditor) {\n      contentBoxEditor.blur();\n    }\n  }, [contentBoxEditor]);\n\n  const addLink = useCallback((url) => {\n    if (contentBoxEditor) {\n      contentBoxEditor.insertHyperlink(url);\n      contentBoxEditor.blur();\n    }\n  }, [contentBoxEditor]);\n\n  return isOpen ? (<ContentEditLinkModal closeModal={closeModal} addLinkHandler={addLink} existingLink={existingLinkUrl} />) : null;\n};\n\nexport default ContentEditLinkModalContainer;", "import ContentEditLinkModal from './ContentEditLinkModalContainer';\n\nexport default ContentEditLinkModal;"], "sourceRoot": ""}