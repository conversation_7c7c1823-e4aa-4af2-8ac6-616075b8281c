{"version": 3, "sources": ["webpack:///./src/ui/node_modules/core-js/modules/es.number.is-finite.js", "webpack:///./src/ui/node_modules/core-js/internals/number-is-finite.js"], "names": ["$", "target", "stat", "isFinite", "globalIsFinite", "module", "exports", "Number", "it"], "mappings": "+EAAQ,EAAQ,GAKhBA,CAAE,CAAEC,OAAQ,SAAUC,MAAM,GAAQ,CAAEC,SAJjB,EAAQ,S,qBCD7B,IAEIC,EAFS,EAAQ,IAEOD,SAK5BE,EAAOC,QAAUC,OAAOJ,UAAY,SAAkBK,GACpD,MAAoB,iBAANA,GAAkBJ,EAAeI", "file": "chunks/chunk.82.js", "sourcesContent": ["var $ = require('../internals/export');\nvar numberIsFinite = require('../internals/number-is-finite');\n\n// `Number.isFinite` method\n// https://tc39.es/ecma262/#sec-number.isfinite\n$({ target: 'Number', stat: true }, { isFinite: numberIsFinite });\n", "var global = require('../internals/global');\n\nvar globalIsFinite = global.isFinite;\n\n// `Number.isFinite` method\n// https://tc39.es/ecma262/#sec-number.isfinite\n// eslint-disable-next-line es-x/no-number-isfinite -- safe\nmodule.exports = Number.isFinite || function isFinite(it) {\n  return typeof it == 'number' && globalIsFinite(it);\n};\n"], "sourceRoot": ""}