(function(){var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.arrayIteratorImpl=function(d){var c=0;return function(){return c<d.length?{done:!1,value:d[c++]}:{done:!0}}};$jscomp.arrayIterator=function(d){return{next:$jscomp.arrayIteratorImpl(d)}};$jscomp.ASSUME_ES5=!1;$jscomp.ASSUME_NO_NATIVE_MAP=!1;$jscomp.ASSUME_NO_NATIVE_SET=!1;$jscomp.SIMPLE_FROUND_POLYFILL=!1;$jscomp.ISOLATE_POLYFILLS=!1;$jscomp.FORCE_POLYFILL_PROMISE=!1;$jscomp.FORCE_POLYFILL_PROMISE_WHEN_NO_UNHANDLED_REJECTION=!1;
$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(d,c,e){if(d==Array.prototype||d==Object.prototype)return d;d[c]=e.value;return d};$jscomp.getGlobal=function(d){d=["object"==typeof globalThis&&globalThis,d,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var c=0;c<d.length;++c){var e=d[c];if(e&&e.Math==Math)return e}throw Error("Cannot find global object");};$jscomp.global=$jscomp.getGlobal(this);
$jscomp.IS_SYMBOL_NATIVE="function"===typeof Symbol&&"symbol"===typeof Symbol("x");$jscomp.TRUST_ES6_POLYFILLS=!$jscomp.ISOLATE_POLYFILLS||$jscomp.IS_SYMBOL_NATIVE;$jscomp.polyfills={};$jscomp.propertyToPolyfillSymbol={};$jscomp.POLYFILL_PREFIX="$jscp$";var $jscomp$lookupPolyfilledValue=function(d,c,e){if(!e||null!=d){e=$jscomp.propertyToPolyfillSymbol[c];if(null==e)return d[c];e=d[e];return void 0!==e?e:d[c]}};
$jscomp.polyfill=function(d,c,e,f){c&&($jscomp.ISOLATE_POLYFILLS?$jscomp.polyfillIsolated(d,c,e,f):$jscomp.polyfillUnisolated(d,c,e,f))};$jscomp.polyfillUnisolated=function(d,c,e,f){e=$jscomp.global;d=d.split(".");for(f=0;f<d.length-1;f++){var a=d[f];if(!(a in e))return;e=e[a]}d=d[d.length-1];f=e[d];c=c(f);c!=f&&null!=c&&$jscomp.defineProperty(e,d,{configurable:!0,writable:!0,value:c})};
$jscomp.polyfillIsolated=function(d,c,e,f){var a=d.split(".");d=1===a.length;f=a[0];f=!d&&f in $jscomp.polyfills?$jscomp.polyfills:$jscomp.global;for(var k=0;k<a.length-1;k++){var g=a[k];if(!(g in f))return;f=f[g]}a=a[a.length-1];e=$jscomp.IS_SYMBOL_NATIVE&&"es6"===e?f[a]:null;c=c(e);null!=c&&(d?$jscomp.defineProperty($jscomp.polyfills,a,{configurable:!0,writable:!0,value:c}):c!==e&&(void 0===$jscomp.propertyToPolyfillSymbol[a]&&(e=1E9*Math.random()>>>0,$jscomp.propertyToPolyfillSymbol[a]=$jscomp.IS_SYMBOL_NATIVE?
$jscomp.global.Symbol(a):$jscomp.POLYFILL_PREFIX+e+"$"+a),$jscomp.defineProperty(f,$jscomp.propertyToPolyfillSymbol[a],{configurable:!0,writable:!0,value:c})))};$jscomp.initSymbol=function(){};
$jscomp.polyfill("Symbol",function(d){if(d)return d;var c=function(k,g){this.$jscomp$symbol$id_=k;$jscomp.defineProperty(this,"description",{configurable:!0,writable:!0,value:g})};c.prototype.toString=function(){return this.$jscomp$symbol$id_};var e="jscomp_symbol_"+(1E9*Math.random()>>>0)+"_",f=0,a=function(k){if(this instanceof a)throw new TypeError("Symbol is not a constructor");return new c(e+(k||"")+"_"+f++,k)};return a},"es6","es3");
$jscomp.polyfill("Symbol.iterator",function(d){if(d)return d;d=Symbol("Symbol.iterator");for(var c="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),e=0;e<c.length;e++){var f=$jscomp.global[c[e]];"function"===typeof f&&"function"!=typeof f.prototype[d]&&$jscomp.defineProperty(f.prototype,d,{configurable:!0,writable:!0,value:function(){return $jscomp.iteratorPrototype($jscomp.arrayIteratorImpl(this))}})}return d},"es6",
"es3");$jscomp.iteratorPrototype=function(d){d={next:d};d[Symbol.iterator]=function(){return this};return d};$jscomp.checkEs6ConformanceViaProxy=function(){try{var d={},c=Object.create(new $jscomp.global.Proxy(d,{get:function(e,f,a){return e==d&&"q"==f&&a==c}}));return!0===c.q}catch(e){return!1}};$jscomp.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS=!1;$jscomp.ES6_CONFORMANCE=$jscomp.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS&&$jscomp.checkEs6ConformanceViaProxy();
$jscomp.makeIterator=function(d){var c="undefined"!=typeof Symbol&&Symbol.iterator&&d[Symbol.iterator];if(c)return c.call(d);if("number"==typeof d.length)return $jscomp.arrayIterator(d);throw Error(String(d)+" is not an iterable or ArrayLike");};$jscomp.owns=function(d,c){return Object.prototype.hasOwnProperty.call(d,c)};$jscomp.MapEntry=function(){};
$jscomp.polyfill("Promise",function(d){function c(){this.batch_=null}function e(g){return g instanceof a?g:new a(function(b,h){b(g)})}if(d&&(!($jscomp.FORCE_POLYFILL_PROMISE||$jscomp.FORCE_POLYFILL_PROMISE_WHEN_NO_UNHANDLED_REJECTION&&"undefined"===typeof $jscomp.global.PromiseRejectionEvent)||!$jscomp.global.Promise||-1===$jscomp.global.Promise.toString().indexOf("[native code]")))return d;c.prototype.asyncExecute=function(g){if(null==this.batch_){this.batch_=[];var b=this;this.asyncExecuteFunction(function(){b.executeBatch_()})}this.batch_.push(g)};
var f=$jscomp.global.setTimeout;c.prototype.asyncExecuteFunction=function(g){f(g,0)};c.prototype.executeBatch_=function(){for(;this.batch_&&this.batch_.length;){var g=this.batch_;this.batch_=[];for(var b=0;b<g.length;++b){var h=g[b];g[b]=null;try{h()}catch(m){this.asyncThrow_(m)}}}this.batch_=null};c.prototype.asyncThrow_=function(g){this.asyncExecuteFunction(function(){throw g;})};var a=function(g){this.state_=0;this.result_=void 0;this.onSettledCallbacks_=[];this.isRejectionHandled_=!1;var b=this.createResolveAndReject_();
try{g(b.resolve,b.reject)}catch(h){b.reject(h)}};a.prototype.createResolveAndReject_=function(){function g(m){return function(v){h||(h=!0,m.call(b,v))}}var b=this,h=!1;return{resolve:g(this.resolveTo_),reject:g(this.reject_)}};a.prototype.resolveTo_=function(g){if(g===this)this.reject_(new TypeError("A Promise cannot resolve to itself"));else if(g instanceof a)this.settleSameAsPromise_(g);else{a:switch(typeof g){case "object":var b=null!=g;break a;case "function":b=!0;break a;default:b=!1}b?this.resolveToNonPromiseObj_(g):
this.fulfill_(g)}};a.prototype.resolveToNonPromiseObj_=function(g){var b=void 0;try{b=g.then}catch(h){this.reject_(h);return}"function"==typeof b?this.settleSameAsThenable_(b,g):this.fulfill_(g)};a.prototype.reject_=function(g){this.settle_(2,g)};a.prototype.fulfill_=function(g){this.settle_(1,g)};a.prototype.settle_=function(g,b){if(0!=this.state_)throw Error("Cannot settle("+g+", "+b+"): Promise already settled in state"+this.state_);this.state_=g;this.result_=b;2===this.state_&&this.scheduleUnhandledRejectionCheck_();
this.executeOnSettledCallbacks_()};a.prototype.scheduleUnhandledRejectionCheck_=function(){var g=this;f(function(){if(g.notifyUnhandledRejection_()){var b=$jscomp.global.console;"undefined"!==typeof b&&b.error(g.result_)}},1)};a.prototype.notifyUnhandledRejection_=function(){if(this.isRejectionHandled_)return!1;var g=$jscomp.global.CustomEvent,b=$jscomp.global.Event,h=$jscomp.global.dispatchEvent;if("undefined"===typeof h)return!0;"function"===typeof g?g=new g("unhandledrejection",{cancelable:!0}):
"function"===typeof b?g=new b("unhandledrejection",{cancelable:!0}):(g=$jscomp.global.document.createEvent("CustomEvent"),g.initCustomEvent("unhandledrejection",!1,!0,g));g.promise=this;g.reason=this.result_;return h(g)};a.prototype.executeOnSettledCallbacks_=function(){if(null!=this.onSettledCallbacks_){for(var g=0;g<this.onSettledCallbacks_.length;++g)k.asyncExecute(this.onSettledCallbacks_[g]);this.onSettledCallbacks_=null}};var k=new c;a.prototype.settleSameAsPromise_=function(g){var b=this.createResolveAndReject_();
g.callWhenSettled_(b.resolve,b.reject)};a.prototype.settleSameAsThenable_=function(g,b){var h=this.createResolveAndReject_();try{g.call(b,h.resolve,h.reject)}catch(m){h.reject(m)}};a.prototype.then=function(g,b){function h(r,A){return"function"==typeof r?function(t){try{m(r(t))}catch(u){v(u)}}:A}var m,v,w=new a(function(r,A){m=r;v=A});this.callWhenSettled_(h(g,m),h(b,v));return w};a.prototype.catch=function(g){return this.then(void 0,g)};a.prototype.callWhenSettled_=function(g,b){function h(){switch(m.state_){case 1:g(m.result_);
break;case 2:b(m.result_);break;default:throw Error("Unexpected state: "+m.state_);}}var m=this;null==this.onSettledCallbacks_?k.asyncExecute(h):this.onSettledCallbacks_.push(h);this.isRejectionHandled_=!0};a.resolve=e;a.reject=function(g){return new a(function(b,h){h(g)})};a.race=function(g){return new a(function(b,h){for(var m=$jscomp.makeIterator(g),v=m.next();!v.done;v=m.next())e(v.value).callWhenSettled_(b,h)})};a.all=function(g){var b=$jscomp.makeIterator(g),h=b.next();return h.done?e([]):new a(function(m,
v){function w(t){return function(u){r[t]=u;A--;0==A&&m(r)}}var r=[],A=0;do r.push(void 0),A++,e(h.value).callWhenSettled_(w(r.length-1),v),h=b.next();while(!h.done)})};return a},"es6","es3");
$jscomp.polyfill("Array.from",function(d){return d?d:function(c,e,f){e=null!=e?e:function(b){return b};var a=[],k="undefined"!=typeof Symbol&&Symbol.iterator&&c[Symbol.iterator];if("function"==typeof k){c=k.call(c);for(var g=0;!(k=c.next()).done;)a.push(e.call(f,k.value,g++))}else for(k=c.length,g=0;g<k;g++)a.push(e.call(f,c[g],g));return a}},"es6","es3");
$jscomp.checkStringArgs=function(d,c,e){if(null==d)throw new TypeError("The 'this' value for String.prototype."+e+" must not be null or undefined");if(c instanceof RegExp)throw new TypeError("First argument to String.prototype."+e+" must not be a regular expression");return d+""};
$jscomp.polyfill("String.prototype.endsWith",function(d){return d?d:function(c,e){var f=$jscomp.checkStringArgs(this,c,"endsWith");c+="";void 0===e&&(e=f.length);e=Math.max(0,Math.min(e|0,f.length));for(var a=c.length;0<a&&0<e;)if(f[--e]!=c[--a])return!1;return 0>=a}},"es6","es3");$jscomp.underscoreProtoCanBeSet=function(){var d={a:!0},c={};try{return c.__proto__=d,c.a}catch(e){}return!1};
$jscomp.setPrototypeOf=$jscomp.TRUST_ES6_POLYFILLS&&"function"==typeof Object.setPrototypeOf?Object.setPrototypeOf:$jscomp.underscoreProtoCanBeSet()?function(d,c){d.__proto__=c;if(d.__proto__!==c)throw new TypeError(d+" is not extensible");return d}:null;$jscomp.assign=$jscomp.TRUST_ES6_POLYFILLS&&"function"==typeof Object.assign?Object.assign:function(d,c){for(var e=1;e<arguments.length;e++){var f=arguments[e];if(f)for(var a in f)$jscomp.owns(f,a)&&(d[a]=f[a])}return d};
(function(d){function c(f){if(e[f])return e[f].exports;var a=e[f]={i:f,l:!1,exports:{}};d[f].call(a.exports,a,a.exports,c);a.l=!0;return a.exports}var e={};c.m=d;c.c=e;c.d=function(f,a,k){c.o(f,a)||Object.defineProperty(f,a,{enumerable:!0,get:k})};c.r=function(f){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(f,Symbol.toStringTag,{value:"Module"});Object.defineProperty(f,"__esModule",{value:!0})};c.t=function(f,a){a&1&&(f=c(f));if(a&8||a&4&&"object"===typeof f&&f&&f.__esModule)return f;
var k=Object.create(null);c.r(k);Object.defineProperty(k,"default",{enumerable:!0,value:f});if(a&2&&"string"!=typeof f)for(var g in f)c.d(k,g,function(b){return f[b]}.bind(null,g));return k};c.n=function(f){var a=f&&f.__esModule?function(){return f["default"]}:function(){return f};c.d(a,"a",a);return a};c.o=function(f,a){return Object.prototype.hasOwnProperty.call(f,a)};c.p="/core/office/";return c(c.s=13)})([function(d,c,e){e.d(c,"b",function(){return a});e.d(c,"a",function(){return k});var f=e(1),
a=function(g,b){Object(f.a)("disableLogs")||(b?console.warn("".concat(g,": ").concat(b)):console.warn(g))},k=function(g,b){}},function(d,c,e){e.d(c,"a",function(){return k});e.d(c,"b",function(){return g});var f={},a={flattenedResources:!1,CANVAS_CACHE_SIZE:void 0,maxPagesBefore:void 0,maxPagesAhead:void 0,disableLogs:!1,wvsQueryParameters:{},_trnDebugMode:!1,_logFiltersEnabled:null},k=function(b){return a[b]},g=function(b,h){var m;a[b]=h;null===(m=f[b])||void 0===m?void 0:m.forEach(function(v){v(h)})}},
function(d,c,e){e.d(c,"a",function(){return G});e.d(c,"b",function(){return C});var f=e(7),a=e(0),k=e(4),g=e(3),b="undefined"===typeof window?self:window,h=b.importScripts,m=!1,v=function(p,l){m||(h("".concat(b.basePath,"decode.min.js")),m=!0);p=Object(g.b)(p);p=self.BrotliDecode(p);return l?p:Object(g.a)(p)},w=function(p,l){return Object(f.a)(void 0,void 0,Promise,function(){var q;return Object(f.b)(this,function(z){switch(z.label){case 0:return m?[3,2]:[4,Object(k.a)("".concat(self.Core.getWorkerPath(),
"external/decode.min.js"),"Failed to download decode.min.js",window)];case 1:z.sent(),m=!0,z.label=2;case 2:return q=self.BrotliDecode(Object(g.b)(p)),[2,l?q:Object(g.a)(q)]}})})};(function(){function p(){this.remainingDataArrays=[]}p.prototype.processRaw=function(l){return l};p.prototype.processBrotli=function(l){this.remainingDataArrays.push(l);return null};p.prototype.GetNextChunk=function(l){this.decodeFunction||(this.decodeFunction=0===l[0]&&97===l[1]&&115===l[2]&&109===l[3]?this.processRaw:
this.processBrotli);return this.decodeFunction(l)};p.prototype.End=function(){if(this.remainingDataArrays.length){for(var l=this.arrays,q=0,z=0;z<l.length;++z)q+=l[z].length;q=new Uint8Array(q);var B=0;for(z=0;z<l.length;++z){var E=l[z];q.set(E,B);B+=E.length}return v(q,!0)}return null};return p})();var r=function(p,l,q){void 0===l&&(l=!0);void 0===q&&(q=!1);var z=new XMLHttpRequest;z.open("GET",p,l);p=q&&z.overrideMimeType;z.responseType=p?"text":"arraybuffer";p&&z.overrideMimeType("text/plain; charset=x-user-defined");
return z},A=function(p,l,q){return new Promise(function(z,B){var E=r(p,l,q);E.send();E.onload=function(){200===this.status||0===this.status?z(E.response):B(Error("Download Failed ".concat(p)))};E.onerror=function(){B(Error("Network error occurred ".concat(p)))}})},t=function(p,l){var q=l.decompressFunction,z=l.shouldOutputArray,B=l.compressedMaximum,E="undefined"!==typeof h?Date.now():null;try{var L=z?u(p):p.join("");Object(a.a)("worker","Result length is ".concat(L.length));if(L.length<B){var n=
q(L,z);Object(a.b)("There may be some degradation of performance. Your server has not been configured to serve .gz. and .br. files with the expected Content-Encoding. See https://docs.apryse.com/documentation/web/faq/content-encoding/ for instructions on how to resolve this.");h&&Object(a.a)("worker","Decompressed length is ".concat(n.length));L=n}else z||(L=Object(g.a)(L));if(h){var x=l.paths.join(", ");Object(a.a)("worker","".concat(x," Decompression took ").concat(Date.now()-E," ms"))}return L}catch(H){throw Error("Failed to decompress: ".concat(H));
}},u=function(p){p=p.reduce(function(l,q){q=new Uint8Array(q);return l.concat(Array.from(q))},[]);return new Uint8Array(p)},y=function(p){var l=!p.shouldOutputArray,q=p.paths,z=p.isAsync;z?q=Promise.all(q.map(function(B){return A(B,z,l)})).then(function(B){return t(B,p)}).catch(function(B){throw Error("Failed to fetch or decompress files: ".concat(B.message));}):(q=q.map(function(B){var E=r(B,z,l);E.send();if(200===E.status||0===E.status)return E.response;throw Error("Failed to load ".concat(B));
}),q=t(q,p));return q},G=function(p){var l=p.lastIndexOf("/");-1===l&&(l=0);var q=p.slice(l).replace(".",".br.");h||(q.endsWith(".js.mem")?q=q.replace(".js.mem",".mem"):q.endsWith(".js")&&(q=q.concat(".mem")));return p.slice(0,l)+q},D=function(p){return p.map(function(l){return G(l)})},I=function(p,l){l.decompressFunction=h?v:w;l.paths=D(p);return y(l)},J=function(p,l,q,z){return p.catch(function(B){Object(a.b)(B);return z(l,q)})},C=function(p,l,q,z){p=Array.isArray(p)?p:[p];a:{var B=[I];l={compressedMaximum:l,
isAsync:q,shouldOutputArray:z};if(l.isAsync){var E=B[0](p,l);for(q=1;q<B.length;++q)E=J(E,p,l,B[q])}else{for(q=0;q<B.length;q++){z=B[q];try{E=z(p,l);break a}catch(L){Object(a.b)(L.message)}}throw Error("None of the worker files were able to load. ");}}return E}},function(d,c,e){e.d(c,"b",function(){return f});e.d(c,"a",function(){return a});var f=function(k){if("string"===typeof k){for(var g=new Uint8Array(k.length),b=k.length,h=0;h<b;h++)g[h]=k.charCodeAt(h);return g}return k},a=function(k){if("string"!==
typeof k){for(var g="",b=0,h=k.length,m;b<h;)m=k.subarray(b,b+1024),b+=1024,g+=String.fromCharCode.apply(null,m);return g}return k}},function(d,c,e){function f(k,g,b){return new Promise(function(h){if(!k)return h();var m=b.document.createElement("script");m.type="text/javascript";m.onload=function(){h()};m.onerror=function(){g&&Object(a.b)(g);h()};m.src=k;b.document.getElementsByTagName("head")[0].appendChild(m)})}e.d(c,"a",function(){return f});var a=e(0)},function(d,c,e){function f(h,m,v,w){return a(h,
m,v,w,!!WebAssembly.instantiateStreaming,void 0,void 0).then(function(r){Object(k.a)("load","WASM compilation took ".concat(Date.now()-NaN," ms"));return r})}function a(h,m,v,w,r,A,t){A=A||Date.now();if(r&&!w)return Object(k.a)("load","Try instantiateStreaming"),fetch(Object(g.a)(h)).then(function(u){return WebAssembly.instantiateStreaming(u,m)}).catch(function(u){Object(k.a)("load","instantiateStreaming Failed ".concat(h," message ").concat(u.message));return a(h,m,v,w,!1,A,t)});r=w?w.map(function(u,
y){return"".concat(u,"PDFNetCWasm-chunk-").concat(y,".wasm")}):h;return Object(g.b)(r,v,!0,!0).then(function(u){t=Date.now();Object(k.a)("load","Request took ".concat(t-A," ms"));return WebAssembly.instantiate(u,m)})}e.d(c,"a",function(){return f});var k=e(0),g=e(2),b=e(4);e.d(c,"b",function(){return b.a})},function(d,c){c=function(){return this}();try{c=c||(new Function("return this"))()}catch(e){"object"===typeof window&&(c=window)}d.exports=c},function(d,c,e){function f(k,g,b,h){function m(v){return v instanceof
b?v:new b(function(w){w(v)})}return new (b||(b=Promise))(function(v,w){function r(u){try{t(h.next(u))}catch(y){w(y)}}function A(u){try{t(h["throw"](u))}catch(y){w(y)}}function t(u){u.done?v(u.value):m(u.value).then(r,A)}t((h=h.apply(k,g||[])).next())})}function a(k,g){function b(t){return function(u){return h([t,u])}}function h(t){if(v)throw new TypeError("Generator is already executing.");for(;A&&(A=0,t[0]&&(m=0)),m;)try{if(v=1,w&&(r=t[0]&2?w["return"]:t[0]?w["throw"]||((r=w["return"])&&r.call(w),
0):w.next)&&!(r=r.call(w,t[1])).done)return r;if(w=0,r)t=[t[0]&2,r.value];switch(t[0]){case 0:case 1:r=t;break;case 4:return m.label++,{value:t[1],done:!1};case 5:m.label++;w=t[1];t=[0];continue;case 7:t=m.ops.pop();m.trys.pop();continue;default:if(!(r=m.trys,r=0<r.length&&r[r.length-1])&&(6===t[0]||2===t[0])){m=0;continue}if(3===t[0]&&(!r||t[1]>r[0]&&t[1]<r[3]))m.label=t[1];else if(6===t[0]&&m.label<r[1])m.label=r[1],r=t;else if(r&&m.label<r[2])m.label=r[2],m.ops.push(t);else{r[2]&&m.ops.pop();m.trys.pop();
continue}}t=g.call(k,m)}catch(u){t=[6,u],w=0}finally{v=r=0}if(t[0]&5)throw t[1];return{value:t[0]?t[1]:void 0,done:!0}}var m={label:0,sent:function(){if(r[0]&1)throw r[1];return r[1]},trys:[],ops:[]},v,w,r,A=Object.create(("function"===typeof Iterator?Iterator:Object).prototype);return A.next=b(0),A["throw"]=b(1),A["return"]=b(2),"function"===typeof Symbol&&(A[Symbol.iterator]=function(){return this}),A}e.d(c,"a",function(){return f});e.d(c,"b",function(){return a})},function(d,c,e){c.a=function(){ArrayBuffer.prototype.slice||
(ArrayBuffer.prototype.slice=function(f,a){void 0===f&&(f=0);void 0===a&&(a=this.byteLength);f=Math.floor(f);a=Math.floor(a);0>f&&(f+=this.byteLength);0>a&&(a+=this.byteLength);f=Math.min(Math.max(0,f),this.byteLength);a=Math.min(Math.max(0,a),this.byteLength);if(0>=a-f)return new ArrayBuffer(0);var k=new ArrayBuffer(a-f),g=new Uint8Array(k);f=new Uint8Array(this,f,a-f);g.set(f);return k})}},function(d,c,e){e.d(c,"a",function(){return f});e(10);var f=function(a,k){return function(){}}},function(d,
c,e){c.a=function(f){var a={};decodeURIComponent(f.slice(1)).split("&").forEach(function(k){k=k.split("=",2);a[k[0]]=k[1]});return a}},function(d,c,e){e.d(c,"a",function(){return b});var f=e(2),a=e(5),k=e(12),g=function(){function h(m){var v=this;this.promise=m.then(function(w){v.response=w;v.status=200})}h.prototype.addEventListener=function(m,v){this.promise.then(v)};return h}(),b=function(h,m,v,w){if(Object(k.a)()&&!v){self.Module.instantiateWasm=function(A,t){return Object(a.a)("".concat(h,"Wasm.wasm"),
A,m["Wasm.wasm"],w).then(function(u){t(u.instance)})};if(m.disableObjectURLBlobs){importScripts("".concat(h,"Wasm.js"));return}v=Object(f.b)("".concat(h,"Wasm.js.mem"),m["Wasm.js.mem"],!1,!1)}else{if(m.disableObjectURLBlobs){importScripts("".concat((self.Module.asmjsPrefix?self.Module.asmjsPrefix:"")+h,".js"));return}v=Object(f.b)("".concat((self.Module.asmjsPrefix?self.Module.asmjsPrefix:"")+h,".js.mem"),m[".js.mem"],!1);var r=Object(f.b)("".concat((self.Module.memoryInitializerPrefixURL?self.Module.memoryInitializerPrefixURL:
"")+h,".mem"),m[".mem"],!0,!0);self.Module.memoryInitializerRequest=new g(r)}v=new Blob([v],{type:"application/javascript"});importScripts(URL.createObjectURL(v))}},function(d,c,e){e.d(c,"a",function(){return t});var f,a="undefined"===typeof window?self:window;d=function(){var u=navigator.userAgent.toLowerCase();return(u=/(msie) ([\w.]+)/.exec(u)||/(trident)(?:.*? rv:([\w.]+)|)/.exec(u))?parseInt(u[2],10):u}();var k=function(){var u=a.navigator.userAgent.match(/OPR/),y=a.navigator.userAgent.match(/Maxthon/),
G=a.navigator.userAgent.match(/Edge/);return a.navigator.userAgent.match(/Chrome\/(.*?) /)&&!u&&!y&&!G}();(function(){if(!k)return null;var u=a.navigator.userAgent.match(/Chrome\/([0-9]+)\./);return u?parseInt(u[1],10):u})();var g=!!navigator.userAgent.match(/Edge/i)||navigator.userAgent.match(/Edg\/(.*?)/)&&a.navigator.userAgent.match(/Chrome\/(.*?) /);(function(){if(!g)return null;var u=a.navigator.userAgent.match(/Edg\/([0-9]+)\./);return u?parseInt(u[1],10):u})();c=/iPad|iPhone|iPod/.test(a.navigator.platform)||
"MacIntel"===navigator.platform&&1<navigator.maxTouchPoints||/iPad|iPhone|iPod/.test(a.navigator.userAgent);var b=function(){var u=a.navigator.userAgent.match(/.*\/([0-9\.]+)\s(Safari|Mobile).*/i);return u?parseFloat(u[1]):u}(),h=/^((?!chrome|android).)*safari/i.test(a.navigator.userAgent)||/^((?!chrome|android).)*$/.test(a.navigator.userAgent)&&c;h&&/^((?!chrome|android).)*safari/i.test(navigator.userAgent)&&parseInt(null===(f=navigator.userAgent.match(/Version\/(\d+)/))||void 0===f?void 0:f[1],
10);var m=a.navigator.userAgent.match(/Firefox/);(function(){if(!m)return null;var u=a.navigator.userAgent.match(/Firefox\/([0-9]+)\./);return u?parseInt(u[1],10):u})();d||/Android|webOS|Touch|IEMobile|Silk/i.test(navigator.userAgent);navigator.userAgent.match(/(iPad|iPhone|iPod)/i);a.navigator.userAgent.indexOf("Android");var v=/Mac OS X 10_13_6.*\(KHTML, like Gecko\)$/.test(a.navigator.userAgent),w=a.navigator.userAgent.match(/(iPad|iPhone).+\sOS\s((\d+)(_\d)*)/i)?14<=parseInt(a.navigator.userAgent.match(/(iPad|iPhone).+\sOS\s((\d+)(_\d)*)/i)[3],
10):!1,r=!(!self.WebAssembly||!self.WebAssembly.validate),A=-1<a.navigator.userAgent.indexOf("Edge/16")||-1<a.navigator.userAgent.indexOf("MSAppHost"),t=function(){return r&&!A&&!(!w&&(h&&14>b||v))}},function(d,c,e){d.exports=e(14)},function(d,c,e){e.r(c);e(15);e(20);d=e(8);e(21);Object(d.a)()},function(d,c,e){(function(f,a){function k(g){"@babel/helpers - typeof";return k="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(b){return typeof b}:function(b){return b&&"function"==typeof Symbol&&
b.constructor===Symbol&&b!==Symbol.prototype?"symbol":typeof b},k(g)}(function(g){function b(){for(var n=0;n<E.length;n++)E[n][0](E[n][1]);E=[];L=!1}function h(n,x){E.push([n,x]);L||(L=!0,B(b,0))}function m(n,x){function H(F){r(x,F)}function K(F){t(x,F)}try{n(H,K)}catch(F){K(F)}}function v(n){var x=n.owner,H=x.state_;x=x.data_;var K=n[H];n=n.then;if("function"===typeof K){H=l;try{x=K(x)}catch(F){t(n,F)}}w(n,x)||(H===l&&r(n,x),H===q&&t(n,x))}function w(n,x){var H;try{if(n===x)throw new TypeError("A promises callback cannot return that same promise.");
if(x&&("function"===typeof x||"object"===k(x))){var K=x.then;if("function"===typeof K)return K.call(x,function(F){H||(H=!0,x!==F?r(n,F):A(n,F))},function(F){H||(H=!0,t(n,F))}),!0}}catch(F){return H||t(n,F),!0}return!1}function r(n,x){n!==x&&w(n,x)||A(n,x)}function A(n,x){n.state_===C&&(n.state_=p,n.data_=x,h(y,n))}function t(n,x){n.state_===C&&(n.state_=p,n.data_=x,h(G,n))}function u(n){var x=n.then_;n.then_=void 0;for(n=0;n<x.length;n++)v(x[n])}function y(n){n.state_=l;u(n)}function G(n){n.state_=
q;u(n)}function D(n){if("function"!==typeof n)throw new TypeError("Promise constructor takes a function argument");if(!(this instanceof D))throw new TypeError("Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.");this.then_=[];m(n,this)}g.createPromiseCapability=function(){var n={};n.promise=new D(function(x,H){n.resolve=x;n.reject=H});return n};var I=g.Promise,J=I&&"resolve"in I&&"reject"in I&&"all"in I&&"race"in I&&function(){var n;
new I(function(x){n=x});return"function"===typeof n}();"undefined"!==typeof exports&&exports?(exports.Promise=J?I:D,exports.Polyfill=D):"function"===typeof define&&e(19)?define(function(){return J?I:D}):J||(g.Promise=D);var C="pending",p="sealed",l="fulfilled",q="rejected",z=function(){},B="undefined"!==typeof a?a:setTimeout,E=[],L;D.prototype={constructor:D,state_:C,then_:null,data_:void 0,then:function(n,x){n={owner:this,then:new this.constructor(z),fulfilled:n,rejected:x};this.state_===l||this.state_===
q?h(v,n):this.then_.push(n);return n.then},"catch":function(n){return this.then(null,n)}};D.all=function(n){if("[object Array]"!==Object.prototype.toString.call(n))throw new TypeError("You must pass an array to Promise.all().");return new this(function(x,H){function K(P){O++;return function(Q){F[P]=Q;--O||x(F)}}for(var F=[],O=0,M=0,N;M<n.length;M++)(N=n[M])&&"function"===typeof N.then?N.then(K(M),H):F[M]=N;O||x(F)})};D.race=function(n){if("[object Array]"!==Object.prototype.toString.call(n))throw new TypeError("You must pass an array to Promise.race().");
return new this(function(x,H){for(var K=0,F;K<n.length;K++)(F=n[K])&&"function"===typeof F.then?F.then(x,H):x(F)})};D.resolve=function(n){return n&&"object"===k(n)&&n.constructor===this?n:new this(function(x){x(n)})};D.reject=function(n){return new this(function(x,H){H(n)})}})("undefined"!==typeof window?window:"undefined"!==typeof f?f:"undefined"!==typeof self?self:void 0)}).call(this,e(6),e(16).setImmediate)},function(d,c,e){(function(f){function a(b,h){this._id=b;this._clearFn=h}var k="undefined"!==
typeof f&&f||"undefined"!==typeof self&&self||window,g=Function.prototype.apply;c.setTimeout=function(){return new a(g.call(setTimeout,k,arguments),clearTimeout)};c.setInterval=function(){return new a(g.call(setInterval,k,arguments),clearInterval)};c.clearTimeout=c.clearInterval=function(b){b&&b.close()};a.prototype.unref=a.prototype.ref=function(){};a.prototype.close=function(){this._clearFn.call(k,this._id)};c.enroll=function(b,h){clearTimeout(b._idleTimeoutId);b._idleTimeout=h};c.unenroll=function(b){clearTimeout(b._idleTimeoutId);
b._idleTimeout=-1};c._unrefActive=c.active=function(b){clearTimeout(b._idleTimeoutId);var h=b._idleTimeout;0<=h&&(b._idleTimeoutId=setTimeout(function(){b._onTimeout&&b._onTimeout()},h))};e(17);c.setImmediate="undefined"!==typeof self&&self.setImmediate||"undefined"!==typeof f&&f.setImmediate||this&&this.setImmediate;c.clearImmediate="undefined"!==typeof self&&self.clearImmediate||"undefined"!==typeof f&&f.clearImmediate||this&&this.clearImmediate}).call(this,e(6))},function(d,c,e){(function(f,a){(function(k,
g){function b(C){delete y[C]}function h(C){if(G)setTimeout(h,0,C);else{var p=y[C];if(p){G=!0;try{var l=p.callback,q=p.args;switch(q.length){case 0:l();break;case 1:l(q[0]);break;case 2:l(q[0],q[1]);break;case 3:l(q[0],q[1],q[2]);break;default:l.apply(g,q)}}finally{b(C),G=!1}}}}function m(){I=function(C){a.nextTick(function(){h(C)})}}function v(){if(k.postMessage&&!k.importScripts){var C=!0,p=k.onmessage;k.onmessage=function(){C=!1};k.postMessage("","*");k.onmessage=p;return C}}function w(){var C=
"setImmediate$"+Math.random()+"$",p=function(l){l.source===k&&"string"===typeof l.data&&0===l.data.indexOf(C)&&h(+l.data.slice(C.length))};k.addEventListener?k.addEventListener("message",p,!1):k.attachEvent("onmessage",p);I=function(l){k.postMessage(C+l,"*")}}function r(){var C=new MessageChannel;C.port1.onmessage=function(p){h(p.data)};I=function(p){C.port2.postMessage(p)}}function A(){var C=D.documentElement;I=function(p){var l=D.createElement("script");l.onreadystatechange=function(){h(p);l.onreadystatechange=
null;C.removeChild(l);l=null};C.appendChild(l)}}function t(){I=function(C){setTimeout(h,0,C)}}if(!k.setImmediate){var u=1,y={},G=!1,D=k.document,I,J=Object.getPrototypeOf&&Object.getPrototypeOf(k);J=J&&J.setTimeout?J:k;"[object process]"==={}.toString.call(k.process)?m():v()?w():k.MessageChannel?r():D&&"onreadystatechange"in D.createElement("script")?A():t();J.setImmediate=function(C){"function"!==typeof C&&(C=new Function(""+C));for(var p=Array(arguments.length-1),l=0;l<p.length;l++)p[l]=arguments[l+
1];y[u]={callback:C,args:p};I(u);return u++};J.clearImmediate=b}})("undefined"===typeof self?"undefined"===typeof f?this:f:self)}).call(this,e(6),e(18))},function(d,c){function e(){throw Error("setTimeout has not been defined");}function f(){throw Error("clearTimeout has not been defined");}function a(y){if(v===setTimeout)return setTimeout(y,0);if((v===e||!v)&&setTimeout)return v=setTimeout,setTimeout(y,0);try{return v(y,0)}catch(G){try{return v.call(null,y,0)}catch(D){return v.call(this,y,0)}}}function k(y){if(w===
clearTimeout)return clearTimeout(y);if((w===f||!w)&&clearTimeout)return w=clearTimeout,clearTimeout(y);try{return w(y)}catch(G){try{return w.call(null,y)}catch(D){return w.call(this,y)}}}function g(){A&&t&&(A=!1,t.length?r=t.concat(r):u=-1,r.length&&b())}function b(){if(!A){var y=a(g);A=!0;for(var G=r.length;G;){t=r;for(r=[];++u<G;)t&&t[u].run();u=-1;G=r.length}t=null;A=!1;k(y)}}function h(y,G){this.fun=y;this.array=G}function m(){}d=d.exports={};try{var v="function"===typeof setTimeout?setTimeout:
e}catch(y){v=e}try{var w="function"===typeof clearTimeout?clearTimeout:f}catch(y){w=f}var r=[],A=!1,t,u=-1;d.nextTick=function(y){var G=Array(arguments.length-1);if(1<arguments.length)for(var D=1;D<arguments.length;D++)G[D-1]=arguments[D];r.push(new h(y,G));1!==r.length||A||a(b)};h.prototype.run=function(){this.fun.apply(null,this.array)};d.title="browser";d.browser=!0;d.env={};d.argv=[];d.version="";d.versions={};d.on=m;d.addListener=m;d.once=m;d.off=m;d.removeListener=m;d.removeAllListeners=m;d.emit=
m;d.prependListener=m;d.prependOnceListener=m;d.listeners=function(y){return[]};d.binding=function(y){throw Error("process.binding is not supported");};d.cwd=function(){return"/"};d.chdir=function(y){throw Error("process.chdir is not supported");};d.umask=function(){return 0}},function(d,c){d.exports={}},function(d,c,e){(function(f){"undefined"===typeof f.crypto&&(f.crypto={getRandomValues:function(a){for(var k=0;k<a.length;k++)a[k]=256*Math.random()}})})("undefined"===typeof window?self:window)},
function(d,c,e){function f(b){"@babel/helpers - typeof";return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(h){return typeof h}:function(h){return h&&"function"==typeof Symbol&&h.constructor===Symbol&&h!==Symbol.prototype?"symbol":typeof h},f(b)}var a=e(9),k=e(11),g=null;(function(b){function h(y){t||(t=[]);t.push(y)}var m,v,w,r,A=!1,t=[],u=function(){function y(){m=function(){}}function G(l){var q=[];return{resource_array:q,msg:JSON.stringify(l.data,function(z,B){if("object"===
f(B)&&(z=null,B instanceof Uint8Array?z=B:B instanceof ArrayBuffer&&(z=new Uint8Array(B)),z)){B=w(z.length);var E=r(B);E&&(new Uint8Array(Module.HEAPU8.buffer,E,z.length)).set(z);q.push(B);return{__trn_res_id:B}}return B})}}function D(){A=!0;postMessage({type:"abort",data:{error:"Office worker has terminated unexpectedly"}})}function I(l){if(!A)try{var q=G(l);v(q.msg)}catch(z){D(z)}}b.basePath="../";var J=b.officeWorkerPath||"";b.workerBasePath&&(b.basePath=b.workerBasePath);b.basePath=b.externalPath?
b.externalPath:b.basePath+"external/";importScripts("".concat(b.basePath,"Promise.js"));b.ContinueFunc=function(l){m("ContinueFunc called");setTimeout(function(){onmessage({data:{action:"continue"}})},l)};if(b.pdfWorkerPath)var C=b.pdfWorkerPath;if(b.officeAsmPath)var p=b.officeAsmPath;b.Module={memoryInitializerPrefixURL:C,asmjsPrefix:p,onRuntimeInitialized:function(){m||y();var l=Date.now()-g;Object(a.a)("load","time duration from start to ready: ".concat(JSON.stringify(l)));v=function(q){if(null!==
q&&void 0!==q&&0!==q&&!A){var z=(q.length<<2)+1,B=Module._malloc(z);0<stringToUTF8(q,B,z)&&Module._TRN_OnMessage(B)}};w=function(q){return Module._TRN_CreateBufferResource(q)};r=function(q){return Module._TRN_GetResourcePointer(q)};m("OnReady called");onmessage=I;Module._TRN_InitWorker();for(l=0;l<t.length;++l)onmessage(t[l]);t=null},fetchSelf:function(){g=Date.now();Object(k.a)("".concat(J,"WebOfficeWorker"),{"Wasm.wasm":5E6,"Wasm.js.mem":1E5,".js.mem":5E6,".mem":3E6,disableObjectURLBlobs:b.disableObjectURLBlobs},
!!navigator.userAgent.match(/Edge/i)||b.wasmDisabled)},onAbort:D,noExitRuntime:!0}};b.onmessage=function(y){"init"===y.data.action&&(b.wasmDisabled=!y.data.wasm,b.externalPath=y.data.externalPath,b.officeAsmPath=y.data.officeAsmPath,b.pdfWorkerPath=y.data.pdfWorkerPath,b.disableObjectURLBlobs=y.data.disableObjectURLBlobs,b.onmessage=h,u(),b.Module.fetchSelf())}})("undefined"===typeof window?self:window)}]);}).call(this || window)
