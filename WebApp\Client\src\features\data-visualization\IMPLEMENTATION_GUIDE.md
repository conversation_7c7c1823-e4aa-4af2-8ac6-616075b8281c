# Data Visualization Implementation Guide

## Quick Start

### For New Implementations

```typescript
import { createVisualization } from '@/features/data-visualization';

// Basic usage
const visualization = createVisualization('container-id', data);

// With options
const visualization = createVisualization('container-id', data, {
  config: {
    dataSourceType: 'pages',
    animations: { enableAnimations: true }
  },
  enableDebugMode: true
});
```

### For Legacy Code Migration

```typescript
// OLD (legacy service)
import { initializeVisualization } from '@/services/d3-visualisation';

// NEW (feature module)
import { initializeVisualization } from '@/features/data-visualization';
// API remains exactly the same!
```

## Migration Steps

### Step 1: Update Imports

Replace all imports of the legacy service:

```typescript
// Before
import { initializeVisualization, refreshFilterPanel } from '@/services/d3-visualisation';

// After  
import { initializeVisualization, refreshFilterPanel } from '@/features/data-visualization';
```

### Step 2: Update Service Registration (if using DI)

```typescript
// Before
container.register('VisualizationService', {
  useValue: d3VisualisationService
});

// After
import { DataSourceVisualizationNetwork } from '@/features/data-visualization';
container.register('VisualizationService', {
  useClass: DataSourceVisualizationNetwork
});
```

### Step 3: Test and Validate

1. **Functional Testing**: Ensure all features work as expected
2. **Performance Testing**: Compare rendering times and memory usage
3. **Visual Testing**: Verify output matches legacy implementation

## API Reference

### Main Classes

#### DataSourceVisualizationNetwork
The main orchestrator class for creating visualizations.

```typescript
class DataSourceVisualizationNetwork {
  constructor(containerId: string, data: DataNetwork, options?: VisualizationOptions)
  
  // Public methods
  resetVisualization(): Promise<void>
  toggleDataSourceType(type: 'pages' | 'dependantSources'): Promise<void>
  updateData(newData: DataNetwork): void
  getState(): VisualizationState
  updateConfig(newConfig: Partial<VisualizationConfig>): void
  destroy(): void
  
  // Static factory methods
  static create(containerId: string, data: DataNetwork, options?: VisualizationOptions): DataSourceVisualizationNetwork
  static createHighPerformance(containerId: string, data: DataNetwork, options?: VisualizationOptions): DataSourceVisualizationNetwork
  static createAccessible(containerId: string, data: DataNetwork, options?: VisualizationOptions): DataSourceVisualizationNetwork
}
```

#### VisualizationLegacyAPI
Provides backward compatibility with the legacy API.

```typescript
class VisualizationLegacyAPI {
  static async initializeVisualization(id: string, key: string): Promise<void>
  static async refreshFilterPanel(urlKey?: string): Promise<void>
  static hideVisualizationFilter(): void
  static getCurrentInstance(): DataSourceVisualizationNetwork | null
  static destroyCurrentInstance(): void
}
```

### Configuration Options

```typescript
interface VisualizationOptions {
  config?: Partial<VisualizationConfig>;
  initialState?: Partial<VisualizationState>;
  enableDebugMode?: boolean;
  performanceMode?: 'auto' | 'high' | 'balanced' | 'performance';
}
```

### Key Configuration Properties

```typescript
interface VisualizationConfig {
  // Container settings
  containerId: string;
  containerWidth: number;
  containerHeight: number;
  
  // Data source type
  dataSourceType: 'pages' | 'dependantSources';
  
  // Visual settings
  dimensions: {
    cardWidth: number;
    cardHeight: number;
    cardMargin: number;
  };
  
  // Link settings
  links: {
    enableBundling: boolean;
    routingType: 'simple' | 'optimized' | 'hierarchical';
    strokeWidth: number;
    arrowSize: number;
  };
  
  // Performance settings
  performance: {
    enableLOD: boolean;
    enableVirtualization: boolean;
    maxNodes: number;
  };
  
  // Animation settings
  animations: {
    enableAnimations: boolean;
    duration: number;
    easing: string;
  };
}
```

## Advanced Usage

### Custom Configuration

```typescript
import { createVisualization, ConfigUtils, DEFAULT_CONFIG } from '@/features/data-visualization';

const customConfig = ConfigUtils.mergeConfig({
  links: {
    routingType: 'hierarchical',
    enableBundling: true
  },
  animations: {
    enableAnimations: true,
    duration: 750
  },
  performance: {
    enableLOD: true,
    maxNodes: 200
  }
}, DEFAULT_CONFIG);

const visualization = createVisualization('container', data, {
  config: customConfig,
  enableDebugMode: true
});
```

### High Performance Mode

```typescript
import { createHighPerformanceVisualization } from '@/features/data-visualization';

// Optimized for large datasets
const visualization = createHighPerformanceVisualization('container', data, {
  config: {
    performance: {
      enableLOD: true,
      enableVirtualization: true,
      maxNodes: 500
    },
    animations: {
      enableAnimations: false // Disabled for performance
    }
  }
});
```

### Accessibility Mode

```typescript
import { createAccessibleVisualization } from '@/features/data-visualization';

const visualization = createAccessibleVisualization('container', data, {
  config: {
    accessibility: {
      enableKeyboardNavigation: true,
      enableScreenReader: true,
      highContrast: true
    }
  }
});
```

### Event Handling

```typescript
// The visualization automatically handles events, but you can access the state
const visualization = createVisualization('container', data);

// Get current state
const state = visualization.getState();
console.log('Current zoom level:', state.zoomLevel);

// Update configuration dynamically
visualization.updateConfig({
  links: { strokeWidth: 3 }
});
```

## Debugging and Development

### Enable Debug Mode

```typescript
import { DevTools } from '@/features/data-visualization';

// Enable debug mode
DevTools.enableDebugMode();

// Get performance metrics
const metrics = DevTools.getPerformanceMetrics();
console.log('Performance:', metrics);

// Log current state
DevTools.logCurrentState();
```

### Debug Panel

When debug mode is enabled, a debug panel appears showing:
- Node and link counts
- Active animations
- Performance metrics
- Current zoom level
- Data source type

## Performance Optimization

### For Large Datasets (>100 nodes)

1. **Use High Performance Mode**:
   ```typescript
   const viz = createHighPerformanceVisualization('container', data);
   ```

2. **Disable Animations**:
   ```typescript
   const viz = createVisualization('container', data, {
     config: { animations: { enableAnimations: false } }
   });
   ```

3. **Enable Level of Detail (LOD)**:
   ```typescript
   const viz = createVisualization('container', data, {
     config: { performance: { enableLOD: true } }
   });
   ```

### Memory Management

```typescript
// Always destroy visualizations when done
visualization.destroy();

// Or use the helper
import { MigrationHelpers } from '@/features/data-visualization';
MigrationHelpers.destroyExistingVisualization();
```

## Troubleshooting

### Common Issues

1. **Container Not Found**: Ensure the container element exists before creating visualization
2. **Invalid Data**: Use `validateDataNetwork()` to check data format
3. **Performance Issues**: Enable performance mode for large datasets
4. **Memory Leaks**: Always call `destroy()` when removing visualizations

### Migration Issues

1. **Import Errors**: Update all import paths to use the feature module
2. **API Changes**: The legacy API is maintained for backward compatibility
3. **Global Variables**: Legacy global variables are still available for compatibility

### Getting Help

1. Check the assessment document: `MODULAR_IMPLEMENTATION_ASSESSMENT.md`
2. Review the migration plan: `MIGRATION_PLAN.md`
3. Use debug mode to inspect current state
4. Check browser console for warnings and errors
