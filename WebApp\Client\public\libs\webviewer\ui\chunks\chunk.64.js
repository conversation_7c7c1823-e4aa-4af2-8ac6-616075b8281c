(window.webpackJsonp=window.webpackJsonp||[]).push([[64],{1949:function(e,a,r){var n=r(32),t=r(1950);"string"==typeof(t=t.__esModule?t.default:t)&&(t=[[e.i,t,""]]);var o={insert:function(e){if(!window.isApryseWebViewerWebComponent)return void document.head.appendChild(e);let a;a=document.getElementsByTagName("apryse-webviewer"),a.length||(a=function e(a,r=document){const n=[];return r.querySelectorAll(a).forEach(e=>n.push(e)),r.querySelectorAll("*").forEach(r=>{r.shadowRoot&&n.push(...e(a,r.shadowRoot))}),n}("apryse-webviewer"));const r=[];for(let n=0;n<a.length;n++){const t=a[n];if(0===n)t.shadowRoot.appendChild(e),e.onload=function(){r.length>0&&r.forEach(a=>{a.innerHTML=e.innerHTML})};else{const a=e.cloneNode(!0);t.shadowRoot.appendChild(a),r.push(a)}}},singleton:!1};n(t,o);e.exports=t.locals||{}},1950:function(e,a,r){(e.exports=r(33)(!1)).push([e.i,".FormulaBar{display:flex;border:1px solid var(--gray-5);background-color:var(--gray-2);padding:8px;grid-gap:8px;gap:8px;border-bottom:1px solid #e0e0e0}.FormulaBar .RangeInput{padding:6px 8px}.FormulaBar .RangeInput.focus-visible,.FormulaBar .RangeInput:focus-visible{outline:var(--focus-visible-outline)!important}.FormulaBar .Formula{display:flex;align-items:center;flex-grow:1;height:32px;position:relative;border:1px solid var(--border);border-radius:4px;background-color:var(--component-background);padding-left:30px}.FormulaBar .Formula.readOnly{border-color:var(--lighter-border)}.FormulaBar .Formula:not(.readOnly)[focus-within]{outline:var(--focus-visible-outline)!important}.FormulaBar .Formula:not(.readOnly):focus-within{outline:var(--focus-visible-outline)!important}.FormulaBar .FormulaIcon{position:absolute;left:8px}.FormulaBar .FormulaIcon.readOnly{color:var(--disabled-icon)}.FormulaBar .FormulaInput{flex-grow:1;width:100%;padding:6px 8px;border:none;outline:none;font-size:14px;background-color:transparent}",""])},2007:function(e,a,r){"use strict";r.r(a);r(19),r(12),r(13),r(8),r(14),r(10),r(9),r(11),r(16),r(15),r(20),r(18);var n=r(0),t=r.n(n),o=r(6),l=r(3),i=r(429),u=r(43),c=(r(1949),r(4)),s=r.n(c),d=r(5),p=r(76),f=r(17),m=r.n(f),g=function(e){var a=e.isReadOnly,r=e.activeCellRange,n=e.cellFormula,o=e.stringCellValue,l=e.onRangeInputChange,c=e.onRangeInputKeyDown,s=Object(i.a)().t,f=n||o||"";return t.a.createElement(p.a,{className:"FormulaBar",dataElement:d.a.FORMULA_BAR},t.a.createElement("input",{type:"text",className:"RangeInput",value:r,onChange:function(e){return l(e.target.value)},onKeyDown:c,"aria-label":s("formulaBar.range")}),t.a.createElement("div",{className:m()("Formula",{readOnly:a})},t.a.createElement(u.a,{glyph:"function",className:m()("FormulaIcon",{readOnly:a})}),t.a.createElement("input",{type:"text",className:m()("FormulaInput",{readOnly:a}),onChange:function(){},value:f,readOnly:a,"aria-label":s("formulaBar.label")})))};g.propTypes={isReadOnly:s.a.bool,activeCellRange:s.a.string,cellFormula:s.a.oneOfType([s.a.string,s.a.number]),stringCellValue:s.a.string,onRangeInputChange:s.a.func,onRangeInputKeyDown:s.a.func};var b=g,v=r(1);function y(e,a){return function(e){if(Array.isArray(e))return e}(e)||function(e,a){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,t,o,l,i=[],u=!0,c=!1;try{if(o=(r=r.call(e)).next,0===a){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(i.push(n.value),i.length!==a);u=!0);}catch(e){c=!0,t=e}finally{try{if(!u&&null!=r.return&&(l=r.return(),Object(l)!==l))return}finally{if(c)throw t}}return i}}(e,a)||function(e,a){if(!e)return;if("string"==typeof e)return h(e,a);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return h(e,a)}(e,a)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(e,a){(null==a||a>e.length)&&(a=e.length);for(var r=0,n=new Array(a);r<a;r++)n[r]=e[r];return n}var w=[{value:"=SUMIF",label:"SUMIF",description:"formulaBar.sumif"},{value:"=SUMSQ",label:"SUMSQ",description:"formulaBar.sumsq"},{value:"=SUM",label:"SUM",description:"formulaBar.sum"},{value:"=ASINH",label:"ASINH",description:"formulaBar.asinh"},{value:"=ACOS",label:"ACOS",description:"formulaBar.acos"},{value:"=COSH",label:"COSH",description:"formulaBar.cosh"},{value:"=ISEVEN",label:"ISEVEN",description:"formulaBar.iseven"},{value:"=ISODD",label:"ISODD",description:"formulaBar.isodd"}],O=window.Core.SpreadsheetEditor.SpreadsheetEditorEditMode,S=function(){var e=Object(o.e)(l.a.getActiveCellRange),a=Object(o.e)(l.a.getCellFormula),r=Object(o.e)(l.a.getStringCellValue),i=Object(o.e)(l.a.getSpreadsheetEditorEditMode)===O.VIEW_ONLY,u=y(Object(n.useState)(e),2),c=u[0],s=u[1];return Object(n.useEffect)((function(){s(e)}),[e]),t.a.createElement(b,{isReadOnly:i,formulaOptions:w,activeCellRange:c,cellFormula:a,stringCellValue:r,onRangeInputChange:function(e){s(e)},onRangeInputKeyDown:function(a){if("Enter"===a.key)try{var r=v.a.getDocumentViewer().getDocument().getSpreadsheetEditorDocument(),n=v.a.getCellRange(a.target.value);r.selectCellRange(n)}catch(a){return s(e),void console.error(a)}}})};a.default=S}}]);
//# sourceMappingURL=chunk.64.js.map