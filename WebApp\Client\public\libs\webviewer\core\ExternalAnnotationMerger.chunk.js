/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[4],{631:function(ya,ua,n){n.r(ua);var na=n(0),ma=n(653),oa=n(654),ka;(function(ia){ia[ia.EXTERNAL_XFDF_NOT_REQUESTED=0]="EXTERNAL_XFDF_NOT_REQUESTED";ia[ia.EXTERNAL_XFDF_NOT_AVAILABLE=1]="EXTERNAL_XFDF_NOT_AVAILABLE";ia[ia.EXTERNAL_XFDF_AVAILABLE=2]="EXTERNAL_XFDF_AVAILABLE"})(ka||(ka={}));ya=function(){function ia(fa){this.ba=fa;this.state=ka.EXTERNAL_XFDF_NOT_REQUESTED}ia.prototype.mCa=function(){var fa=this;return function(x,
y,r){return Object(na.b)(fa,void 0,void 0,function(){var e,a,f,h,b,w,z,aa=this,ea;return Object(na.d)(this,function(ba){switch(ba.label){case 0:if(this.state!==ka.EXTERNAL_XFDF_NOT_REQUESTED)return[3,2];e=this.ba.getDocument().nz();return[4,this.Lza(e)];case 1:a=ba.aa(),f=this.qta(a),this.yU=null!==(ea=null===f||void 0===f?void 0:f.parse())&&void 0!==ea?ea:null,this.state=null===this.yU?ka.EXTERNAL_XFDF_NOT_AVAILABLE:ka.EXTERNAL_XFDF_AVAILABLE,ba.label=2;case 2:if(this.state===ka.EXTERNAL_XFDF_NOT_AVAILABLE)return r(x),
[2];h=new DOMParser;b=h.parseFromString(x,"text/xml");y.forEach(function(ca){aa.merge(b,aa.yU,ca-1)});w=new XMLSerializer;z=w.serializeToString(b);r(z);return[2]}})})}};ia.prototype.s_=function(fa){this.Lza=fa};ia.prototype.Wf=function(){this.yU=void 0;this.state=ka.EXTERNAL_XFDF_NOT_REQUESTED};ia.prototype.qta=function(fa){return fa?Array.isArray(fa)?new ma.a(fa):"string"!==typeof fa?null:(new DOMParser).parseFromString(fa,"text/xml").querySelector("xfdf > add")?new ma.a(fa):new oa.a(fa):null};ia.prototype.merge=
function(fa,x,y){var r=this;0===y&&(this.JGa(fa,x.Yu),this.LGa(fa,x.cU));var e=x.ea[y];e&&(this.MGa(fa,e.Pu),this.OGa(fa,e.cja,x.FD),this.NGa(fa,e.page,y),this.KGa(fa,e.C7));e=this.ba.zb();if(y===e-1){var a=x.FD;Object.keys(a).forEach(function(f){a[f].xW||r.nca(fa,f,a[f])})}};ia.prototype.JGa=function(fa,x){null!==x&&(fa=this.KC(fa),this.Mw(fa,"calculation-order",x))};ia.prototype.LGa=function(fa,x){null!==x&&(fa=this.KC(fa),this.Mw(fa,"document-actions",x))};ia.prototype.MGa=function(fa,x){var y=
this,r=this.JC(fa.querySelector("xfdf"),"annots");Object.keys(x).forEach(function(e){y.Mw(r,'[name="'.concat(e,'"]'),x[e])})};ia.prototype.OGa=function(fa,x,y){var r=this;if(0!==x.length){var e=this.KC(fa);x.forEach(function(a){var f=a.getAttribute("field"),h=y[f];h&&(r.nca(fa,f,h),r.Mw(e,"null",a))})}};ia.prototype.nca=function(fa,x,y){var r=this.KC(fa),e=r.querySelector('ffield[name="'.concat(x,'"]'));null!==y.tL&&null===e&&this.Mw(r,'ffield[name="'.concat(x,'"]'),y.tL);fa=this.JC(fa.querySelector("xfdf"),
"xfdf > fields","fields");x=x.split(".");this.mZ(fa,x,0,y.value);y.xW=!0};ia.prototype.NGa=function(fa,x,y){null!==x&&(fa=this.KC(fa),fa=this.JC(fa,"pages"),this.Mw(fa,'[number="'.concat(y+1,'"]'),x))};ia.prototype.KGa=function(fa,x){Object.keys(x).forEach(function(y){(y=fa.querySelector('annots [name="'.concat(y,'"]')))&&y.parentElement.removeChild(y)})};ia.prototype.mZ=function(fa,x,y,r){if(y===x.length)x=document.createElementNS("","value"),x.textContent=r,this.Mw(fa,"value",x);else{var e=x[y];
this.JC(fa,'[name="'.concat(e,'"]'),"field").setAttribute("name",e);fa=fa.querySelectorAll('[name="'.concat(e,'"]'));1===fa.length?this.mZ(fa[0],x,y+1,r):(e=this.Yxa(fa),this.mZ(y===x.length-1?e:this.aRa(fa,e),x,y+1,r))}};ia.prototype.Yxa=function(fa){for(var x=null,y=0;y<fa.length;y++){var r=fa[y];if(0===r.childElementCount||1===r.childElementCount&&"value"===r.children[0].tagName){x=r;break}}return x};ia.prototype.aRa=function(fa,x){for(var y=0;y<fa.length;y++)if(fa[y]!==x)return fa[y];return null};
ia.prototype.Mw=function(fa,x,y){x=fa.querySelector(x);null!==x&&fa.removeChild(x);fa.appendChild(y)};ia.prototype.KC=function(fa){var x=fa.querySelector("pdf-info");if(null!==x)return x;x=this.JC(fa.querySelector("xfdf"),"pdf-info");x.setAttribute("xmlns","http://www.pdftron.com/pdfinfo");x.setAttribute("version","2");x.setAttribute("import-version","4");return x};ia.prototype.JC=function(fa,x,y){var r=fa.querySelector(x);if(null!==r)return r;r=document.createElementNS("",y||x);fa.appendChild(r);
return r};return ia}();ua["default"]=ya},642:function(ya,ua){ya=function(){function n(){}n.prototype.AJ=function(na){var ma={Yu:null,cU:null,FD:{},ea:{}};na=(new DOMParser).parseFromString(na,"text/xml");ma.Yu=na.querySelector("pdf-info calculation-order");ma.cU=na.querySelector("pdf-info document-actions");ma.FD=this.tIa(na);ma.ea=this.HIa(na);return ma};n.prototype.tIa=function(na){var ma=na.querySelector("fields");na=na.querySelectorAll("pdf-info > ffield");if(null===ma&&null===na)return{};var oa=
{};this.Epa(oa,ma);this.Cpa(oa,na);return oa};n.prototype.Epa=function(na,ma){if(null!==ma&&ma.children){for(var oa=[],ka=0;ka<ma.children.length;ka++){var ia=ma.children[ka];oa.push({name:ia.getAttribute("name"),element:ia})}for(;0!==oa.length;)for(ma=oa.shift(),ka=0;ka<ma.element.children.length;ka++)ia=ma.element.children[ka],"value"===ia.tagName?na[ma.name]={value:ia.textContent,tL:null,xW:!1}:ia.children&&oa.push({name:"".concat(ma.name,".").concat(ia.getAttribute("name")),element:ia})}};n.prototype.Cpa=
function(na,ma){ma.forEach(function(oa){var ka=oa.getAttribute("name");na[ka]?na[ka].tL=oa:na[ka]={value:null,tL:oa,xW:!1}})};n.prototype.HIa=function(na){var ma=this,oa={};na.querySelectorAll("pdf-info widget").forEach(function(ka){var ia=parseInt(ka.getAttribute("page"),10)-1;ma.RM(oa,ia);oa[ia].cja.push(ka)});na.querySelectorAll("pdf-info page").forEach(function(ka){var ia=parseInt(ka.getAttribute("number"),10)-1;ma.RM(oa,ia);oa[ia].page=ka});this.T9(na).forEach(function(ka){var ia=parseInt(ka.getAttribute("page"),
10),fa=ka.getAttribute("name");ma.RM(oa,ia);oa[ia].Pu[fa]=ka});this.w9(na).forEach(function(ka){var ia=parseInt(ka.getAttribute("page"),10);ka=ka.textContent;ma.RM(oa,ia);oa[ia].C7[ka]=!0});return oa};n.prototype.RM=function(na,ma){na[ma]||(na[ma]={Pu:{},C7:{},cja:[],page:null})};return n}();ua.a=ya},653:function(ya,ua,n){var na=n(0),ma=n(1);n.n(ma);ya=function(oa){function ka(ia){var fa=oa.call(this)||this;fa.Hxa=Array.isArray(ia)?ia:[ia];return fa}Object(na.c)(ka,oa);ka.prototype.parse=function(){var ia=
this,fa={Yu:null,cU:null,FD:{},ea:{}};this.Hxa.forEach(function(x){fa=Object(ma.merge)(fa,ia.AJ(x))});return fa};ka.prototype.T9=function(ia){var fa=[];ia.querySelectorAll("add > *").forEach(function(x){fa.push(x)});ia.querySelectorAll("modify > *").forEach(function(x){fa.push(x)});return fa};ka.prototype.w9=function(ia){return ia.querySelectorAll("delete > *")};return ka}(n(642).a);ua.a=ya},654:function(ya,ua,n){var na=n(0);ya=function(ma){function oa(ka){var ia=ma.call(this)||this;ia.Ixa=ka;return ia}
Object(na.c)(oa,ma);oa.prototype.parse=function(){return this.AJ(this.Ixa)};oa.prototype.T9=function(ka){return ka.querySelectorAll("annots > *")};oa.prototype.w9=function(){return[]};return oa}(n(642).a);ua.a=ya}}]);}).call(this || window)
