
networks:
    localdev:


services:
    postgres-db:
        image: docker.io/postgres:16.6
        restart: on-failure
        ports:
            - '15432:5432'
        networks:
            - localdev
        environment:
            POSTGRES_USER: postgres
            POSTGRES_PASSWORD: root
        command:
            - -N 500
            - -cmax_locks_per_transaction=1024
        volumes:
            - ../../../DockerDataDoNotDelete/postgres-db:/var/lib/postgresql/data

    postgres-test-db:
        image: docker.io/postgres:16.6
        restart: on-failure
        ports:
            - '10432:5432'
        networks:
            - localdev
        tmpfs: /var/lib/pg/data
        environment:
            POSTGRES_USER: postgres
            POSTGRES_PASSWORD: root
            PGDATA: /var/lib/pg/data
        command:
            - -N 500
            - -cmax_locks_per_transaction=1024

    mongo-db:
        image: mongodb/mongodb-community-server:8.0.4-ubi8
        restart: on-failure
        networks:
            - localdev
        environment:
            - MONGO_INITDB_ROOT_USERNAME=admin
            - MONGO_INITDB_ROOT_PASSWORD=root
        ports:
            - '17017:27017'
        volumes:
            - ../../../DockerDataDoNotDelete/mongo-db:/data/db
