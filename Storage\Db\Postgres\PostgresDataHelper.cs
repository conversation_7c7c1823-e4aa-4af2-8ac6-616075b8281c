using System.Collections;
using System.Data;
using System.Text.RegularExpressions;
using Levelbuild.Core.DataStoreInterface.Dto;
using Levelbuild.Core.DataStoreInterface.Enum;
using Levelbuild.Core.DataStoreInterface.Exceptions;
using Levelbuild.Core.DataStoreInterface.Filter;
using Levelbuild.Domain.Storage.Db.QueryParser;
using Levelbuild.Domain.Storage.Helper;
using Levelbuild.Domain.StorageEntities.Features.Dto;
using Levelbuild.Domain.StorageEntities.Features.Enum;
using Newtonsoft.Json;
using Serilog;
using SqlKata;
using SqlKata.Compilers;
using SqlKata.Execution;

namespace Levelbuild.Domain.Storage.Db.Postgres;

/// <summary>
/// Contains postgres specific helper functions for CRUD data
/// </summary>
public class PostgresDataHelper : SqlDataHelper
{
	public PostgresDataHelper(Db db, HashSet<string> failedMigrationTables, ILogger logger) : base(db, failedMigrationTables, logger)
	{
	}

	public static string CompileQuery(Query query, QueryFactory db, Dictionary<string, object> bindings)
	{
		var compiledQuery = db.Compiler.Compile(db.FromQuery(query));
		var sql = compiledQuery.RawSql;
		for (int i = 0; i < compiledQuery.Bindings.Count; i++)
		{
			var binding = compiledQuery.Bindings[i];
			if (binding is Query subSelect)
			{
				var subSql = CompileQuery(subSelect, db, bindings);
				sql = sql.ReplaceFirst("?", subSql);
			}
			else if (binding is IList list)
			{
				string arr = "ARRAY[";
				for (int j = 0; j < list.Count; j++)
				{
					string placeholder = $"@p{bindings.Count}";
					arr += $"{placeholder},";
					bindings[placeholder] = list[j]!;
				}

				arr = arr.Substring(0, arr.Length - 1);
				arr += "]";
				var regex = new Regex("\\?(,\\?)*");
				sql = regex.Replace(sql, arr, 1);
			}
			else
			{
				string placeholder = $"@p{bindings.Count}";
				sql = sql.ReplaceFirst("?", placeholder);

				bindings[placeholder] = binding;
			}
		}

		return sql;
	}


	/// <summary>
	/// <inheritdoc cref="SqlDataHelper.InlineMultiValueFields"/>
	/// </summary>
	protected internal override bool InlineMultiValueFields => true;

	protected override IDictionary<string, object?> InsertWithConnection(QueryFactory queryFactory, StorageIndexDefinition sid,
																		 IDictionary<string, object?> dict,
																		 IDictionary<string, List<IDictionary<string, object?>>> multiValueLookupData,
																		 string language,
																		 Func<string, StorageIndexDefinition?> getStorageIndexDefinition,
																		 IList<DataStoreQueryField>? lookupFields = null)
	{
		try
		{
			var returnQuery = GetPlainByIdQuery(sid, null, language, lookupFields, getStorageIndexDefinition, true, true, false, queryFactory,
												out var changedFieldAliases);
			var queryResult = PostgresqlKataHelper.InsertGetPostgres(sid, dict, multiValueLookupData, returnQuery, queryFactory);

			foreach (var changedFieldAlias in changedFieldAliases)
			{
				if (queryResult.Remove(changedFieldAlias.Key, out var value))
					queryResult[changedFieldAlias.Value] = value;
			}

			return queryResult;
		}
		catch (Exception e)
		{
			var additionalInfos = "";
			if (e.Message.Contains("22001: value too long for type character varying"))
			{
				var tooLongFields = new Dictionary<string, string>();
				var stringFields = sid.Fields.Where(it => it.Type == DataStoreFieldType.String).ToList();
				foreach (var field in stringFields)
				{
					if (dict.TryGetValue(field.Name, out var value) && value != null && value.ToString()!.Length > field.Length)
						tooLongFields.Add(field.Name, value.ToString()!);
				}
				if (tooLongFields.Count > 0)
					additionalInfos = $"Fields too long: {string.Join(", ", tooLongFields)}";
			}
			
			if (string.IsNullOrWhiteSpace(additionalInfos))
				additionalInfos = string.Join(", ", dict);

			throw new DataStoreOperationException($"Error while trying to insert data into {sid.Name}: {additionalInfos} - {e.Message}", e);
		}
	}

	protected override IList<Guid> InsertMultiWithConnection(QueryFactory queryFactory, StorageIndexDefinition sid,
															   IList<IDictionary<string, object?>> dictList)
	{
		try
		{
			return queryFactory.Query(sid.Name).InsertMultiGetPostgres(sid, dictList, queryFactory);
		}
		catch (Exception e)
		{
			Logger.Debug(JsonConvert.SerializeObject(dictList));
			throw new DataStoreOperationException($"Error while trying to insert multiple data into {sid.Name} - {e.Message}", e);
		}
	}

	protected override IDictionary<string, object?> UpdateWithConnection(QueryFactory queryFactory, StorageIndexDefinition sid, Guid id,
																		 IDictionary<string, object?> dict,
																		 IDictionary<string, List<IDictionary<string, object?>>> multiValueLookupData,
																		 string language,
																		 Func<string, StorageIndexDefinition?> getStorageIndexDefinition,
																		 IList<DataStoreQueryField>? lookupFields = null)
	{
		try
		{
			var returnQuery = GetPlainByIdQuery(sid, null, language, lookupFields, getStorageIndexDefinition, true, true, false, queryFactory,
												out var changedFieldAliases);

			var queryResult = PostgresqlKataHelper.UpdateGetPostgres(sid, StorageSystemField.Id.ToString(), id, dict, multiValueLookupData, returnQuery, queryFactory);

			foreach (var changedFieldAlias in changedFieldAliases)
			{
				if (queryResult.Remove(changedFieldAlias.Key, out var value))
					queryResult[changedFieldAlias.Value] = value;
			}

			return queryResult;
		}
		catch (Exception e)
		{
			var additionalInfos = "";
			if (e.Message.Contains("22001: value too long for type"))
			{
				var tooLongFields = new Dictionary<string, string>();
				var stringFields = sid.Fields.Where(it => it.Type == DataStoreFieldType.String).ToList();
				foreach (var field in stringFields)
				{
					if (dict.TryGetValue(field.Name, out var value) && value != null && value.ToString()!.Length > field.Length)
						tooLongFields.Add(field.Name, value.ToString()!);
				}
				if (tooLongFields.Count > 0)
					additionalInfos = $"Fields too long: {string.Join(", ", tooLongFields)}";
			}
			
			if (string.IsNullOrWhiteSpace(additionalInfos))
				additionalInfos = string.Join(", ", dict);

			throw new DataStoreOperationException(
				$"Error while trying to update data in {sid.Name} for {StorageSystemField.Id.ToString()} = {id}: {additionalInfos} - {e.Message}", e);
		}
	}

	/// <inheritdoc/>
	public override string IsNullRaw(string field, object replaceWith)
	{
		return $"COALESCE({field}, {replaceWith})";
	}

	public override string LimitStringLength(string field, int i)
	{
		return $"SUBSTRING({field} FROM 0 FOR {i})";
	}

	/// <inheritdoc/>
	public override string StringAggregateRaw(string field, string separator, string? orderBy = null)
	{
		string orderByClause = orderBy != null ? $" ORDER BY {orderBy}" : "";
		return $"STRING_AGG({field}, '{separator}'{orderByClause})";
	}

	/// <inheritdoc/>
	public override string ArrayAggregateRaw(string field, string? orderBy = null)
	{
		string orderByClause = orderBy != null ? $" ORDER BY {orderBy}" : "";
		return $"ARRAY_AGG({field}{orderByClause})";
	}

	/// <inheritdoc/>
	public override void ArrayEqual(Query query, QueryField field, IList param)
	{
		// array length equal and array contains second array.
		// works on the premise, that there are not duplicates in the arrays
		query.WhereRaw($"cardinality({field.AsQueryString()}) = ?", param.Count);
		if (param.Count > 0)
			query.WhereRaw($"CAST({field.AsQueryString()} AS {GetColumnType(field.Type == DataStoreFieldType.String ? DataStoreFieldType.Text : field.Type)}\\[\\]) @> ARRAY\\[?\\]", param);
	}

	/// <inheritdoc/>
	public override void ArrayNotEqual(Query query, QueryField field, IList param)
	{
		// array length equal and array contains second array.
		// works on the premise, that there are not duplicates in the arrays
		query.Where(q =>
		{
			q.WhereRaw($"cardinality({field.AsQueryString()}) != ?", param.Count);
			if (param.Count > 0)
				q.OrWhereRaw($"NOT(CAST({field.AsQueryString()} AS {GetColumnType(field.Type == DataStoreFieldType.String ? DataStoreFieldType.Text : field.Type)}\\[\\]) @> ARRAY\\[?\\])", param);
			return q;
		});
	}

	/// <inheritdoc/>
	public override void ArrayEqualColumns(Query query, string fieldRaw, string compareFieldRaw)
	{
		// array length equal and array contains second array.
		// works on the premise, that there are not duplicates in the arrays
		query.WhereRaw($"cardinality({fieldRaw}) = cardinality({compareFieldRaw})").WhereRaw($"{fieldRaw} @> {compareFieldRaw}");
	}

	/// <inheritdoc/>
	public override void ArrayNotEqualColumns(Query query, string fieldRaw, string compareFieldRaw)
	{
		// array length equal and array contains second array.
		// works on the premise, that there are not duplicates in the arrays
		query.Where(q => q.WhereRaw($"cardinality({fieldRaw}) != cardinality({compareFieldRaw})").OrWhereRaw($"NOT({fieldRaw} @> {compareFieldRaw})"));
	}

	/// <inheritdoc/>
	public override void ArrayLike(Query query, QueryField field, string param)
	{
		query.WhereExists(new Query().FromRaw($"unnest({field.AsQueryString()}) tag").WhereLike("tag", param).Select("1"));
	}

	/// <inheritdoc/>
	public override void ArrayNotLike(Query query, QueryField field, string param)
	{
		query.Not();
		ArrayLike(query, field, param);
	}

	/// <inheritdoc/>
	public override void ArrayLike(Query query, string param, QueryField field)
	{
		query.WhereExists(new Query().FromRaw($"unnest({field.AsQueryString()}) tag").WhereRaw("? like tag", param).Select("1"));
	}

	/// <inheritdoc/>
	public override void ArrayNotLike(Query query, string param, QueryField field)
	{
		query.Not();
		ArrayLike(query, param, field);
	}

	/// <inheritdoc/>
	public override void ArrayLike(Query query, QueryField field, QueryField param)
	{
		query.WhereExists(new Query().FromRaw($"unnest({field.AsQueryString()}) tag")
							  .WhereRawColumnToSubselect($"[tag]", "like", param.AsQuery(), Db.ConnectionHelper.GetCompiler())
							  .Select("1"));
	}

	/// <inheritdoc/>
	public override void ArrayNotLike(Query query, QueryField field, QueryField param)
	{
		query.Not();
		ArrayLike(query, field, param);
	}

	/// <inheritdoc/>
	public override void ArrayContains(Query query, QueryField field, object param)
	{
		query.WhereRaw($"? = ANY({field.AsQueryString()})", param);
	}

	/// <inheritdoc/>
	public override void ArrayNotContains(Query query, QueryField field, object param)
	{
		query.WhereRaw($"? != ALL({field.AsQueryString()})", param);
	}

	/// <inheritdoc/>
	public override void ArrayAggregate(Query query, Compiler compiler, QueryField field,
										DataStoreColumnAggregationFunction columnAggregationFunction,
										string compOperation, string compareTo, List<object> bindings)
	{
		query.WhereRaw(compiler,
					   new Query().FromRaw($"unnest({field.Expression.GetColumnExpressions()[0].AsString()}) AS aggregated_mvf")
						   .SelectAggregate("[aggregated_mvf]", columnAggregationFunction),
					   compOperation, compareTo, bindings);
	}

	public override void ArrayOverlap(Query query, QueryField field, IList param)
	{
		query.WhereRaw($"CAST({field.AsQueryString()} AS {GetColumnType(field.Type == DataStoreFieldType.String ? DataStoreFieldType.Text : field.Type)}\\[\\]) && CAST(ARRAY\\[?\\] AS {GetColumnType(field.Type == DataStoreFieldType.String ? DataStoreFieldType.Text : field.Type)}\\[\\])", param);
	}

	/// <inheritdoc/>
	public override void ColumnToArrayColumn(Query query, string fieldRaw, QueryFilterOperator filterOperator,
											 string compareOperation, string arrayFieldRaw)
	{
		if (filterOperator is QueryFilterOperator.NotIn or QueryFilterOperator.NotLike)
			query.Not();
		query.WhereExists(new Query().FromRaw($"unnest({arrayFieldRaw}) tag").WhereRaw($"{fieldRaw} {compareOperation} [tag]")
							  .Select("1"));
	}

	public override void FulltextQuery(Query query, StorageIndexDefinition storageIndexDefinition, string compareValue)
	{
		compareValue = Regex.Replace(compareValue.Trim(), @"\s+", " ");
		if (compareValue.Contains(' '))
		{
			compareValue = string.Join(" & ", compareValue.Split(' '));
		}

		query.WhereRaw(
			$"[{storageIndexDefinition.Name}].[{StorageSystemField.SysFulltext}] @@ to_tsquery('{new PostgresLanguages()[Db.CustomerContext!.Language]}', ?)",
			compareValue + ":*");
	}

	protected override string GetDateTimeCol()
	{
		return "TIMESTAMP";
	}
}

public static class PostgresqlKataHelper
{
	public static IDictionary<string, object?> InsertGetPostgres(StorageIndexDefinition sid,
																 IDictionary<string, object?> data,
																 IDictionary<string, List<IDictionary<string, object?>>> multiValueLookupData,
																 Query returnQuery,
																 QueryFactory db,
																 int? timeout = null)
	{
		// looks like sql kata extracts enumerables, but dapper would be ok with it. so here we wrap them once.
		// sql kata will unwrap it again and dapper can handle arrays (at least the postgres implementation)
		var wrapped = new Query(sid.Name).AsInsert(
			data.Select(it => new KeyValuePair<string, object?>(it.Key, it.Value.IsArray() ? new[] { it.Value } : it.Value))
				.ToDictionary());

		var multiValueLookupCtes = GetMultiValueLookupQueries(sid, null, multiValueLookupData, db);

		return WithCte(db, sid, wrapped, multiValueLookupCtes, returnQuery, timeout);
	}

	public static IList<Guid> InsertMultiGetPostgres(
		this Query query,
		StorageIndexDefinition indexDefinition,
		IList<IDictionary<string, object?>> dataList,
		QueryFactory db,
		int? timeout = null)
	{
		var fields = indexDefinition.Fields.ToDictionary(field => field.Name);
		var result = new List<Guid>();

		foreach (var entrySet in dataList.Chunk(1000))
		{
			IList<string> keys = new List<string>();
			IList<string> multiValueKeys = new List<string>();
			foreach (IDictionary<string, object?> data in entrySet)
			{
				foreach (var key in data.Keys)
				{
					if (!keys.Contains(key))
						keys.Add(key);
				}
			}

			foreach (var key in keys)
			{
				var field = fields[key];
				if (field is { MultiValue: true, LookupSource: not null })
				{
					multiValueKeys.Add(key);
				}
			}

			foreach (var k in multiValueKeys)
			{
				keys.Remove(k);
			}

			IList<IList<object?>> valuesList = new List<IList<object?>>();
			foreach (IDictionary<string, object?> data in entrySet)
			{
				IList<object?> valueList = new List<object?>();
				foreach (var key in keys)
				{
					if (data.TryGetValue(key, out object? value))
					{
						if (value is IList { Count: 0 })
						{
							valueList.Add(null);
						}
						else
						{
							valueList.Add(SqlDataHelper.ToType(value, fields[key]));
						}
					}
					else
					{
						valueList.Add(SqlDataHelper.GetDefaultValue(fields[key]));
					}
				}

				valuesList.Add(valueList);
			}

			query.AsInsert(keys, valuesList);
			Dictionary<string, object> bindings = new();
			var sql = PostgresDataHelper.CompileQuery(query, db, bindings);

			sql += $" RETURNING \"{StorageSystemField.Id}\"";

			var ids = db.Select<Guid>(
				sql,
				bindings,
				null,
				timeout
			).ToList();
			MultiValueBatchInsert(db, ids, indexDefinition, fields, multiValueKeys, entrySet, timeout);

			result.AddRange(ids);
		}

		return result;
	}

	private static void MultiValueBatchInsert(QueryFactory db, List<Guid> ids, StorageIndexDefinition indexDefinition,
											  Dictionary<string, StorageFieldDefinitionOrm> fields, IList<string> multiValueKeys,
											  IDictionary<string, object?>[] data,
											  int? timeout)
	{
		Dictionary<string, List<List<object?>>> realData = new();

		var foreignTableForLookupDefintion = new Dictionary<string, string>();
		foreach (var key in multiValueKeys)
		{
			var field = fields[key];
			realData.TryAdd(field.LookupSourceMappingTable!, new List<List<object?>>());
			foreignTableForLookupDefintion[field.LookupSourceMappingTable!] = field.LookupSource!;
		}

		for (int i = 0; i < ids.Count; i++)
		{
			var id = ids[i];
			foreach (var multiValueKey in multiValueKeys)
			{
				if (!data[i].TryGetValue(multiValueKey, out var multiValueData) || multiValueData == null)
					continue;
				var multiValueDataList = multiValueData.ToArray().ToList();
				if (multiValueDataList.Count > 0)
				{
					for (int j = 0; j < multiValueDataList.Count; j++)
					{
						var field = fields[multiValueKey];
						var list = new List<object?>();
						realData[field.LookupSourceMappingTable!].Add(list);
						list.Add(field.Id);
						list.Add(j);
						list.Add(id);
						list.Add(multiValueDataList[j]);
					}
				}
			}
		}

		foreach (var (table, referenceData) in realData)
		{
			var columns = new List<string>()
			{
				StorageSystemField.StorageFieldDefinitionId.ToString(), StorageSystemField.SysOrderBy.ToString(), indexDefinition.Name,
				foreignTableForLookupDefintion[table]
			};
			db.Query(table).Insert(columns, referenceData, null, timeout);
		}
	}

	public static IDictionary<string, object?> UpdateGetPostgres(
		StorageIndexDefinition sid,
		string idCol,
		Guid id,
		IDictionary<string, object?> data,
		IDictionary<string, List<IDictionary<string, object?>>> multiValueLookupData,
		Query returnQuery,
		QueryFactory db,
		int? timeout = null)
	{
		// looks like sql kata extracts enumerables, but dapper would be ok with it. so here we wrap them once.
		// sql kata will unwrap it again and dapper can handle arrays (at least the postgres implementation)
		var wrapped = new Query(sid.Name).AsUpdate(
				data.Select(it => new KeyValuePair<string, object?>(it.Key, it.Value.IsArray() ? new[] { it.Value } : it.Value))
					.ToDictionary())
			.Where(idCol, id);

		var multiValueLookupCtes = GetMultiValueLookupQueries(sid, id, multiValueLookupData, db);

		return WithCte(db, sid, wrapped, multiValueLookupCtes, returnQuery, timeout);
	}

	/// <summary>
	/// Provides multiple queries that insert the mapping data. If id is provided, the existing entries will be deleted.
	/// </summary>
	/// <param name="sid"></param>
	/// <param name="id"></param>
	/// <param name="multiValueLookupData"></param>
	/// <param name="db"></param>
	/// <returns></returns>
	private static Dictionary<string, Query> GetMultiValueLookupQueries(StorageIndexDefinition sid, Guid? id,
																		IDictionary<string, List<IDictionary<string, object?>>> multiValueLookupData,
																		QueryFactory db)
	{
		var multiValueLookupCtes = new Dictionary<string, Query>();
		foreach (var (table, referenceData) in multiValueLookupData)
		{
			if (id != null)
				db.Query(table).Where(sid.Name, id).Delete();

			if (referenceData.Count == 0)
				continue;

			var columns = referenceData[0].Keys;
			// we need to order the entries by the columns
			var cte = new Query(table).AsInsert(columns, referenceData.Select(it => columns.Select(c => it[c])).ToList());
			multiValueLookupCtes.Add(table, cte);
		}

		return multiValueLookupCtes;
	}

	/// <summary>
	/// write as CTE:
	/// <code>
	/// with "insert_or_update_cte" as (
	/// 		insert into "Cultures" ("Id", "Name", "Slug") values ('20c95508-59a5-407f-8c26-00cd297e194c', 'fr', 'fr') returning *
	/// )
	/// select * from insert_or_update_cte as "Cultures"
	/// </code>
	/// </summary>
	/// <param name="db"></param>
	/// <param name="sid"></param>
	/// <param name="wrapped"></param>
	/// <param name="multiValueLookupCtes">mapping of table name to insert ... RETURNING * cte; WARNING: unused CTEs (in returnQuery) will not be executed</param>
	/// <param name="returnQuery"></param>
	/// <param name="timeout"></param>
	/// <returns></returns>
	private static IDictionary<string, object?> WithCte(QueryFactory db, StorageIndexDefinition sid, Query wrapped,
														IDictionary<string, Query> multiValueLookupCtes, Query returnQuery,
														int? timeout)
	{
		var wrappedCompiled = db.Compiler.Compile(wrapped);

		var cteName = "insert_or_update_cte";
		returnQuery.WithRaw(cteName, wrappedCompiled.RawSql + " RETURNING *",
							wrappedCompiled.NamedBindings.Select(it => it.Value.IsArray() ? new[] { it.Value } : it.Value).ToArray());

		returnQuery.From(cteName + " as " + sid.Name);

		foreach (var (name, query) in multiValueLookupCtes)
		{
			var queryCompiled = CompileInsertQueryWithSubSelect(db, query, cteName, out var bindings);
			returnQuery.WithRaw(name, queryCompiled + " RETURNING *",
								bindings.Select(it => it.IsArray() ? new[] { it } : it).ToArray());
		}

		var compiledQuery = db.Compiler.Compile(returnQuery);
		var sql = compiledQuery.Sql;

		return db.Select<dynamic>(
			sql,
			compiledQuery.NamedBindings,
			null,
			timeout
		).First();
	}

	/// <summary>
	/// workaround as long as not supported by sqlkata (https://github.com/sqlkata/querybuilder/pull/468)
	/// </summary>
	/// <param name="queryFactory"></param>
	/// <param name="query"></param>
	/// <param name="cteName"></param>
	/// <param name="bindings"></param>
	/// <returns></returns>
	private static string CompileInsertQueryWithSubSelect(QueryFactory queryFactory, Query query, string cteName, out List<object> bindings)
	{
		bindings = new List<object>();
		var queryCompiled = queryFactory.Compiler.Compile(query);
		var sqlString = queryCompiled.Sql;
		foreach (var (key, binding) in queryCompiled.NamedBindings)
		{
			if (binding is Query subQuery)
			{
				subQuery.From(cteName);
				// really primitive; assumes, that the subselect does not have bindings on its own.
				sqlString = sqlString.ReplaceFirst(key, $"({queryFactory.Compiler.Compile(subQuery).Sql})");
			}
			else
			{
				sqlString = sqlString.ReplaceFirst(key, "?");
				bindings.Add(binding);
			}
		}

		return sqlString;
	}
}