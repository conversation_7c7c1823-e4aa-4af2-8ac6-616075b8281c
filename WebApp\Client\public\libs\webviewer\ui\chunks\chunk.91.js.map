{"version": 3, "sources": ["webpack:///./src/ui/src/components/ModularComponents/PresetButton/buttons/SheetEditor/CellFormatButton.js"], "names": ["propTypes", "formatType", "PropTypes", "string", "isFlyoutItem", "bool", "secondaryLabel", "style", "object", "className", "CellFormatButton", "forwardRef", "props", "ref", "dispatch", "useDispatch", "key", "char<PERSON>t", "toLowerCase", "slice", "menuItems", "dataElement", "icon", "title", "handleClick", "DataElements", "CELL_FORMAT_MORE_BUTTON", "actions", "setFlyoutToggleElement", "toggleElement", "CELL_FORMAT_MORE_FLYOUT", "onClick", "additionalClass", "isActive", "img", "ariaPressed", "displayName"], "mappings": "+aASA,IAAMA,EAAY,CAChBC,WAAYC,IAAUC,OACtBC,aAAcF,IAAUG,KACxBC,eAAgBJ,IAAUC,OAC1BI,MAAOL,IAAUM,OACjBC,UAAWP,IAAUC,QAGjBO,EAAmBC,sBAAW,SAACC,EAAOC,GAC1C,IAAQT,EAA+DQ,EAA/DR,aAAcH,EAAiDW,EAAjDX,WAAYK,EAAqCM,EAArCN,eAAgBC,EAAqBK,EAArBL,MAAOE,EAAcG,EAAdH,UACnDK,EAAWC,cAGXC,EAAM,GAAH,OAAMf,EAAWgB,OAAO,GAAGC,eAAa,OAAGjB,EAAWkB,MAAM,IACrE,EAAqCC,IAAUJ,GAAvCK,EAAW,EAAXA,YAAaC,EAAI,EAAJA,KAAMC,EAAK,EAALA,MAErBC,EAAc,WAEdH,IAAgBI,IAAaC,0BAC/BZ,EAASa,IAAQC,uBAAuBP,IACxCP,EAASa,IAAQE,cAAcJ,IAAaK,4BAIhD,OACE1B,EACE,kBAAC,IAAmB,KACdQ,EAAK,CACTC,IAAKA,EACLkB,QAASP,EACTlB,eAAgBA,EAChB0B,gBAAuC,MAGvC,kBAAC,IAAY,CACXhB,IAAKf,EACLgC,UAzBS,EA0BTF,QAASP,EACTH,YAAaA,EACbE,MAAOA,EACPW,IAAKZ,EACLa,aA9BS,EA+BT5B,MAAOA,EACPE,UAAWA,OAMrBC,EAAiBV,UAAYA,EAC7BU,EAAiB0B,YAAc,mBAEhB1B", "file": "chunks/chunk.91.js", "sourcesContent": ["import React, { forwardRef } from 'react';\nimport ActionButton from 'components/ActionButton';\nimport PropTypes from 'prop-types';\nimport { useDispatch } from 'react-redux';\nimport DataElements from 'constants/dataElement';\nimport actions from 'actions';\nimport FlyoutItemContainer from '../../../FlyoutItemContainer';\nimport { menuItems } from '../../../Helpers/menuItems';\n\nconst propTypes = {\n  formatType: PropTypes.string,\n  isFlyoutItem: PropTypes.bool,\n  secondaryLabel: PropTypes.string,\n  style: PropTypes.object,\n  className: PropTypes.string,\n};\n\nconst CellFormatButton = forwardRef((props, ref) => {\n  const { isFlyoutItem, formatType, secondaryLabel, style, className } = props;\n  const dispatch = useDispatch();\n  const isActive = false;\n\n  const key = `${formatType.charAt(0).toLowerCase()}${formatType.slice(1)}`;\n  const { dataElement, icon, title } = menuItems[key];\n\n  const handleClick = () => {\n    // handle button click\n    if (dataElement === DataElements.CELL_FORMAT_MORE_BUTTON) {\n      dispatch(actions.setFlyoutToggleElement(dataElement));\n      dispatch(actions.toggleElement(DataElements.CELL_FORMAT_MORE_FLYOUT));\n    }\n  };\n\n  return (\n    isFlyoutItem ?\n      <FlyoutItemContainer\n        {...props}\n        ref={ref}\n        onClick={handleClick}\n        secondaryLabel={secondaryLabel}\n        additionalClass={isActive ? 'active' : ''}\n      />\n      : (\n        <ActionButton\n          key={formatType}\n          isActive={isActive}\n          onClick={handleClick}\n          dataElement={dataElement}\n          title={title}\n          img={icon}\n          ariaPressed={isActive}\n          style={style}\n          className={className}\n        />\n      )\n  );\n});\n\nCellFormatButton.propTypes = propTypes;\nCellFormatButton.displayName = 'CellFormatButton';\n\nexport default CellFormatButton;"], "sourceRoot": ""}