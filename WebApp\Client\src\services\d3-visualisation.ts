import * as d3 from 'd3';
import OverlayInstance from './overlay-service'
import { StringLocalizer } from '@/shared/string-localizer.ts'

const _localizer: StringLocalizer = new StringLocalizer('DataSourceVisulizationNetwork')



/**
 * Represents a node in the network visualization
 */
interface Node {
	layerNumber: number
	trunkConnection: string
	expansionLayer: { hemisphere: string; layer: number }
	id: string;
	name: string;
	description: string;
	highlighted: boolean;
	isGrayedOut?: boolean;
	slug?: string;
	size?: number;
	position?: number;
	isDependent?: boolean;
	x?: number;
	y?: number;
	fx?: number;
	fy?: number;
	connectedFields?: {
		current: Field[];
		dependent: Field[];
	};
}

/**
 * Represents a data field within a node
 */
interface Field {
	name: string;
	isKey: boolean;
}

/**
 * Represents a link/connection between nodes
 */
interface Link {
	_bundleKey: any
	source: string | Node;
	target: string | Node;
	isLinkReversed?: boolean;
	_sourceRef?: Node;
	_targetRef?: Node;
}

/**
 * Represents the complete data network
 */
interface DataNetwork {
	nodes: Node[];
	links: Link[];
}

/**
 * Represents a tooltip associated with a node
 */
interface TooltipData {
	tooltip: d3.Selection<HTMLDivElement, unknown, HTMLElement, any>;
	nodeGroup: d3.Selection<SVGGElement, unknown, null, undefined>;
	update: () => void;
}

/**
 * Global window extensions
 */
declare global {
	interface Window {
		sourceNode: Node;
		key: string;
		routeKey: string;
		visualizationMainDatasource: any;
		zoomScale: any;
		linkBundles: any;
		fs: any;
		DataSourceVisulizationNetwork: any;
	}
}

interface FieldFilter {
	FilterColumn: string;
	Operator: 'Equals' | 'Contains' | 'GreaterThan' | 'LessThan' | string; // Add other possible operators
	CompareValue: string | number | boolean; // Adjust type based on what compareValue can be
}


/**
 * Gets the color for a node based on its state
 * @param data - The node data
 * @returns The CSS color variable name
 */
function getColor(data: Node): string {
	switch (true) {
		case data.isGrayedOut === true && !data.highlighted:
			return "var(--visualization-txt-8)";
		case data.highlighted:
			return "var(--clr-levelbuild-purple)";
		default:
			return "var(--visualization-blue-0)";
	}
}

/**
 * Gets the background color for a node on hover
 * @param highlighted - Whether the node is highlighted
 * @returns The CSS color value
 */
function getColorHoverBg(highlighted?: boolean): string {
	if (highlighted) return "var(--clr-purple-200)";
	return "var(--clr-blue-200)";
}

/**
 * Toggles active state between two buttons
 * @param activeButton - The button to make active
 * @param inActiveButton - The button to make inactive
 */
function toggleHandler(activeButton: d3.Selection<d3.BaseType, unknown, d3.BaseType, unknown>, inActiveButton: d3.Selection<d3.BaseType, unknown, d3.BaseType, unknown>): void {
	if (activeButton.classed("active")) return;

	// Update button styles
	activeButton.select("path").attr("fill", "var(--visualization-blue-0)").attr("stroke", "var(--visualization-blue-0)");
	activeButton.select("text").attr("fill", "var(--visualization-txt-11)");
	inActiveButton.select("path").attr("fill", "var(--visualization-bg-1)").attr("stroke", "var(--visualization-bg-1)");
	inActiveButton.select("text").attr("fill", "var(--visualization-txt-1)");
	// Update classes
	activeButton.classed("active", true);
	inActiveButton.classed("active", false);
}

/**
 * Initializes the visualization with data from the API
 * @param id - The ID of the data source to visualize
 * @param key - The key for the type of visualization (pages or dependantSources)
 */
async function initializeVisualization(id: string, key: string): Promise<void> {
	if (id) {
		window.Overlay.showWait(_localizer.localize("Loading"));

		const container = document.getElementById('visualizationChartContainer');
		if (!container) {
			console.error('Container not found');
			return;
		}
		window.key = key;

		const loadingElement = container.querySelector('.loading');
		loadingElement?.classList.remove('hide');

		try {
			const response = await fetch(`/api/${key}/${id}/DataNetwork`);
			if (!response.ok) {
				// Handle the error directly instead of throwing
				console.error(`HTTP error! status: ${response.status}`);
				return; // or handle it appropriately
			}
			const data = await response.json();

			// Double-check DataSourceVisulizationNetwork is available
			if (!response.ok) {
				console.error(`HTTP error! status: ${response.status}`);
				return; // or handle it appropriately
			}

			await OverlayInstance.hideWait()

			new window.DataSourceVisulizationNetwork('visualizationChartContainer', data);
			loadingElement?.classList.add('hide');
		} catch (error) {
			console.error('Error initializing visualization:', error);
			await OverlayInstance.hideWait()
			loadingElement?.classList.add('hide');
			throw error; // Re-throw to allow calling code to handle the error
		}
	}
}

async function refreshFilterPanel(urlKey: string = "DataVisualization"): Promise<void> {
	const filterPanel = document.getElementById('data-visualization-filter') as HTMLElement & {
		url?: string;
		reload?: () => void;
	};

	if (!filterPanel) return;

	try {
		// Show loading state
		filterPanel.setAttribute('skeleton', 'true');

		// Combine base filters with any additional filters
		// Note: In TypeScript, we can't use Razor syntax directly. This would come from your backend.
		const dataSourceId = window.visualizationMainDatasource?.id;
		 const baseFilters: string = JSON.stringify([{
			 FilterColumn: 'filterByDataSourceId',
			 Operator: 'Equals',
			 CompareValue: dataSourceId
		}] as FieldFilter[]);; // Replace with actual filters from your backend

		// Make API call to get updated data
		const response = await fetch(`/Api/${urlKey}/?filters=${encodeURIComponent(baseFilters)}`);
		if (!response.ok) {
			throw new Error(`API request failed with status ${response.status}`);
		}

		const data = await response.json();

		// Update the filter panel with new data
		if (data?.rows || data?.data?.rows) {
			// Update filter panel's internal data
			filterPanel.url = `/Api/${urlKey}/?filters=${encodeURIComponent(JSON.stringify(baseFilters))}`;

			// If the component has a reload method, use it
			if (typeof filterPanel.reload === 'function') {
				filterPanel.reload();
			} else {
				// Force refresh by dispatching a custom event
				filterPanel.dispatchEvent(new CustomEvent('refresh-data', {
					detail: { data: data.rows }
				}));
			}
		}
	} catch (error) {
		console.error('Error refreshing filter panel:', error);
	} finally {
		// Remove loading state
		filterPanel.removeAttribute('skeleton');
	}
}

class DataSourceVisulizationNetwork {
	// Properties
	private container: d3.Selection<d3.BaseType, unknown, HTMLElement, any> | undefined; // @ts-ignore
	private width: number | undefined; // @ts-ignore
	private height: number | undefined;
	private tooltips: Map<string, TooltipData> | undefined; // @ts-ignore
	private rootNode: Node | undefined;
	private data: DataNetwork | undefined;
	private svg: d3.Selection<SVGSVGElement, unknown, HTMLElement, any> | undefined;
	private g: d3.Selection<SVGGElement, unknown, HTMLElement, any> | undefined;
	private simulation: d3.Simulation<Node, undefined> | undefined; // @ts-ignore
	private node: d3.Selection<d3.BaseType, Node, d3.BaseType, unknown> | undefined;
	private zoom: d3.ZoomBehavior<Element, unknown> | undefined
	private clickTimeout: number | null = null
	private updateLinkPositions: (() => void) | undefined
	private cardWidth: number = 160
	private cardHeight: number = 20
	private cardMargin: number = 8
	private leftBorderWidth: number = 5
	private leftBorderOffset: number = 3

	private fontFamily: string = 'Roboto, system-ui, Helvetica, sans-serif'

	constructor(containerId: string, data: DataNetwork) {
		if (!containerId || !data) {
			console.error('Container ID and data are required')
			return
		}

		// Store the initial data source for reset functionality
		if (data.nodes && data.nodes.length > 0) {
			this.rootNode = data.nodes.find(node => node.highlighted)
		}

		// Initialize container and dimensions
		this.container = d3.select(`#${containerId}`)
		// @ts-ignore
		this.width = this.container.node().getBoundingClientRect().width
		// @ts-ignore
		this.height = this.container.node().getBoundingClientRect().height
		this.tooltips = new Map<string, TooltipData>() // Track tooltips by node ID


		// Find and validate root node
		this.rootNode = data.nodes.find(n => n.highlighted) as Node
		if (!this.rootNode) {
			console.warn('No root node found in the dataset')
			return
		}

		// Process data
		this.processData(data)
		this.init()

		// Start fade-out animation after initialization
		setTimeout(() => this.startFadeOutAnimation(), 500)
	}

	// Add this new method to handle the fade-out animation
	startFadeOutAnimation(): void {
		// Select all grayed-out node groups (that are not highlighted)
		// @ts-ignore
		const grayedOutNodes = this.svg.selectAll<d3.BaseType, Node>('.node-group')
															 .filter(d => !!d.isGrayedOut && !d.highlighted)

		// Select all links connected to grayed-out nodes
		// @ts-ignore
		const grayedOutLinks = this.svg.selectAll<d3.BaseType, Link>('.links-group path')
															 .filter(d => {
																 // @ts-ignore
																 const source = typeof d.source === 'object' ? d.source : // @ts-ignore
																	 this.data.nodes.find(n => n.id === d.source)
																 // @ts-ignore

																 const target = typeof d.target === 'object' ? d.target : // @ts-ignore
																	 this.data.nodes.find(n => n.id === d.target)

																 // Use strict equality to handle undefined cases
																 return (source?.isGrayedOut === true && source?.highlighted !== true) ||
																	 (target?.isGrayedOut === true && target?.highlighted !== true)
															 })

		// Animation duration in milliseconds
		const duration = 5000 // 5 seconds for full fade-out

		// Animate the opacity of grayed-out nodes from 0.5 to 0
		grayedOutNodes.transition()
									.duration(duration)
									.style('opacity', '0')
									.on('end', function () {
										// Optional: fully hide the node after animation
										d3.select(this).style('display', 'none')
									})

		// Animate the opacity of related links from 0.5 to 0
		grayedOutLinks.transition()
									.duration(duration)
									.style('opacity', '0')
									.on('end', function () {
										// Optional: fully hide the link after animation
										d3.select(this).style('display', 'none')
									})
	}

	processData(data: DataNetwork): void {
		// Calculate node sizes based on card dimensions
		const baseSize = Math.max(this.cardWidth, this.cardHeight) / 2 + 10
		data.nodes.forEach(node => {
			node.size = baseSize + (node.highlighted ? 20 : 0)
		})

		// Filter nodes and links
		const usedNodeIds = new Set(data.links.flatMap(link => {
			const source = typeof link.source === 'object' ? link.source.id : link.source
			const target = typeof link.target === 'object' ? link.target.id : link.target
			return [ source, target ]
		}))

		this.data = {
			nodes: data.nodes.filter(node => node.highlighted || usedNodeIds.has(node.id)),
			links: data.links,
		}
	}

	init(): void {
		// 1. Clear container and create SVG (must be first)
		// @ts-ignore
		this.container.selectAll('*').remove()
		// @ts-ignore
		this.svg = this.container.append('svg') // @ts-ignore
									 .attr('width', this.width) // @ts-ignore
									 .attr('height', this.height) as d3.Selection<SVGSVGElement, unknown, HTMLElement, any>

		// 2. Setup core visualization elements
		this.g = this.svg.append('g') as d3.Selection<SVGGElement, unknown, HTMLElement, any> // Main group for all elements
		this.setupMarkers() // Must be before createLinks()
		this.setupZoom() // Zoom behavior needs to be early

		// 3. Create simulation and nodes first
		this.setupSimulation()
		this.createLinks()
		this.createNodes()

		this.addHelpIcon()
		this.addZoomControls()
		this.addDataSourceToggle()
		this.addResetButton()
	}

	/**
	 * Sets up the zoom behavior for the visualization
	 * Includes handling for zoom events and initializes with centered transform
	 */
	setupZoom(): void {
		// Create a basic zoom behavior
		// @ts-ignore
		this.zoom = d3.zoom<SVGSVGElement, unknown>()
									.scaleExtent([0.25, 2])
									.on('zoom', (event: d3.D3ZoomEvent<SVGSVGElement, unknown>) => {
										// Apply the transform directly to the main group
										// @ts-ignore
										this.g.attr('transform', event.transform.toString());

										// Update zoom text if needed
										// @ts-ignore
										const zoomText = this.svg.select<SVGTextElement>('.zoom-controls text')
																				 .filter((_d, i) => i === 0);
										if (!zoomText.empty()) {
											zoomText.text(`${Math.round(event.transform.k * 100)}%`);
										}
									});

		// Apply the zoom behavior directly to the SVG
		// @ts-ignore
		this.svg.call(this.zoom as any);

		// Set an initial identity transform (no zoom, no pan)
		const initialTransform = d3.zoomIdentity;
		// @ts-ignore
		this.svg.call((this.zoom as any).transform, initialTransform);
	}

	applyZoom(scale: number): void {
		if (!this.svg || !this.zoom) return;
		this.closeAllTooltips()
		// Calculate center of visualization
		const boundedScale = Math.max(0.25, Math.min(2, scale));
		// @ts-ignore
		const centerX = this.width ? this.width / 2 : 0;
		// @ts-ignore
		const centerY = this.height ? this.height / 2 : 0;

		// Create centered transform
		// @ts-ignore
		// Create centered transform
		const transform = d3.zoomTransform(this.svg.node() as Element)
			.translate(centerX, centerY)
			.scale(boundedScale)
			.translate(-centerX, -centerY);

		// Apply with smooth transition
		// @ts-ignore
		this.svg.transition()
				.duration(100)
				.call((this.zoom as any).transform, transform)

		window.zoomScale = boundedScale;

	}

	setupNodePositions(): void {
		// @ts-ignore
		const centerX = this.width / 4
		// @ts-ignore
		const centerY = this.height / 4
		const radius = 150
		const horizontalRadius = radius * 1.2

		// Find root node
		// @ts-ignore
		const rootNode = this.data.nodes.find(n => n.highlighted)
		if (!rootNode) return

		// Position root node at center and fix it
		rootNode.x = centerX
		rootNode.y = centerY
		rootNode.fx = centerX
		rootNode.fy = centerY
		window.sourceNode = rootNode

		// @ts-ignore
		const childNodes = this.data.nodes
													 .filter(node => !node.highlighted)
													 .filter(Boolean)

		const childNodsCount = childNodes.length
		let distanceFor3n9ClockNodes;

		if (childNodsCount > 8) {
			// @ts-ignore
			distanceFor3n9ClockNodes = (this.width / 9)
		} else {
			// @ts-ignore
			distanceFor3n9ClockNodes = (this.width / 20)
		}

		// Distribute nodes
		childNodes.forEach((node, i) => {
			switch (i) {
				case 0: // 6 o'clock (bottom center)
					node.x = centerX
					node.y = centerY + radius / 2
					node.position = 6
					break
				case 1: // 7 o'clock (bottom left)
					node.x = centerX - radius * Math.cos(Math.PI / 6)
					node.y = centerY + radius * Math.sin(Math.PI / 6)
					node.position = 7
					break
				case 2: // 5 o'clock (bottom right)
					node.x = centerX + radius * Math.cos(Math.PI / 6)
					node.y = centerY + radius * Math.sin(Math.PI / 6)
					node.position = 5

					break
				case 3: // 12 o'clock (top center)
					node.x = centerX
					node.y = centerY - radius / 2
					node.position = 12

					break
				case 4: // 11 o'clock (top left)
					node.x = centerX - radius * Math.cos(Math.PI / 6)
					node.y = centerY - radius * Math.sin(Math.PI / 6)
					node.position = 11

					break
				case 5: // 1 o'clock (top right)
					node.x = centerX + radius * Math.cos(Math.PI / 6)
					node.y = centerY - radius * Math.sin(Math.PI / 6)
					node.position = 1

					break
				case 6: // 3 o'clock (right center)
					// @ts-ignore
					node.x = centerX + horizontalRadius + distanceFor3n9ClockNodes
					node.y = centerY
					node.position = 3

					break
				case 7: // 9 o'clock (left center)
					// @ts-ignore
					node.x = centerX - horizontalRadius - distanceFor3n9ClockNodes
					node.y = centerY
					node.position = 9
					break

				case 8: // 10 o'clock (top left)
					node.x = centerX - (radius * 2) * Math.cos(Math.PI / 6)
					node.y = centerY - radius * Math.sin(Math.PI / 6)
					node.position = 10
					break

				case 9: // 2 o'clock (top right)
					node.x = centerX + (radius * 2) * Math.cos(Math.PI / 6)
					node.y = centerY - radius * Math.sin(Math.PI / 6)
					node.position = 2
					break

				case 10: // 4 o'clock (bottom right)
					node.x = centerX + (radius * 2) * Math.cos(Math.PI / 6)
					node.y = centerY + radius * Math.sin(Math.PI / 6)
					node.position = 4
					break
				case 11: // 8 o'clock (bottom left)
					node.x = centerX - (radius * 2) * Math.cos(Math.PI / 6)
					node.y = centerY + radius * Math.sin(Math.PI / 6)
					node.position = 8
					break
				default: // Any additional nodes
					// Place in a circle with even distribution
					const angle = 2 * Math.PI * (i - 6) / (childNodes.length - 6)
					node.x = centerX + radius * Math.cos(angle)
					node.y = centerY + radius * Math.sin(angle)
					node.position = 0
			}

			// Fix positions permanently
			node.fx = node.x
			node.fy = node.y
		})
	}

	setupSimulation(): void {
		// Initialize force simulation
		// @ts-ignore
		this.simulation = d3.forceSimulation<Node>(this.data.nodes) // @ts-ignore
												.force('link', d3.forceLink<Node, Link>(this.data.links)
																				 .id(d => d.id)
																				 .distance(200))
												.force('charge', d3.forceManyBody().strength(-10))
												.force('collide', d3.forceCollide<Node>()
																						.radius(d => (d.size || 0) + 10)
																						.strength(0.2)) // @ts-ignore
												.force('center', d3.forceCenter(this.width / 4, this.height / 4)) // Add center force
												.force('x', null)
												.force('y', null)
												// @ts-ignore
		// Set initial node positions
		// @ts-ignore
		if (this.data.nodes.length > 13) {
			this.setupExpandedNodePositions( // @ts-ignore
				this.data.nodes, // @ts-ignore
				this.width / 4, // @ts-ignore
				this.height / 4,
				150 // baseRadius
			);
		}
		else {
			this.setupNodePositions()
		}

		// Update positions on tick
		this.simulation.on('tick', () => this.updatePositions())

		// Force a few ticks to stabilize the layout
		for (let i = 0; i < 10; i++) {
			this.simulation.tick()
		}

		// Update positions one final time
		this.updatePositions()
	}

	// Update positions function to ensure links follow nodes
	updatePositions(): void {
		// Update node positions first
		// @ts-ignore
		this.svg.selectAll<d3.BaseType, Node>('.node-group')
				.attr('transform', d => `translate(${d.x || 0},${d.y || 0})`)

		// Then update link paths with the new node positions
		// @ts-ignore
		this.svg.select<SVGGElement>('.links-group').selectAll<SVGPathElement, Link>('path')
				.attr('d', d => {
					// Use the stored references to the node objects
					const source = d._sourceRef || (typeof d.source === 'object' ? d.source : // @ts-ignore
						this.data.nodes.find(n => n.id === d.source))
					const target = d._targetRef || (typeof d.target === 'object' ? d.target : // @ts-ignore
						this.data.nodes.find(n => n.id === d.target))

					if (!source || !target) return ''

					// Use the current positions for the link path determineIfInitialReversed
					// @ts-ignore
					if (this.data.nodes.length > 13) {
						return this.determineIfReversed({
							source: source,
							target: target,
							isLinkReversed: d.isLinkReversed,
							_bundleKey: d._bundleKey
						})
					}
					else{
						return this.determineIfInitialReversed({
							source: source,
							target: target,
							isLinkReversed: d.isLinkReversed,
							_bundleKey: d._bundleKey
						})
					}

				})
	}

	// Add this helper function to set up arrow markers with better visibility
	setupMarkers(): void {
		// Remove any existing markers to prevent duplicates
		// @ts-ignore
		this.svg.selectAll('defs').remove()

		// Create marker definitions
		// @ts-ignore
		const defs = this.svg.append('defs')

		// Arrow marker for the end of links
		defs.append('marker')
				.attr('id', 'arrowhead')
				.attr('viewBox', '0 -5 10 10')
				.attr('refX', 0)  // Adjusted to align with the end of the path
				.attr('refY', 0)
				.attr('markerWidth', 6)
				.attr('markerHeight', 6)
				.attr('orient', 'auto')
				.append('path')
				.attr('d', 'M0,-5L10,0L0,5')
				.attr('class', 'arrowhead')
				.style('fill', 'context-stroke') // This makes the arrow take the color of the line

		// Dot marker for the start of links
		defs.append('marker')
				.attr('id', 'startdot')
				.attr('viewBox', '-6 -6 11 12')
				.attr('refX', 0)
				.attr('refY', 0)
				.attr('markerWidth', 8)
				.attr('markerHeight', 8)
				.attr('orient', 'auto')
				.attr('markerUnits', 'userSpaceOnUse')
				.append('circle')
				.attr('r', 6)
				.attr('cx', 0)
				.attr('cy', 0)
				.style('fill', 'context-stroke') // Takes the color of the line
				.style('stroke', 'context-stroke')
				.style('stroke-width', '1')
	}

	createLinks(): void {
		// First setup markers
		this.setupMarkers();

		// Create a global link bundle registry for the visualization
		if (!window.linkBundles) {
			window.linkBundles = {};
		}

		// Clear existing bundle data for this visualization
		window.linkBundles = {};

		// Preprocess links to identify bundles based on direction
		if (this.data && this.data.links && this.data.nodes) {
			// First, assign source and target references to each link
			this.data.links.forEach(link => {
				// @ts-ignore
				link._sourceRef = typeof link.source === 'object' ? link.source : // @ts-ignore
					this.data.nodes.find(n => n.id === link.source);
				link._targetRef = typeof link.target === 'object' ? link.target : // @ts-ignore
					this.data.nodes.find(n => n.id === link.target);
			});
		}

		// Create links group
		// @ts-ignore
		this.link = this.g.append("g")
										.attr("class", "links-group")
										.selectAll<SVGPathElement, Link>("path") // @ts-ignore
										.data(this.data.links)
										.enter().append("path") // @ts-ignore
										.attr('d', d => this.data.nodes.length > 13 ? this.determineIfReversed(d) : this.determineIfInitialReversed(d))
										.attr('fill', 'none')
										.attr('stroke', d => this.implementLinkColor(d))
										.attr('stroke-width', 2)
										.attr('marker-end', "url(#arrowhead)")
										.attr('marker-start', "url(#startdot)")
										.style("pointer-events", "visibleStroke")
										.style("opacity", d => {
											// Get source and target nodes
											const source = d._sourceRef || (typeof d.source === 'object' ? d.source : // @ts-ignore
												this.data.nodes.find(n => n.id === d.source));
											const target = d._targetRef || (typeof d.target === 'object' ? d.target : // @ts-ignore
												this.data.nodes.find(n => n.id === d.target));

											// Set initial opacity based on node status
											if ((source?.isGrayedOut === true && source?.highlighted !== true) ||
												(target?.isGrayedOut === true && target?.highlighted !== true)) {
												return 0.5; // Start with partial opacity for grayed links
											}
											return 1; // Full opacity for normal links
										});

		// Add a method to update link positions that respects bundling
		this.updateLinkPositions = () => {
			// @ts-ignore
			this.svg.select<SVGGElement>('.links-group').selectAll<SVGPathElement, Link>('path')
					.attr('d', d => {
						// Use stored bundle information if available
						// @ts-ignore
						if (d._bundleKey && window.linkBundles[d._bundleKey]) {
							// Retrieve bundling coordinates
							// @ts-ignore
							const bundle = window.linkBundles[d._bundleKey];

							// If this is the first link in the bundle, calculate and store the bundle path
							if (!bundle.pathCalculated) {
								// @ts-ignore
								bundle.path = this.data.nodes.length > 12 ? this.determineIfReversed(d) : this.determineIfInitialReversed(d);
								bundle.pathCalculated = true;
								return bundle.path;
							}

							// Use the precalculated bundle path for all subsequent links in this bundle
							return bundle.path;
						}

						// Fall back to normal path calculation if no bundle info
						// @ts-ignore
						return this.data.nodes.length > 12 ? this.determineIfReversed(d) : this.determineIfInitialReversed(d);
					});
		};

		// Override the simulation tick method to use our enhanced link position updates
		// @ts-ignore
		const originalTick = this.simulation.tick;
		// @ts-ignore
		this.simulation.tick = () => {
			originalTick.call(this.simulation);
			// @ts-ignore
			this.updateLinkPositions();
		};
	}

	determineIfInitialReversed(d: Link): string {
		if (d.isLinkReversed === true) return this.createInitialReversedSimpleLink(d);
		return this.createInitialSimpleLink(d);
	}

	// Modified createSimpleLink for better edge handling at 3 and 9 o'clock
	createInitialSimpleLink(d: Link): string {
		const multiplier = 2;
		// @ts-ignore
		const source = typeof d.source === 'object' ? d.source : // @ts-ignore
			this.data.nodes.find(n => n.id === d.source);
		const target = typeof d.target === 'object' ? d.target : // @ts-ignore
			this.data.nodes.find(n => n.id === d.target);

		if (!source || !target) return 'M0,0';

		// Node dimensions
		const nodeWidth = this.cardWidth + (this.cardMargin * 2);
		const nodeHeight = this.cardHeight + (this.cardMargin * 2);
		const padding = 15; // Space between node edge and link

		// Calculate direction vector
		const dx = (target.x || 0) - (source.x || 0);
		const dy = (target.y || 0) - (source.y || 0);
		const length = Math.sqrt(dx * dx + dy * dy);

		if (length === 0) return 'M0,0';

		// Normalized direction vector
		const nx = dx / length;
		const ny = dy / length;

		// Calculate exit points from nodes
		const getEdgePoint = (node: Node, isSource: boolean): { x: number, y: number } => {
			const nodeX = node.x || 0;
			const nodeY = node.y || 0;

			// Special handling for clock positions at cardinal and intermediate directions
			switch (node.position) {
				case 3: // 3 o'clock (right center)
					return {
						x: (nodeX * multiplier) - nodeWidth/6 - padding,
						y: nodeY * multiplier
					};
				case 9: // 9 o'clock (left center)
					return {
						x: (nodeX * multiplier) + nodeWidth/6 + padding,
						y: nodeY * multiplier
					};
				case 2: // 2 o'clock (top right)
					return {
						x: (nodeX * multiplier) + nodeWidth/2.1,
						y: (nodeY * multiplier) + nodeHeight/3 + padding
					};
				case 10: // 10 o'clock (top left)
					return {
						x: (nodeX * multiplier) - nodeWidth/2.1,
						y: (nodeY * multiplier) + nodeHeight/4 + padding
					};
				case 4: // 4 o'clock (bottom right)
					return {
						x: (nodeX * multiplier) + nodeWidth/2.1,
						y: (nodeY * multiplier) - nodeHeight/3 - padding
					};
				case 8: // 8 o'clock (bottom left)
					return {
						x: (nodeX * multiplier) - nodeWidth/2.1,
						y: (nodeY * multiplier) - nodeHeight/3 - padding
					};
			}

			// For root node (center)
			if (node.highlighted) {
				// If it's the source node, use fixed exit points based on direction
				if (isSource) {
					// Determine if the target is on the left or right side
					const isRightSide = dx > 0;
					const isTopSide = dy < 0;

					// For left/right sides, use fixed exit points at 3 and 9 o'clock
					if (Math.abs(dx) > Math.abs(dy)) {
						return {
							x: nodeX * multiplier + (isRightSide ? (nodeWidth/2 + padding) : -(nodeWidth/2 + padding)),
							y: nodeY * multiplier
						};
					}
					// For top/bottom sides, use fixed exit points at 12 and 6 o'clock
					else {
						return {
							x: nodeX * multiplier,
							y: nodeY * multiplier + (isTopSide ? -(nodeHeight/2 + padding) : (nodeHeight/2 + padding))
						};
					}
				}
				// If it's not a source node, use the original calculation
				return {
					x: nodeX * multiplier,
					y: nodeY * multiplier
				};
			}

			// For non-root nodes not handled by specific position cases above,
			// use the original angle-based calculation
			const angle = Math.atan2(dy, dx) * (180 / Math.PI);

			// Determine which side of the node to exit from based on angle
			if (angle > -45 && angle < 45) { // Right side
				return {
					x: (nodeX * multiplier) + nodeWidth/3 + padding,
					y: nodeY * multiplier
				};
			}
			else if (angle > 45 && angle < 135) { // Bottom side
				return {
					x: nodeX * multiplier,
					y: (nodeY * multiplier) + nodeHeight/4 + padding
				};
			}
			else if (angle > 135 || angle < -135) { // Left side
				return {
					x: (nodeX * multiplier) - nodeWidth/3 - padding,
					y: nodeY * multiplier
				};
			}
			else { // Top side
				return {
					x: nodeX * multiplier,
					y: (nodeY * multiplier) - nodeHeight/4 - padding
				};
			}
		};
		const start = getEdgePoint(source, true);
		const end = getEdgePoint(target, false);

		// Adjust end point to account for arrowhead
		const arrowAdjust = -65; // Increased for better visibility
		const adjustedEndX = end.x + nx * arrowAdjust;
		const adjustedEndY = end.y + ny * arrowAdjust;

		// Special handling for horizontal nodes (3 and 9 o'clock)
		if (source.highlighted && (target.position === 3 || target.position === 9)) {
			const curveRadius = 0;
			const extensionValue = 100; // Shorter horizontal extension for these positions

			// For 3 o'clock (right)
			if (target.position === 3) {
				return `M${start.x},${start.y}
          L${start.x + extensionValue},${start.y}
          C${start.x + extensionValue + curveRadius/2},${start.y}
           ${start.x + extensionValue + curveRadius},${start.y + curveRadius/2}
           ${start.x + extensionValue + curveRadius},${start.y + curveRadius}
          L${adjustedEndX},${adjustedEndY}`;
			}
			// For 9 o'clock (left)
			else if (target.position === 9) {
				return `M${start.x},${start.y}
          L${start.x - extensionValue},${start.y}
          C${start.x - extensionValue - curveRadius/2},${start.y}
           ${start.x - extensionValue - curveRadius},${start.y + curveRadius/2}
           ${start.x - extensionValue - curveRadius},${start.y + curveRadius}
          L${adjustedEndX},${adjustedEndY}`;
			}
		}

		// Check if target is at 2, 5, 8, or 10 o'clock position
		// These positions correspond to specific child nodes in setupNodePositions
		const isSpecialPosition = (): boolean => {
			if (!source.highlighted) return false;

			// Use position property to determine clock position
			return [1, 2, 4, 5, 7, 8, 10, 11].includes(target.position || 0);
		};

		if (isSpecialPosition()) {
			const arrowAdjust = -80; // Increased for better visibility
			const adjustedEndX = end.x + nx * arrowAdjust;
			const adjustedEndY = end.y + ny * arrowAdjust;
			// Define a smaller curve radius for tighter turns
			const curveRadius = 30;

			// Determine horizontal and vertical direction
			const isRightSide = adjustedEndX > start.x;
			// const isBottomSide = adjustedEndY > start.y;

			// Adjust extension value based on position for better spacing
			let extensionValue = 131;

			// Adjust extension values based on specific clock positions
			if ([8, 10].includes(target.position || 0)) { // Left side positions (8, 10 o'clock)
				extensionValue = extensionValue*3;
			} else if ([2, 4].includes(target.position || 0)) {
				extensionValue = extensionValue*3;
			}


			const horizontalExtension = isRightSide ? extensionValue : -extensionValue;

			// Calculate the x position after the curve
			const xAfterCurve = start.x + horizontalExtension + (isRightSide ? curveRadius : -curveRadius);

			// Adjust vertical curve based on whether target is above or below
			const verticalCurveFactor = Math.sign(adjustedEndY - start.y) * (
				// Increase curve height for top/bottom positions
				[2, 10].includes(target.position || 0) ? curveRadius * 1.5 :
					[4, 8].includes(target.position || 0) ? curveRadius * 1.2 :
						curveRadius
			);

			// Create path: horizontal line, smoothed curve, then straight line to target
			return `M${start.x},${start.y}
      L${start.x + horizontalExtension},${start.y}
      C${start.x + horizontalExtension + (isRightSide ? curveRadius/2 : -curveRadius/2)},${start.y}
       ${xAfterCurve},${start.y + verticalCurveFactor/2}
       ${xAfterCurve},${start.y + verticalCurveFactor}
      L${adjustedEndX},${adjustedEndY}`;
		}

		// Default straight line for other positions
		return `M${start.x},${start.y} L${adjustedEndX},${adjustedEndY}`;
	}

	createInitialReversedSimpleLink(d: Link): string {
		// Get the actual node objects - keep original source and target
		const multiplier = 2;
		const source = typeof d.source === 'object' ? d.source : // @ts-ignore
			this.data.nodes.find(n => n.id === d.source);
		const target = typeof d.target === 'object' ? d.target : // @ts-ignore
			this.data.nodes.find(n => n.id === d.target);

		if (!source || !target) return 'M0,0';

		// Node dimensions
		const nodeWidth = this.cardWidth + (this.cardMargin * 2);
		const nodeHeight = this.cardHeight + (this.cardMargin * 2);
		const padding = 15; // Space between node edge and link

		// Calculate direction vector - SAME as original, not reversed
		const dx = (target.x || 0) - (source.x || 0);
		const dy = (target.y || 0) - (source.y || 0);
		const length = Math.sqrt(dx * dx + dy * dy);

		if (length === 0) return 'M0,0';

		// Normalized direction vector
		const nx = dx / length;
		const ny = dy / length;

		// Calculate exit points from nodes - SAME as original
		// @ts-ignore
		const getEdgePoint = (node: Node, isSource: boolean): { x: number, y: number } => {
			const nodeX = node.x || 0;
			const nodeY = node.y || 0;

			// Special handling for 3 and 9 o'clock positions
			if (node.position === 3) { // 3 o'clock position
				return {
					x: (nodeX * multiplier) - nodeWidth/2 - padding,
					y: nodeY * multiplier
				};
			}
			else if (node.position === 9) { // 9 o'clock position
				return {
					x: (nodeX * multiplier) + nodeWidth/2 + padding,
					y: nodeY * multiplier
				};
			}

			let xValue: number, yValue: number;
			if (node.highlighted && [1, 2, 4, 5, 7, 8, 10, 11].includes(source.position || 0)) {
				// Determine if the target is on the left or right side
				const isRightSide = dx > 0;
				const isTopSide = dy < 0;

				if (isRightSide) {
					xValue = nodeX * multiplier - (nodeWidth/2 + padding);
				} else {
					xValue = nodeX * multiplier + (nodeWidth/2 + padding);
				}

				if (isTopSide) {
					yValue = nodeY * multiplier + (nodeHeight/2 + padding);
				} else {
					yValue = nodeY * multiplier - (nodeHeight/2 + padding);
				}

				// Return the calculated position for source node
				return {
					x: xValue,
					y: yValue
				};
			}

			// For non-root nodes, use the original position-based exit points
			switch(node.position) {
				case 1: // 1 o'clock
					return {
						x: (nodeX * multiplier) - nodeWidth/4 + nodeWidth/4,
						y: (nodeY * multiplier) + nodeHeight
					};
				case 2: // 1 o'clock
					return {
						x: (nodeX * multiplier) - nodeWidth/4 + nodeWidth/4,
						y: (nodeY * multiplier) + nodeHeight
					};
				case 4: // 4 o'clock
					return {
						x: (nodeX * multiplier) - nodeWidth/4 + nodeWidth/4,
						y: (nodeY * multiplier) - nodeHeight
					};
				case 5: // 5 o'clock
					return {
						x: (nodeX * multiplier) - nodeWidth/4 + nodeWidth/4,
						y: (nodeY * multiplier) - nodeHeight
					};
				case 7: // 8 o'clock
					return {
						x: (nodeX * multiplier) - nodeWidth/4 + nodeWidth/4,
						y: (nodeY * multiplier) - nodeHeight
					};
				case 8: // 8 o'clock
					return {
						x: (nodeX * multiplier) - nodeWidth/4 + nodeWidth/4,
						y: (nodeY * multiplier) - nodeHeight
					};
				case 10: // 11 o'clock
					return {
						x: (nodeX * multiplier) + nodeWidth/4 - nodeWidth/4,
						y: (nodeY * multiplier) + nodeHeight
					};
				case 11: // 11 o'clock
					return {
						x: (nodeX * multiplier) + nodeWidth/4 - nodeWidth/4,
						y: (nodeY * multiplier) + nodeHeight
					};
				case 6: // 6 o'clock (bottom)
					return {
						x: nodeX * multiplier,
						y: (nodeY * multiplier) - nodeHeight/2 - padding
					};
				case 12: // 12 o'clock (top)
					return {
						x: nodeX * multiplier,
						y: (nodeY * multiplier) + nodeHeight/2 + padding
					};
				default:
					// Fall back to angle-based logic for any other positions
					const angle = Math.atan2(dy, dx) * (180 / Math.PI);
					if (angle > -45 && angle < 45) { // Right side
						return {
							x: (nodeX * multiplier) + nodeWidth/2 + padding,
							y: nodeY * multiplier
						};
					}
					else if (angle > 45 && angle < 135) { // Bottom side
						return {
							x: nodeX * multiplier,
							y: (nodeY * multiplier) + nodeHeight/2 + padding
						};
					}
					else if (angle > 135 || angle < -135) { // Left side
						return {
							x: (nodeX * multiplier) - nodeWidth/2 - padding,
							y: nodeY * multiplier
						};
					}
					else { // Top side
						return {
							x: nodeX * multiplier,
							y: (nodeY * multiplier) - nodeHeight/2 - padding
						};
					}
			}
		};

		const originalStart = getEdgePoint(target, true);
		const originalEnd = getEdgePoint(source, false);

		// Reverse the start and end points
		const start = originalEnd;  // Target becomes start
		const end = originalStart;  // Source becomes end

		const reversedNx = -nx;
		const reversedNy = -ny;

		const arrowAdjust = 70;
		const adjustedEndX = end.x + reversedNx * arrowAdjust;
		const adjustedEndY = end.y + reversedNy * arrowAdjust;

		// Special handling for horizontal nodes (3 and 9 o'clock)
		if ([3, 9].includes(source.position || 0)) {
			const curveRadius = 0;
			const add = 16;
			let entryPointX: number, entryPointY: number;

			// Specific handling for 3 o'clock (right side) nodes
			if (source.position === 3) {
				entryPointX = end.x + (nodeWidth + padding + add);
				entryPointY = end.y;

				// Create a curved path coming from the left side of the node
				return `M${start.x},${start.y}
          L${start.x - 60},${start.y}
          C${start.x - 60 - curveRadius/2},${start.y}
            ${start.x - 60 - curveRadius},${entryPointY}
            ${entryPointX},${entryPointY}`;
			}

			// Specific handling for 9 o'clock (left side) nodes
			if (source.position === 9) {
				entryPointX = end.x - (nodeWidth + padding + add);
				entryPointY = end.y;

				// Create a curved path coming from the right side of the node
				return `M${start.x},${start.y}
          L${start.x + 60},${start.y}
          C${start.x + 60 + curveRadius/2},${start.y}
            ${start.x + 60 + curveRadius},${entryPointY}
            ${entryPointX},${entryPointY}`;
			}
		}

		const isSpecialPosition = (): boolean => {
			return [1, 2, 4, 5, 7, 8, 10, 11].includes(source.position || 0);
		};

		if (isSpecialPosition()) {
			const arrowAdjust = 6;

			let entryPointX: number, entryPointY: number;
			let eastHemisphereXLink = (end.x - nodeWidth) + (nodeWidth + padding) - nodeWidth/8;
			let rightHemisphereXLink = (end.x + nodeWidth) - (nodeWidth + padding) + nodeWidth/8;
			let eastHemisphereYLink = end.y + nodeHeight;

			// Group positions by which side of the node they're on
			if ([1, 2].includes(source.position || 0)) {
				entryPointX = rightHemisphereXLink;
				entryPointY = (end.y + nodeHeight/2) + (nodeHeight/padding) + padding;
			}
			else if ([4, 5].includes(source.position || 0)) {
				entryPointX = rightHemisphereXLink;
				entryPointY = (end.y - nodeHeight/2) - (nodeHeight/padding) - padding;
			}
			else if ([10, 11].includes(source.position || 0)) {
				entryPointX = eastHemisphereXLink;
				entryPointY = eastHemisphereYLink;
			}
			else if ([7, 8].includes(source.position || 0)) {
				entryPointX = eastHemisphereXLink;
				entryPointY = (end.y - nodeHeight/2) - (nodeHeight/padding) - padding;
			}
			else if ([6].includes(source.position || 0)) {
				entryPointX = end.x;
				entryPointY = end.y + (nodeHeight/2 + padding);
			}
			else {
				// Links from top (11, 12, 1 o'clock) enter at a single point on the bottom
				entryPointX = end.x;
				entryPointY = end.y - (nodeHeight/2 + padding);
			}

			const adjustedEndX = entryPointX - (reversedNx * arrowAdjust);
			const adjustedEndY = entryPointY + (reversedNy * arrowAdjust);

			// Radius for the curved corner
			const curveRadius = 30;

			// Create the path with a curved elbow
			return `M${start.x},${start.y}
					L${start.x},${adjustedEndY - Math.sign(adjustedEndY - start.y) * curveRadius}
					C${start.x},${adjustedEndY - Math.sign(adjustedEndY - start.y) * curveRadius/2}
					 ${start.x + Math.sign(adjustedEndX - start.x) * curveRadius/2},${adjustedEndY}
					 ${start.x + Math.sign(adjustedEndX - start.x) * curveRadius},${adjustedEndY}
					L${adjustedEndX},${adjustedEndY}`;
		}

		return `M${start.x},${start.y} L${adjustedEndX},${adjustedEndY}`;
	}

	implementLinkColor(link: Link): string {
		// Get actual node objects for both source and target
		const source = typeof link.source === 'object' ? link.source : // @ts-ignore
			this.data.nodes.find(n => n.id === link.source);
		const target = typeof link.target === 'object' ? link.target : // @ts-ignore
			this.data.nodes.find(n => n.id === link.target);

		const hasHighlightedNode = source?.highlighted || target?.highlighted;

		if (hasHighlightedNode && !target?.isGrayedOut) {
			return "var(--visualization-blue-0)";
		}
		if (hasHighlightedNode && !source?.isGrayedOut) {
			return "var(--visualization-blue-0)";
		}

		if (hasHighlightedNode && !target?.isGrayedOut) {
			return "var(--visualization-blue-0)";
		}

		// If either end is grayed out (and neither is highlighted), use light color
		if ((source?.isGrayedOut || target?.isGrayedOut)) {
			return "var(--visualization-txt-8)";
		}

		// Default for normal active links
		return "var(--visualization-blue-0)";
	}

	addHelpIcon(): void {
		const padding = 9;
		const radius = 15;
		const bottomMargin = 4;
		const self = this;

		// Create a group for the help icon that won't be affected by zoom/pan
		// @ts-ignore
		const helpIconGroup = this.svg.append("g")
							.attr("class", "help-icon") // @ts-ignore
							.attr("transform", `translate(${radius + padding}, ${this.height - radius - padding - bottomMargin})`)
							.style("cursor", "pointer")
							.on("mouseenter", function(this: d3.BaseType) {
								// Show tooltip on hover
								self.showTooltip.call(this);
							})
							.on("mouseleave", function() {
								// Hide tooltip when mouse leaves
								d3.select(".tooltip-container").remove();
							});

		// Add circular background
		helpIconGroup.append("circle")
								 .attr("r", radius)
								 .attr("fill", "var(--visualization-bg-2)")
								 .attr("stroke", "var(--visualization-bg-2)")
								 .attr("filter", "drop-shadow(0 0 0.8rem var(--clr-shadow-weak))")
								 .attr("stroke-width", 1);

		// Add question mark
		helpIconGroup.append("text")
								 .attr("text-anchor", "middle")
								 .attr("dy", "0.35em")
								 .attr("font-family", this.fontFamily)
								 .attr("font-size", "var(--size-text-l)")
								 .attr("fill", "var(--clr-blue-900)")
								 .text("?");
	}

	addZoomControls(): void {
		const padding = 20;
		const bottomMargin = 0; // Match the bottom margin from help icon
		const buttonWidth = 34;
		const buttonHeight = 25;
		const controlsWidth = 120; // Total width of the zoom control panel
		const controlsHeight = buttonHeight;
		const buttonRadius = 4;

		// Create a group for the zoom controls
		// @ts-ignore
		const zoomControlsGroup = this.svg.append("g")
																	.attr("class", "zoom-controls") // @ts-ignore
																	.attr("transform", `translate(${this.width - controlsWidth - padding}, ${this.height - controlsHeight - padding - bottomMargin})`);

		// Background container for the controls
		zoomControlsGroup.append("rect")
										 .attr("width", controlsWidth)
										 .attr("height", controlsHeight)
										 .attr("rx", buttonRadius)
										 .attr("ry", buttonRadius)
										 .attr("fill", "var(--visualization-bg-0)")
										 .attr("stroke", "var(--visualization-bg-0)")
										 .attr("filter", "drop-shadow(0 0 0.8rem var(--clr-shadow-weak))")
										 .attr("stroke-width", 1);

		// Current zoom level display
		const zoomText = zoomControlsGroup.append("text")
																			.attr("x", controlsWidth / 2)
																			.attr("y", buttonHeight / 2)
																			.attr("text-anchor", "middle")
																			.attr("dominant-baseline", "central")
																			.attr("font-family", this.fontFamily)
																			.attr("font-size", "var(--size-text-m)")
																			.attr("fill", "var(--visualization-txt-0)")
																			.text("100%");

		// Zoom out button (left)
		const zoomOutButton = zoomControlsGroup.append("g")
																					 .attr("class", "zoom-out-button")
																					 .style("cursor", "pointer");

		zoomOutButton.append("rect")
								 .attr("x", 0)
								 .attr("y", 0)
								 .attr("width", buttonWidth)
								 .attr("height", buttonHeight)
								 .attr("rx", buttonRadius)
								 .attr("ry", buttonRadius)
								 .attr("fill", "var(--visualization-bg-0)")
								 .attr("stroke", "var(--visualization-bg-0)")
								 .attr("stroke-width", 1);

		zoomOutButton.append("text")
								 .attr("x", buttonWidth / 2)
								 .attr("y", buttonHeight / 2)
								 .attr("text-anchor", "middle")
								 .attr("dominant-baseline", "central")
								 .attr("font-family", this.fontFamily)
								 .attr("font-size", "var(--size-text-m)")
								 .attr("fill", "var(--visualization-txt-0)")
								 .text("−");

		// Zoom in button (right)
		const zoomInButton = zoomControlsGroup.append("g")
																					.attr("class", "zoom-in-button")
																					.attr("transform", `translate(${controlsWidth - buttonWidth}, 0)`)
																					.style("cursor", "pointer");

		zoomInButton.append("rect")
								.attr("x", 0)
								.attr("y", 0)
								.attr("width", buttonWidth)
								.attr("height", buttonHeight)
								.attr("rx", buttonRadius)
								.attr("ry", buttonRadius)
								.attr("fill", "var(--visualization-bg-0)")
								.attr("stroke", "var(--visualization-bg-0)")
								.attr("stroke-width", 1);

		zoomInButton.append("text")
								.attr("x", buttonWidth / 2)
								.attr("y", buttonHeight / 2)
								.attr("text-anchor", "middle")
								.attr("dominant-baseline", "central")
								.attr("font-family", this.fontFamily)
								.attr("font-size", "var(--size-text-m)")
								.attr("fill", "var(--visualization-txt-0)")
								.text("+");

		window.zoomScale = window.zoomScale ? window.zoomScale : 1;

		// Set up zoom behavior for the buttons
		let currentZoom = window.zoomScale; // Starting at 100%

		zoomOutButton.on("click", () => {
			if (currentZoom <= 0.25) return; // Don't zoom out if at minimum
			const newZoom = currentZoom - 0.1; // Only decrease zoom
			if (newZoom < 0.25 || newZoom >= 2) return; // Additional safety check
			currentZoom = newZoom;
			window.zoomScale = newZoom;
			this.applyZoom(newZoom);
			zoomText.text(`${Math.round(newZoom * 100)}%`);
		});

		zoomInButton.on("click", () => {
			if (currentZoom >= 2) return; // Don't zoom in if at maximum
			const newZoom = currentZoom + 0.1; // Only increase zoom
			if (newZoom > 2 || newZoom < 0.25) return; // Additional safety check
			currentZoom = newZoom;
			window.zoomScale = newZoom;
			this.applyZoom(newZoom);
			zoomText.text(`${Math.round(newZoom * 100)}%`);
		});
	}

	addDataSourceToggle(): void {
		const buttonHeight = 25;
		const buttonWidth = 180;
		const totalWidth = buttonWidth * 2;
		const cornerRadius = 4;
		const padding = 20;
		const topMargin = -10;

		// Create a group for the buttons at the top center
		// @ts-ignore
		const buttonGroup = this.svg.append("g")
														.attr("class", "data-source-toggle")
														.attr("filter", "drop-shadow(0 0 0.8rem var(--clr-shadow-weak))")
														.attr("fill", "var(--visualization-bg-1)") // @ts-ignore
														.attr("transform", `translate(${(this.width - totalWidth) / 2}, ${padding + topMargin})`);

		// Referenced Data Sources button (active by default)
		const referencedButton = buttonGroup.append("g")
																				.attr("class", "referenced-button active")
																				.attr("filter", "drop-shadow(0 0 0.8rem var(--clr-shadow-weak))")
																				.style("cursor", "pointer");

		referencedButton.append("text")
										.attr("x", buttonWidth / 2)
										.attr("y", buttonHeight / 2)
										.attr("text-anchor", "middle")
										.attr("dominant-baseline", "central")
										.attr("font-family", this.fontFamily)
										.attr("font-size", "var(--size-text-m)")
										.attr("fill", "white")
										.text(_localizer.localize("ReferencedDataSources"));

		// Add curved path for referenced button
		referencedButton.insert("path", "text")
										.attr("d", `
      M ${cornerRadius},0
      H ${buttonWidth - cornerRadius}
      Q ${buttonWidth},0 ${buttonWidth},${cornerRadius}
      V ${buttonHeight - cornerRadius}
      Q ${buttonWidth},${buttonHeight} ${buttonWidth - cornerRadius},${buttonHeight}
      H ${cornerRadius}
      Q 0,${buttonHeight} 0,${buttonHeight - cornerRadius}
      V ${cornerRadius}
      Q 0,0 ${cornerRadius},0
    `)
										.attr("fill", "var(--visualization-blue-0)")
										.attr("stroke", "var(--visualization-blue-0)")
										.attr("stroke-width", 1);

		// Dependent Data Sources button
		const dependentButton = buttonGroup.append("g")
																			 .attr("class", "dependent-button")
																			 .attr("transform", `translate(${buttonWidth}, 0)`)
																			 .attr("filter", "drop-shadow(0 0 0.8rem var(--clr-shadow-weak))")
																			 .style("cursor", "pointer");

		dependentButton.append("path")
									 .attr("d", `
                  M 0,0
                  H ${buttonWidth - cornerRadius}
                  Q ${buttonWidth},0 ${buttonWidth},${cornerRadius}
                  V ${buttonHeight - cornerRadius}
                  Q ${buttonWidth},${buttonHeight} ${buttonWidth - cornerRadius},${buttonHeight}
                  H 0
                  V 0
                  `)
									 .attr("fill", "var(--visualization-bg-1)")
									 .attr("stroke", "var(--visualization-bg-1)")
									 .attr("stroke-width", 1);

		dependentButton.append("text")
									 .attr("x", buttonWidth / 2)
									 .attr("y", buttonHeight / 2)
									 .attr("text-anchor", "middle")
									 .attr("dominant-baseline", "central")
									 .attr("font-family", this.fontFamily)
									 .attr("font-size", "var(--size-text-m)")
									 .attr("fill", "var(--visualization-txt-1)")
									 .text(_localizer.localize("DependentDataSources"));

		if  (window.key === "dependantSources" && this.data?.nodes?.some((l) => l?.isDependent)) {
			// if (window.key === "dependantSources"){
				toggleHandler(dependentButton as any, referencedButton as any);
			}
			else {
				toggleHandler(referencedButton as any, dependentButton as any);
			}
		// }

		referencedButton.on("click", () => {
			toggleHandler(referencedButton as any, dependentButton as any);
			this.showReferencedDataSources();
		});

		dependentButton.on("click", () => {
			toggleHandler(dependentButton as any, referencedButton as any);
			this.showDependentDataSources();
		});
	}

	async showReferencedDataSources(): Promise<void> {
		// @ts-ignore - Function is defined in _Connections.cshtml
		window.hideVisualizationFilter();
		window.routeKey = "pages";
		// @ts-ignore
		await refreshFilterPanel()
		// @ts-ignore
		await initializeVisualization(this.rootNode.id, "pages");
	}

	async showDependentDataSources(): Promise<void> {
		// @ts-ignore - Function is defined in _Connections.cshtml
		window.hideVisualizationFilter();
		window.routeKey = "dependantSources";
		// @ts-ignore
		await refreshFilterPanel("DependentDataVisualization")
		// @ts-ignore
		await initializeVisualization(this.rootNode.id, "dependantSources");
	}

	addResetButton(): void {
		const buttonHeight = 25;
		const buttonWidth = 60;
		const cornerRadius = 4;
		const padding = 20;
		const topMargin = -10;

		// @ts-ignore
		const resetButtonGroup = this.svg.append("g")
																 .attr("class", "reset-button")
																 .attr("fill", "var(--visulization-bg-0)")
																 .attr("filter", "drop-shadow(0 0 0.8rem var(--clr-shadow-weak))") // @ts-ignore
																 .attr("transform", `translate(${this.width - buttonWidth - padding + 2}, ${padding + topMargin})`)
																 .style("cursor", "pointer")
																 .on("click", () => this.resetVisualization());

		// Button background
		resetButtonGroup.append("rect")
										.attr("x", 0)
										.attr("y", 0)
										.attr("width", buttonWidth)
										.attr("height", buttonHeight)
										.attr("rx", cornerRadius)
										.attr("ry", cornerRadius)
										.attr("fill", "var(--visualization-bg-0)")
										.attr("filter", "drop-shadow(0 0 0.8rem var(--clr-shadow-weak))")
										.attr("stroke", "var(--visualization-bg-0)")
										.attr("stroke-width", 1);

		// Reset icon (circular arrow)
		resetButtonGroup.append("path")
										.attr("transform", `translate(${buttonWidth/10}, ${buttonHeight/4}) scale(0.026)`)
										.attr("d", "M256.455 8c66.269.119 126.437 26.233 170.859 68.685l35.715-35.715C478.149 25.851 504 36.559 504 57.941V192c0 13.255-10.745 24-24 24H345.941c-21.382 0-32.09-25.851-16.971-40.971l41.75-41.75c-30.864-28.899-70.801-44.907-113.23-45.273-92.398-.798-170.283 73.977-169.484 169.442C88.764 348.009 162.184 424 256 424c41.127 0 79.997-14.678 110.629-41.556 4.743-4.161 11.906-3.908 16.368.553l39.662 39.662c4.872 4.872 4.631 12.815-.482 17.433C378.202 479.813 319.926 504 256 504 119.034 504 8.001 392.967 8 256.002 7.999 119.193 119.646 7.755 256.455 8z")
										.attr("fill", "var(--visualization-txt-0)");

		// Reset text
		resetButtonGroup.append("text")
										.attr("x", buttonWidth/2 + 8)
										.attr("y", buttonHeight/2)
										.attr("text-anchor", "middle")
										.attr("dominant-baseline", "central")
										.attr("font-family", this.fontFamily)
										.attr("font-size", "var(--size-text-m)")
										.attr("fill", "var(--visualization-txt-0)")
										.text(_localizer.localize("Reset"));
	}

	async resetVisualization(): Promise<void> {
		try {
			// @ts-ignore - Function is defined in _Connections.cshtml 
			window.hideVisualizationFilter();
			const mainDataSource = window.visualizationMainDatasource;
			if (!mainDataSource || !mainDataSource.id) {
				console.error('Main data source not found');
				return;
			}
			window.routeKey = "pages";

			// Reset to initial view with pages
			await initializeVisualization(mainDataSource.id, "pages");

		} catch (error) {
			console.error('Error resetting visualization:', error);
			// Get the current URL parameters
			const urlParams = new URLSearchParams(window.location.search);
			const dataSourceId = urlParams.get('id');

			// If we have an ID in the URL, use that as fallback
			if (dataSourceId) {
				await initializeVisualization(dataSourceId, "pages");
			}
		}
	}

	/**
	 * Creates all node elements in the visualization
	 */
	createNodes(): void {
		// @ts-ignore
		this.node = this.g.append("g")
					.selectAll<SVGGElement, Node>(".node-group") // @ts-ignore
					.data(this.data.nodes)
					.enter().append<SVGGElement>("g")
					.attr("class", "node-group")
					.call((selection: d3.Selection<SVGGElement, Node, SVGGElement, unknown>) =>
						this.createCustomNode(selection, this)
					);
	}

	/**
	 * Creates the custom node visualization for each node
	 * @param selection - The D3 selection of nodes to customize
	 * @param self - Reference to the DataSourceVisulizationNetwork instance
	 */
	createCustomNode(selection: d3.Selection<SVGGElement, Node, SVGGElement, unknown>, self: DataSourceVisulizationNetwork): void {
		const cardMargin = this.cardMargin;
		const cardWidth = this.cardWidth;
		const cardHeight = this.cardHeight;
		const leftBorderWidth = this.leftBorderWidth;
		const leftBorderOffset = this.leftBorderOffset;
		const adjustedHeight = cardHeight + (cardMargin * 2);

		const cardX = (d: Node): number => (d.x || 0) - (cardWidth/2) - cardMargin;
		const cardY = (d: Node): number => (d.y || 0) - (cardHeight/2) - cardMargin;

		selection.each(function(d: Node) {
			const node = d3.select(this);
			const x = (d.x || 0) - cardWidth/2;
			const y = (d.y || 0) - cardHeight/2;

			// Skip special fade handling for highlighted nodes
			const isGrayedButNotHighlighted = d.isGrayedOut && !d.highlighted;

			// Set initial opacity for grayed out nodes (but not highlighted ones)
			const initialOpacity = isGrayedButNotHighlighted ? 0.5 : 1;

			// Set the whole node group's opacity
			node.style("opacity", String(initialOpacity));

			d3.select("body").append("div") // for the custom title tooltip
				.attr("class", "custom-tooltip")
				.style("opacity", "0")
				.style("position", "absolute")
				.style("background", "var(--clr-blue-950)")
				.style("color", "white")
				.style("padding", "var(--size-spacing-s) var(--size-spacing-m)")
				.style("border-radius", "var(--size-border-radius-l)")
				.style("font-size", "var(--size-text-m)")
				.style("pointer-events", "none");

			// Card background
			node.append('rect')
					.attr('x', x - cardMargin)
					.attr('y', y - cardMargin)
					.attr('width', cardWidth + cardMargin*2)
					.attr('height', cardHeight + cardMargin*2)
					.attr('rx', 3)
					.attr('ry', 3)
					.attr('fill', 'var(--visualization-bg-0)')
					.attr('stroke', getColor(d))
					.attr('stroke-width', 2)
					.attr('cursor', "pointer")
					.attr('class', 'node-background')
					.on('mouseover', function(event: MouseEvent) {
						event.stopPropagation();
						// Only apply hover effects if the node is still visible
						if (parseFloat(node.style("opacity") as string) > 0.05) {
							// Temporarily boost opacity on hover
							const hoverOpacity = isGrayedButNotHighlighted ? 0.8 : 1;
							node.style("opacity", String(hoverOpacity));

							d3.select(this).attr('fill', getColorHoverBg(d.highlighted));
							iconGroup.select('path').attr('fill', "var(--visualization-bg-6)");
						}
					})
					.on('mouseout', function(event: MouseEvent) {
						event.stopPropagation();
						// Return to the current animation state opacity
						node.style("opacity", String(initialOpacity)); // Animation will continue updating this

						d3.select(this).attr('fill', 'var(--visualization-bg-0)');
						iconGroup.select('path').attr('fill', 'var(--visualization-bg-6)');
					})
					.on("click", function(event: MouseEvent) {
						// Only respond to clicks if the node is still somewhat visible
						if (parseFloat(node.style("opacity") as string) > 0.1) {
							event.stopPropagation();
							event.preventDefault();
							self.handleNodeClick(event, d, node);
						}
					});

			// Apply a dashed stroke pattern for grayed out nodes
			if (isGrayedButNotHighlighted) {
				node.select('.node-background')
						.attr('stroke-dasharray', '3,2');
			}

			// Left border
			node.append('line')
					.attr('x1', cardX(d) + leftBorderOffset)
					.attr('y1', cardY(d))
					.attr('x2', cardX(d) + leftBorderOffset)
					.attr('y2', cardY(d) + adjustedHeight)
					.attr('stroke', getColor(d))
					.attr('stroke-width', leftBorderWidth);

			// Text elements
			node.append('text')
					.attr('x', x + leftBorderOffset + leftBorderWidth - 5)
					.attr('y', y + 6)
					.attr('font-family', 'Roboto, system-ui, Helvetica, sans-serif')
					.attr('font-size', 'var(--size-text-m)')
					.attr('fill', 'var(--visualization-txt-0)')
					.attr('cursor', "pointer")
					.text(d.name)
					.on('mouseover', function(event: MouseEvent) {
						// Only apply hover effects if the node is still visible
						if (parseFloat(node.style("opacity") as string) > 0.05) {
							event.stopPropagation();
							// Temporarily boost opacity on hover
							const hoverOpacity = isGrayedButNotHighlighted ? 0.8 : 1;
							node.style("opacity", String(hoverOpacity));

							node.select('rect').attr('fill', getColorHoverBg(d.highlighted));
							iconGroup.select('path').attr('fill', getColor(d));
						}
					})
					.on('mouseout', function(event: MouseEvent) {
						event.stopPropagation();
						// Return to the current animation state opacity
						node.style("opacity", String(initialOpacity));

						node.select('rect').attr('fill', 'white');
						iconGroup.select('path').attr('fill', 'var(--clr-black-600)');
					})
					.on("click", function(event: MouseEvent) {
						// Only respond to clicks if the node is still somewhat visible
						if (parseFloat(node.style("opacity") as string) > 0.1) {
							event.stopPropagation();
							event.preventDefault();
							self.handleNodeClick(event, d, node);
						}
					});

			node.append('text')
					.attr('x', x + leftBorderOffset + leftBorderWidth - 5)
					.attr('y', y + 19)
					.attr('font-family', 'Roboto, system-ui, Helvetica, sans-serif')
					.attr('font-size', 'var(--size-text-s)')
					.attr('fill', 'var(--visualization-txt-5)')
					.attr('cursor', "pointer")
					.text((d.description))
					.on('mouseover', function(event: MouseEvent) {
						// Only apply hover effects if the node is still visible
						if (parseFloat(node.style("opacity") as string) > 0.05) {
							event.stopPropagation();
							// Temporarily boost opacity on hover
							const hoverOpacity = isGrayedButNotHighlighted ? 0.8 : 1;
							node.style("opacity", String(hoverOpacity));

							node.select('rect').attr('fill', getColorHoverBg(d.highlighted));
							iconGroup.select('path').attr('fill', getColor(d));
						}
					})
					.on('mouseout', function(event: MouseEvent) {
						event.stopPropagation();
						// Return to the current animation state opacity
						node.style("opacity", String(initialOpacity));

						node.select('rect').attr('fill', 'white');
						iconGroup.select('path').attr('fill', 'var(--clr-black-600)');
					})
					.on("click", function(event: MouseEvent) {
						// Only respond to clicks if the node is still somewhat visible
						if (parseFloat(node.style("opacity") as string) > 0.1) {
							event.stopPropagation();
							event.preventDefault();
							self.handleNodeClick(event, d, node);
						}
					});

			// Add a "grayed out" overlay label for clearly identifying inactive nodes
			if (isGrayedButNotHighlighted) {
				node.append('text')
						.attr('x', x + cardWidth/2)
						.attr('y', y + cardHeight/2 + 2) // Center in the card
						.attr('text-anchor', 'middle')
						.attr('dominant-baseline', 'middle')
						.attr('font-family', 'Roboto, system-ui, Helvetica, sans-serif')
						.attr('font-size', 'var(--size-text-xs)')
						.attr('font-style', 'italic')
						.attr('fill', 'var(--visualization-txt-8)')
						.attr('pointer-events', 'none') // Don't interfere with clicks
						.text('Inactive');
			}

			// Create a separate group for the icon
			const iconGroup = node.append('g')
									.attr('transform', `translate(${x + cardWidth - 20}, ${y + 4}) scale(0.018)`);

			// Add an invisible hit area that's larger than the icon
			iconGroup.append('rect')
							 .attr('x', -300)
							 .attr('y', -300)
							 .attr('rx', 4)
							 .attr('ry', 4)
							 .attr('width', 1200)
							 .attr('height', 1200)
							 .attr('fill', 'transparent')
							 .attr('cursor', 'pointer')
							 .attr('class', 'icon-hit-area')
							 .on('mouseover', function(event: MouseEvent) {
								 // Only apply hover effects if the node is still visible
								 if (parseFloat(node.style("opacity") as string) > 0.05) {
									 event.stopPropagation();
									 event.preventDefault();

									 // Temporarily boost opacity on hover
									 const hoverOpacity = isGrayedButNotHighlighted ? 0.8 : 1;
									 node.style("opacity", String(hoverOpacity));

									 node.select('rect').attr('fill', getColorHoverBg(d.highlighted));

									 // Change fill color on hover
									 iconGroup.select('.icon-path').attr('fill', 'var(--visualization-bg-6)');

									 const tooltip = d3.select(".custom-tooltip");
									 tooltip.style("opacity", "1")
													.text("Open Data source")
													.style("left", ((event.pageX + 10)/10) + "rem")
													.style("top", ((event.pageY + 10)/10) + "rem");

									 // Add a background rectangle
									 iconGroup.insert('rect', '.icon-hit-area')
														.attr('x', -300)
														.attr('y', -300)
														.attr('rx', 10)
														.attr('ry', 10)
														.attr('width', 1200)
														.attr('height', 1200)
														.attr('fill', 'var(--visualization-bg-3)')
														.attr('class', 'hover-bg');
								 }
							 })
							 .on('mouseout', function(event: MouseEvent) {
								 event.stopPropagation();
								 // Return to the current animation state opacity
								 node.style("opacity", String(initialOpacity));

								 // Restore original fill color
								 node.select('rect').attr('fill', 'var(--visualization-bg-0)');

								 iconGroup.select('.icon-path').attr('fill', 'var(--visualization-bg-6)');
								 d3.select(".custom-tooltip").style("opacity", "0");
								 // Remove the background
								 iconGroup.selectAll('.hover-bg').remove();
							 })
							 .on('mousemove', function(event: MouseEvent) {
								 d3.select(".custom-tooltip")
									 .style("left", ((event.pageX + 10)/10) + "rem")
									 .style("top", ((event.pageY + 10)/10) + "rem");
							 })
							 .on('click', function(event: MouseEvent) {
								 // Only respond to clicks if the node is still somewhat visible
								 if (parseFloat(node.style("opacity") as string) > 0.1) {
									 // Handle icon click separately from node click
									 event.stopPropagation();
									 event.preventDefault();
									 const datasource = window.visualizationMainDatasource;
									 const datasourceSlug = d.slug;
									 const datastoreSlug = datasource.dataStore.slug;
									 window.open(`/Admin/DataStores/${datastoreSlug}/DataSources/${datasourceSlug}/Connections`, '_blank');
								 }
							 });

			iconGroup.append('path')
							 .attr('d', 'M320 0c-17.7 0-32 14.3-32 32s14.3 32 32 32l82.7 0L201.4 265.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L448 109.3l0 82.7c0 17.7 14.3 32 32 32s32-14.3 32-32l0-160c0-17.7-14.3-32-32-32L320 0zM80 32C35.8 32 0 67.8 0 112L0 432c0 44.2 35.8 80 80 80l320 0c44.2 0 80-35.8 80-80l0-112c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 112c0 8.8-7.2 16-16 16L80 448c-8.8 0-16-7.2-16-16l0-320c0-8.8 7.2-16 16-16l112 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L80 32z')
							 .attr('fill', 'var(--visualization-bg-6)')
							 .attr('cursor', 'pointer')
							 .attr('class', 'icon-path')
							 .on('mouseover', function(event: MouseEvent) {
								 // Only apply hover effects if the node is still visible
								 if (parseFloat(node.style("opacity") as string) > 0.05) {
									 event.stopPropagation();
									 event.preventDefault();

									 // Temporarily boost opacity on hover
									 const hoverOpacity = isGrayedButNotHighlighted ? 0.8 : 1;
									 node.style("opacity", String(hoverOpacity));

									 node.select('rect').attr('fill', getColorHoverBg(d.highlighted));

									 // Change fill color on hover
									 iconGroup.select('.icon-path').attr('fill', 'var(--clr-blue-900)');

									 const tooltip = d3.select(".custom-tooltip");
									 tooltip.style("opacity", "1")
													.text("Open Data source")
													.style("left", ((event.pageX + 10)/10) + "rem")
													.style("top", ((event.pageY + 10)/10) + "rem");

									 // Add a background rectangle
									 iconGroup.insert('rect', '.icon-hit-area')
														.attr('x', -300)
														.attr('y', -300)
														.attr('rx', 5)
														.attr('ry', 5)
														.attr('width', 1200)
														.attr('height', 1200)
														.attr('fill', 'var(--visualization-bg-3)')
														.attr('class', 'hover-bg');
								 }
							 })
							 .on('mouseout', function(event: MouseEvent) {
								 event.stopPropagation();
								 // Return to the current animation state opacity
								 node.style("opacity", String(initialOpacity));

								 // Restore original fill color
								 node.select('rect').attr('fill', 'white');
								 iconGroup.select('.icon-path').attr('fill', 'var(--visualization-bg-6)');

								 d3.select(".custom-tooltip").style("opacity", "0");
								 // Remove the background
								 iconGroup.selectAll('.hover-bg').remove();
							 })
							 .on('mousemove', function(event: MouseEvent) {
								 d3.select(".custom-tooltip")
									 .style("left", ((event.pageX + 10)/10) + "rem")
									 .style("top", ((event.pageY + 10)/10) + "rem");
							 })
							 .on('click', function(event: MouseEvent) {
								 // Only respond to clicks if the node is still somewhat visible
								 if (parseFloat(node.style("opacity") as string) > 0.1) {
									 event.stopPropagation();
									 event.preventDefault();
									 const datasource = window.visualizationMainDatasource;
									 const datasourceSlug = d.slug;
									 const datastoreSlug = datasource.dataStore.slug;
									 window.open(`/Admin/DataStores/${datastoreSlug}/DataSources/${datasourceSlug}/Connections`, '_blank');
								 }
							 });
		});
	}

	/**
	 * Shows a tooltip for a node
	 * @param _event
	 * @param nodeData - The data for the node
	 * @param nodeGroup - The D3 selection for the node group
	 */
	nodeShowTooltip(_event: MouseEvent, nodeData: Node, nodeGroup: d3.Selection<SVGGElement, unknown, null, undefined>): void {
		this.closeAllTooltips();
		type PositionKey = 'top' | 'right' | 'bottom' | 'left';

		const nodeElement = nodeGroup.node();
		// @ts-ignore
		if (!nodeElement || !nodeElement.getBoundingClientRect) {
			console.warn("Could not find node element for tooltip");
			return;
		}

		try {
			// @ts-ignore
			const bbox = nodeElement.getBoundingClientRect();
			const tooltipWidth = 35;
			const tooltipHeight = 35; 
			const viewportWidth = window.innerWidth;
			const viewportHeight = window.innerHeight;

			// Calculate positions for all possible placements
			const BOTTOM_SPACING = 1;
			const TOP_SPACING = 0.2;
			const positions = {
				right: {
					left: bbox.right + 10, 
					top: bbox.top
				},
				left: {
					left: bbox.left - tooltipWidth - 10, 
					top: bbox.top
				},
				bottom: {
					left: bbox.left + (bbox.width / 2) - (tooltipWidth / 2),
					top: bbox.bottom + BOTTOM_SPACING
				},
				top: {
					left: bbox.left + (bbox.width / 2) - (tooltipWidth / 2),
					top: bbox.top - tooltipHeight - TOP_SPACING + 10 
				}
			};

			// First, check if there's enough space at the bottom
			let bestPosition = 'bottom' as PositionKey;
			const hasSpaceAtBottom = (bbox.bottom + BOTTOM_SPACING + tooltipHeight) <= viewportHeight;
			const hasSpaceAtTop = (bbox.top - tooltipHeight - TOP_SPACING) >= 0;

			// If no space at bottom, try top
			if (!hasSpaceAtBottom && hasSpaceAtTop) {
				bestPosition = 'top';
			}
			// If neither top nor bottom fits, try right
			else if (positions.right.left + tooltipWidth <= viewportWidth) {
				bestPosition = 'right';
			}
			// Finally, try left
			else if (positions.left.left >= 0) {
				bestPosition = 'left';
			}
			// If nothing else works, default to top
			else {
				bestPosition = 'top';
			}

			// Create tooltip container
			const tooltipId = `tooltip-${nodeData.id}`;
			const tooltip = d3.select("body")
									.append("div")
									.attr("id", tooltipId)
									.attr("class", "graph-tooltip")
									.style("position", "absolute")
									.style("border", "var(--size-spacing-l) solid var(--clr-black-100)")
									.style("box-shadow", "0 var(--size-spacing-xs) var(--size-spacing-s) var(--clr-black-900)")
									.style("border-radius", "var(--size-radius-m)")
									.style("left", `${positions[bestPosition].left/10}rem`) 
									.style("top", `${positions[bestPosition].top/10}rem`) 
									.style("width", `${tooltipWidth}rem`)
									.style("z-index", "1000");

			// Store reference to this tooltip
			// @ts-ignore
			this.tooltips.set(nodeData.id, {
				tooltip,
				nodeGroup,
				update: () => this.updateTooltipPosition(nodeData.id)
			});

			if ((nodeData?.connectedFields?.current?.length ?? 0) > 0) {
				this.addTooltipContent(tooltip, nodeData);
			}
			else {
				tooltip.append("p")
					.style("margin", "var(--size-spacing-m) 0 0 0")
					.style("line-height", "1.5")
					.style("color", "var(--visualization-txt-5)")
					.text(_localizer.localize("ThisDataSourceDoesNotHaveAnyConnectedFields"));
			}

			// Add tooltip content
			// Close button
			tooltip.append("div")
						.attr("class", "tooltip-close")
						.text("×")
						.on("click", () => this.closeTooltip(nodeData.id));

			// Close when clicking outside
			d3.select("body").on(`click.tooltip-${nodeData.id}`, (e: MouseEvent) => {
				if (!e.target ||
					!(e.target as Element).closest(".graph-tooltip") &&
					!(e.target as Element).closest(`.node-group[data-id="${nodeData.id}"]`)) {
					this.closeTooltip(nodeData.id);
				}
			});

		} catch (error) {
			console.error("Error showing tooltip:", error);
		}
	}

	/**
	 * Handles a click event on a node
	 * Differentiates between single and double clicks
	 * @param event - The mouse event
	 * @param nodeData - The data for the node
	 * @param nodeGroup - The D3 selection for the node group
	 */
	handleNodeClick(event: MouseEvent, nodeData: Node, nodeGroup: d3.Selection<SVGGElement, unknown, null, undefined>): void {
		if (this.clickTimeout) {
			clearTimeout(this.clickTimeout);
			this.clickTimeout = null;
		}

		// Check if this is a double click
		if (event.detail > 1) {
			// Double click - make this node the new root
			this.handleNodeDoubleClick(nodeData);
		} else {
			// Single click - show tooltip after a short delay
			this.clickTimeout = window.setTimeout(() => {
				this.nodeShowTooltip(event, nodeData, nodeGroup);
			}, 200); // 200ms delay to check for double click
		}
	}

	/**
	 * Handles a double-click event on a node
	 * Makes the clicked node the new root of the visualization
	 * @param nodeData - The data for the node
	 */
	handleNodeDoubleClick(nodeData: Node): void {
		if (nodeData.id) {
			// Close any open tooltips
			this.closeAllTooltips();

			// Initialize visualization with the new node as root
			initializeVisualization(nodeData.id, "pages");
		}
	}

	/**
	 * Updates the position of a tooltip based on the node's position
	 * @param nodeId - The ID of the node associated with the tooltip
	 */
	updateTooltipPosition(nodeId: string): void {
		type PositionKey = 'top' | 'right' | 'bottom' | 'left';
		// @ts-ignore
		const tooltipData = this.tooltips.get(nodeId);
		if (!tooltipData) return;

		// @ts-ignore
		const nodeBbox = tooltipData.nodeGroup.node()?.getBoundingClientRect();
		if (!nodeBbox) return;

		const tooltipWidth = parseInt(tooltipData.tooltip.style("width") || "0");
		const tooltipHeight = tooltipData.tooltip.node()?.getBoundingClientRect().height || 0;
		const viewportWidth = window.innerWidth;
		const viewportHeight = window.innerHeight;

		// Calculate positions for all possible placements
		const BOTTOM_SPACING = 10;
		const TOP_SPACING = 5;
		const positions = {
			right: {
				left: nodeBbox.right + 10,
				top: nodeBbox.top
			},
			left: {
				left: nodeBbox.left - tooltipWidth - 10,
				top: nodeBbox.top
			},
			bottom: {
				left: nodeBbox.left + (nodeBbox.width / 2) - (tooltipWidth / 2),
				top: nodeBbox.bottom + BOTTOM_SPACING
			},
			top: {
				left: nodeBbox.left + (nodeBbox.width / 2) - (tooltipWidth / 2),
				top: nodeBbox.top + tooltipHeight + TOP_SPACING
			}
		};

		// Use the same position selection logic as nodeShowTooltip
		let bestPosition = 'right' as PositionKey;

		if (positions.right.left + tooltipWidth > viewportWidth) {
			if (positions.left.left >= 0) {
				bestPosition = 'left';
			} else {
				const hasSpaceAtBottom = positions.bottom.top + tooltipHeight <= viewportHeight;
				const hasSpaceAtTop = positions.top.top >= 0;

				if (hasSpaceAtTop) {
					bestPosition = 'top';
				} else if (hasSpaceAtBottom) {
					bestPosition = 'bottom';
				} else {
					bestPosition = 'top';
				}
			}
		}
		else if (positions.right.top + tooltipHeight > viewportHeight) {
			if (positions.top.top >= 0) {
				bestPosition = 'top';
			} else if (positions.left.left >= 0) {
				bestPosition = 'left';
			} else {
				bestPosition = 'top';
			}
		}

		// Update tooltip position
		tooltipData.tooltip
							 .style("left", `${positions[bestPosition].left/10}rem`)
							 .style("top", `${positions[bestPosition].top/10}rem`)
							 .style("visibility", "visible");
	}

	/**
	 * Closes a tooltip and removes its event listeners
	 * @param nodeId - The ID of the node associated with the tooltip
	 */
	closeTooltip(nodeId: string): void {
		// @ts-ignore
		const tooltipData = this.tooltips.get(nodeId);
		if (tooltipData) {
			tooltipData.tooltip.remove();
			d3.select("body").on(`click.tooltip-${nodeId}`, null);
			// @ts-ignore
			this.tooltips.delete(nodeId);
		}
	}

	/**
	 * Closes all active tooltips and clears the tooltips map
	 */
	closeAllTooltips(): void {
		// @ts-ignore
		this.tooltips.forEach((value, nodeId) => {
			this.closeTooltip(nodeId);
		});
		// @ts-ignore
		this.tooltips.clear(); // Clear the map
	}

	/**
	 * Adds content to a tooltip
	 * @param tooltip - The D3 selection for the tooltip
	 * @param nodeData - The data for the node
	 */
	addTooltipContent(tooltip: d3.Selection<HTMLDivElement, unknown, HTMLElement, any>, nodeData: Node): void {
    // Header with bottom border
    const header = tooltip.append("div")
                    .style("padding-bottom", "var(--size-spacing-m)")
                    .style("margin-bottom", "var(--size-spacing-m)")
                    .style("border-bottom", "var(--size-spacing-xs) solid var(--visualization-txt-0)");

    header.append("div")
                .style("font-size", "var(--font-size-2)")
                .style("color", "var(--visualization-txt-1)")
                .style("font-weight", "600")
                .text(`${_localizer.localize("ConnectedDataFields")} (${nodeData.name})`);

    // Content wrapper with fixed height and scroll
    const contentWrapper = tooltip.append("div")
                            .style("height", "19rem")
                            .style("overflow-y", "auto")
                            .style("overflow-x", "hidden")
                            .style("padding-right", "var(--size-spacing-s)")
                            .style("scrollbar-width", "thin")
                            .style("scrollbar-color", "var(--clr-black-100) var(--clr-black-100)");

    // Add webkit scrollbar styles
    contentWrapper.append("style")
                                .html(`
        .tooltip-content::-webkit-scrollbar {
            width: var(--size-spacing-m);
        }
        .tooltip-content::-webkit-scrollbar-track {
            background: var(--clr-black-100);
            border-radius: var(--size-radius-m);
        }
        .tooltip-content::-webkit-scrollbar-thumb {
            background: var(--clr-black-100);
            border-radius: var(--size-radius-m);
        }
    `);

    // Main content container
    const content = contentWrapper.append("div")
                        .attr("class", "tooltip-content")
                        .style("display", "flex")
                        .style("flex-direction", "column")
                        .style("width", "100%");

    const connectedFieldsData = nodeData.connectedFields;
    if (!connectedFieldsData) return;

    // Create column headers container
    const headersContainer = content.append("div")
                                .style("display", "flex")
                                .style("width", "100%")
                                .style("position", "sticky")
                                .style("top", "0")
                                .style("background-color", "var(--clr-black-100)")
                                .style("z-index", "1")
                                .style("padding-bottom", "var(--size-spacing-s)");

    // Create current fields header
    headersContainer.append("div")
                    .style("flex", "1")
                    .style("padding", "0 var(--size-spacing-s)")
                    .style("font-size", "var(--font-size-1)")
                    .style("color", "var(--visualization-txt-5)")
                    .style("font-weight", "500")
                    .style("text-decoration", "underline")
					.style("background-color", "var(--clr-black-100)")
                    .text(_localizer.localize("Current"));

    // Add empty div for arrow column header
    headersContainer.append("div")
                    .style("width", "var(--size-spacing-xxxl)")
                    .style("flex", "none");

    // Create dependent fields header
    headersContainer.append("div")
                    .style("flex", "1")
                    .style("padding", "0 var(--size-spacing-s)")
                    .style("font-size", "var(--font-size-1)")
                    .style("color", "var(--visualization-txt-5)")
                    .style("font-weight", "500")
                    .style("text-decoration", "underline")
					.style("background-color", "var(--clr-black-100)")
                    .text(_localizer.localize("Dependent"));

    // Create rows container
    const rowsContainer = content.append("div")
                                .style("display", "flex")
                                .style("flex-direction", "column")
                                .style("width", "100%");

    // Helper function to create field row
    const createFieldRow = (currentField: Field, dependentField: Field | undefined) => {
        if (!dependentField) return;

        const row = rowsContainer.append("div")
                            .style("display", "flex")
                            .style("align-items", "center")
                            .style("margin-bottom", "var(--size-spacing-s)")
                            .style("width", "100%");

        // Current field column
        const currentCol = row.append("div")
                            .style("flex", "1")
                            .style("padding", "0 var(--size-spacing-s)")
                            .style("display", "flex")
                            .style("align-items", "center");

        currentCol.append("div")
                    .style("font-size", "var(--font-size-1)")
                    .style("color", currentField.isKey ? "var(--visualization-txt-1)" : "var(--visualization-txt-6)")
                    .style("font-weight", currentField.isKey ? "500" : "normal")
                    .text(currentField.name);

        if (currentField.isKey) {
            currentCol.append("div")
                        .style("margin-left", "var(--size-spacing-m)")
                        .style("width", "var(--size-spacing-m)")
                        .style("height", "var(--size-spacing-m)")
                        .style("display", "inline-flex")
                        .style("align-items", "center")
                        .style("justify-content", "center")
                        .html(`
                <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                </svg>
            `);
        }

        // Arrow column
        row.append("div")
            .style("width", "var(--size-spacing-l)")
            .style("flex", "none")
            .style("display", "flex")
            .style("justify-content", "center")
            .style("color", "var(--visualization-txt-6)")
            .style("font-size", "var(--font-size-1)")
            .text("→");

        // Dependent field column
        const dependentCol = row.append("div")
                            .style("flex", "1")
                            .style("padding", "0 var(--size-spacing-s)")
                            .style("padding-left", "var(--size-spacing-xl)")
                            .style("display", "flex")
                            .style("align-items", "center");

        dependentCol.append("div")
                    .style("font-size", "var(--font-size-1)")
                    .style("color", dependentField.isKey ? "var(--visualization-txt-1)" : "var(--visualization-txt-6)")
                    .style("font-weight", dependentField.isKey ? "500" : "normal")
                    .text(dependentField.name);

        if (dependentField.isKey) {
            dependentCol.append("div")
                        .style("margin-left", "var(--size-spacing-m)")
                        .style("width", "var(--size-spacing-m)")
                        .style("height", "var(--size-spacing-m)")
                        .style("display", "inline-flex")
                        .style("align-items", "center")
                        .style("justify-content", "center")
                        .html(`
                <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                </svg>
            `);
        }
    };

    // Create fields
    if (connectedFieldsData.current && connectedFieldsData.dependent) {
        connectedFieldsData.current.forEach((currentField, index) => {
            const dependentField = connectedFieldsData.dependent?.[index];
            createFieldRow(currentField, dependentField);
        });
    }
}

	/**
	 * Shows a help tooltip with information about how to use the visualization
	 * @param event - Optional mouse event that triggered the tooltip
	 */
	showTooltip(event?: MouseEvent): void {
		// Accept event as parameter for D3 v6+
		if (event) {
			event.preventDefault();
			event.stopPropagation();
		}

		// Remove any existing tooltips
		d3.select(".tooltip-container").remove();

		// Create tooltip container
		const tooltipContainer = d3.select("body")
								.append("div")
								.attr("class", "tooltip-container")
								.style("position", "absolute")
								.style("background", "var(--clr-black-100)")
								.style("border", "var(--size-spacing-l) solid var(--clr-black-100)")
								.style("border-radius", "var(--size-radius-m)")
								.style("padding", "var(--size-spacing-m)")
								.style("width", "40rem")
								.style("box-shadow", "0 var(--size-spacing-xs) var(--size-spacing-s) var(--clr-black-900)")
								.style("z-index", "1000")
								.style("font-family", this.fontFamily)
								.style("font-size", "var(--font-size-1)")
								.style("color", "var(--clr-black-700)");

		// Position tooltip near the icon
		// @ts-ignore
		const iconPos = d3.select(this as unknown as d3.BaseType).node()?.getBoundingClientRect();
		if (iconPos) {
			tooltipContainer
				.style("left", `${(iconPos.right + 10)/10}rem`)
				.style("top", `${(iconPos.top - 250)/10}rem`);
		}

		// Add title
		tooltipContainer.append("h3")
										.style("margin", "0 0 var(--size-spacing-m) 0")
										.style("font-size", "var(--font-size-2)")
										.style("font-weight", "bold")
										.text(_localizer.localize("UnderstandingDataSourceLinks"));

		// Add description text
		tooltipContainer.append("p")
										.style("margin", "0 0 var(--size-spacing-m) 0")
										.style("line-height", "var(--size-spacing-l)")
										.text(_localizer.localize("DataSourceVisulizationInformationDescription"));

		// Add functions title
		tooltipContainer.append("h4")
										.style("margin", "var(--size-text-l) 0 var(--size-spacing-m) 0")
										.style("font-size", "var(--font-size-1)")
										.style("font-weight", "bold")
										.text("Functions");

		// Add list of functions
		const functionsList = tooltipContainer.append("ul")
											.style("margin", "0")
											.style("padding-left", "var(--size-spacing-l)");

		functionsList.append("li")
								 .style("margin-bottom", "var(--size-spacing-s)")
								 .html(`<strong>${_localizer.localize("SingleClick")}</strong> → ${_localizer.localize("SingleClickDescription")}`);

		functionsList.append("li")
								 .html(`<strong>${_localizer.localize("DoubleClick")}</strong> → ${_localizer.localize("DoubleClickDescription")}`);

		// Add close button
		tooltipContainer.append("div")
										.style("position", "absolute")
										.style("top", "var(--size-spacing-s)")
										.style("right", "var(--size-spacing-s)")
										.style("cursor", "pointer")
										.style("font-size", "var(--font-size-1)")
										.text("×")
										.on("click", function() {
											d3.select(".tooltip-container").remove();
										})


		// Close tooltip when clicking outside
		d3.select("body").on("click.tooltip", function(clickEvent: MouseEvent) {
			// Only proceed if we have an event
			if (!clickEvent) return;

			// Get target element from the event
			const targetElement = clickEvent.target as Element;

			if (!targetElement.closest(".tooltip-container") &&
				!targetElement.closest(".help-icon")) {
				d3.select(".tooltip-container").remove();
				d3.select("body").on("click.tooltip", null);
			}
		});


	}

	/**
	 * Expands the node layout to accommodate additional nodes beyond the initial clock positions
	 * @param nodes - The array of nodes to position
	 * @param centerX - X-coordinate of the center point
	 * @param centerY - Y-coordinate of the center point
	 * @param baseRadius - The base radius for the core clock positions
	 */
	setupExpandedNodePositions(nodes: Node[], centerX: number, centerY: number, baseRadius: number): void {
		if (!nodes || nodes.length <= 12) {
			// If we have 12 or fewer nodes, use the original clock positioning
			return;
		}

		// Find root node
		const rootNode = nodes.find(n => n.highlighted);
		if (!rootNode) return;

		// Position root node at center and fix it
		rootNode.x = centerX;
		rootNode.y = centerY;
		rootNode.fx = centerX;
		rootNode.fy = centerY;
		window.sourceNode = rootNode;

		// Separate non-root nodes
		const childNodes = nodes.filter(node => !node.highlighted);

		// Initial layer contains 12 positions (clock face positions, 1-12)
		const initialLayerCount = 12;

		// Set up the initial clock positions first
		this.setupClockPositions(childNodes.slice(0, initialLayerCount), centerX, centerY, baseRadius);

		// If there are more nodes, set up expanded positions
		if (childNodes.length > initialLayerCount) {
			this.setupExpansionLayers(
				childNodes.slice(initialLayerCount),
				centerX,
				centerY,
				baseRadius
			);
		}
	}

	/**
	 * Sets up the initial clock positions for the first 12 nodes
	 * @param nodes - The first 12 non-root nodes to position
	 * @param centerX - X-coordinate of the center
	 * @param centerY - Y-coordinate of the center
	 * @param radius - Base radius for positioning
	 */
	setupClockPositions(nodes: Node[], centerX: number, centerY: number, radius: number): void {
		// Define horizontal radius (slightly larger than vertical)
		const horizontalRadius = radius * 1.2;

		// Set positions for initial clock positions
		nodes.forEach((node, i) => {
			switch (i) {
				case 0: // 6 o'clock (bottom center)
					node.x = centerX;
					node.y = centerY + radius / 2;
					node.position = 6;
					break;
				case 1: // 7 o'clock (bottom left)
					node.x = centerX - radius * Math.cos(Math.PI / 6);
					node.y = centerY + radius * Math.sin(Math.PI / 6);
					node.position = 7;
					break;
				case 2: // 5 o'clock (bottom right)
					node.x = centerX + radius * Math.cos(Math.PI / 6);
					node.y = centerY + radius * Math.sin(Math.PI / 6);
					node.position = 5;
					break;
				case 3: // 12 o'clock (top center)
					node.x = centerX;
					node.y = centerY - radius / 2;
					node.position = 12;
					break;
				case 4: // 11 o'clock (top left)
					node.x = centerX - radius * Math.cos(Math.PI / 6);
					node.y = centerY - radius * Math.sin(Math.PI / 6);
					node.position = 11;
					break;
				case 5: // 1 o'clock (top right)
					node.x = centerX + radius * Math.cos(Math.PI / 6);
					node.y = centerY - radius * Math.sin(Math.PI / 6);
					node.position = 1;
					break;
				case 6: // 3 o'clock (right center)
					node.x = centerX + horizontalRadius*2;
					node.y = centerY;
					node.position = 3;
					break;
				case 7: // 9 o'clock (left center)
					node.x = centerX - horizontalRadius*2;
					node.y = centerY;
					node.position = 9;
					break;
				case 8: // 10 o'clock (top left)
					node.x = centerX - (radius * 2) * Math.cos(Math.PI / 6);
					node.y = centerY - radius * Math.sin(Math.PI / 6);
					node.position = 10;
					break;
				case 9: // 2 o'clock (top right)
					node.x = centerX + (radius * 2) * Math.cos(Math.PI / 6);
					node.y = centerY - radius * Math.sin(Math.PI / 6);
					node.position = 2;
					break;
				case 10: // 4 o'clock (bottom right)
					node.x = centerX + (radius * 2) * Math.cos(Math.PI / 6);
					node.y = centerY + radius * Math.sin(Math.PI / 6);
					node.position = 4;
					break;
				case 11: // 8 o'clock (bottom left)
					node.x = centerX - (radius * 2) * Math.cos(Math.PI / 6);
					node.y = centerY + radius * Math.sin(Math.PI / 6);
					node.position = 8;
					break;
			}

			// Fix positions permanently
			node.fx = node.x;
			node.fy = node.y;
		});
	}

	/**
	 * Sets up expansion layers following the structured dynamic positioning
	 * @param extraNodes - Nodes beyond the initial 12 clock positions
	 * @param centerX - X-coordinate of the center
	 * @param centerY - Y-coordinate of the center
	 * @param baseRadius - Base radius for positioning
	 */
	setupExpansionLayers(extraNodes: Node[], centerX: number, centerY: number, baseRadius: number): void {
		// CONSTANT VERTICAL SPACING between ALL layers
		const LAYER_VERTICAL_SPACING = 50; // Fixed vertical distance between any two consecutive layers

		// Define initial southern and northern hemisphere counts
		const initialSouthernCount = 5; // 4, 5, 6, 7, 8 o'clock
		const initialNorthernCount = 5; // 10, 11, 12, 1, 2 o'clock

		// Calculate how many layers we need based on remaining nodes
		let remainingNodes = extraNodes.length;
		let layers = 0;
		let southernHemisphereSize = initialSouthernCount;
		let northernHemisphereSize = initialNorthernCount;
		let currentLayerNodeCount = 0;
		let nodesAccounted = 0;

		// Calculate how many expansion layers we need
		while (remainingNodes > 0) {
			layers++;
			let isEvenLayer = layers % 2 === 0;

			// Alternate between southern and northern expansion
			if (isEvenLayer) {
				northernHemisphereSize++;
				currentLayerNodeCount = northernHemisphereSize;
			} else {
				southernHemisphereSize++;
				currentLayerNodeCount = southernHemisphereSize;
			}

			nodesAccounted += currentLayerNodeCount;
			remainingNodes = extraNodes.length - nodesAccounted;
		}

		// Reset counters for actual node placement
		southernHemisphereSize = initialSouthernCount;
		northernHemisphereSize = initialNorthernCount;
		let processedNodes = 0;

		// Base vertical distance for the first layer from center
		// This is the distance from the root node to the first layer in either direction
		const firstLayerDistance = baseRadius;
		const divider = 1.2;
		// Now place nodes in each expansion layer
		for (let layer = 1; layer <= layers; layer++) {
			const isEvenLayer = layer % 2 === 0;
			let layerNodeCount;

			if (isEvenLayer) {
				// Northern expansion (above)
				northernHemisphereSize++;
				layerNodeCount = northernHemisphereSize;

				// Calculate northern layer number (1, 2, 3, etc.)
				const northernLayerNumber = Math.ceil(layer / (1.6));

				// FIXED vertical distance calculation - each northern layer is exactly LAYER_VERTICAL_SPACING from the previous
				const verticalPosition = firstLayerDistance + ((northernLayerNumber - 1) * LAYER_VERTICAL_SPACING);

				// Place northern hemisphere nodes
				this.placeNorthernLayerNodes(extraNodes.slice(processedNodes, processedNodes + layerNodeCount), centerX, centerY, baseRadius,
					verticalPosition, // Distance from center
					northernLayerNumber // Layer number
				);
			}
			else {
				// Southern expansion (below)
				southernHemisphereSize++;
				layerNodeCount = southernHemisphereSize;

				// Calculate southern layer number (1, 2, 3, etc.)
				const southernLayerNumber = Math.ceil(layer / divider);

				// FIXED vertical distance calculation - each southern layer is exactly LAYER_VERTICAL_SPACING from the previous
				const verticalPosition = firstLayerDistance + ((southernLayerNumber - 1) * LAYER_VERTICAL_SPACING);

				// Place southern hemisphere nodes
				this.placeSouthernLayerNodes(
					extraNodes.slice(processedNodes, processedNodes + layerNodeCount),
					centerX,
					centerY,
					baseRadius,
					verticalPosition, // Distance from center
					southernLayerNumber // Layer number
				);
			}

			// Update processed nodes count
			processedNodes += layerNodeCount;

			// Break if we've placed all nodes
			if (processedNodes >= extraNodes.length) break;
		}
	}

	/**
	 * Places nodes in a southern expansion layer (below)
	 * @param nodes - The nodes to position in this layer
	 * @param centerX - X-coordinate of the center
	 * @param centerY - Y-coordinate of the center
	 * @param baseRadius - Base radius for calculations
	 * @param verticalPosition - Fixed vertical distance from center for this layer
	 * @param layerNumber - The layer number (1, 2, 3, etc.)
	 */
	placeSouthernLayerNodes(nodes: Node[], centerX: number, centerY: number, baseRadius: number, verticalPosition: number, layerNumber: number): void {
		if (!nodes.length) return;

		// Calculate positioning parameters
		// @ts-ignore
		const nodeCount = nodes.length <= 6 ? 6 : nodes.length;
		const layerWidth = baseRadius * (nodeCount/2); // Width of the layer
		const spacing = layerWidth / (nodeCount - (nodeCount > 4 ? 3 : 1)); // Spacing between nodes

		// Calculate starting X position (left edge of the layer)
		const startX = centerX - (layerWidth / (nodeCount > 4 ? 1.2 : 2));

		// Calculate actual Y position (below center)
		const layerY = centerY + verticalPosition;

		// Place nodes in a horizontal row below
		nodes.forEach((node, index) => {
			// Calculate x position with even spacing
			node.x = startX + (index * spacing);
			// Fixed y position based on vertical position
			node.y = layerY;

			// Assign a position value between 100-199 to indicate southern expansion layer
			node.position = 100 + index;

			// Store which expansion layer this node belongs to
			node.expansionLayer = {
				hemisphere: 'southern',
				layer: layerNumber
			};

			// Fix positions permanently
			node.fx = node.x;
			node.fy = node.y;
			node.layerNumber = layerNumber;

			// Important: Tag these nodes as connected to the southern trunk (6 o'clock)
			node.trunkConnection = 'southern';
		});
	}

	/**
	 * Places nodes in a northern expansion layer (above)
	 * @param nodes - The nodes to position in this layer
	 * @param centerX - X-coordinate of the center
	 * @param centerY - Y-coordinate of the center
	 * @param baseRadius - Base radius for calculations
	 * @param verticalPosition - Fixed vertical distance from center for this layer
	 * @param layerNumber - The layer number (1, 2, 3, etc.)
	 */
	placeNorthernLayerNodes(nodes: Node[], centerX: number, centerY: number, baseRadius: number, verticalPosition: number, layerNumber: number): void {
		if (!nodes.length) return;

		// Calculate positioning parameters
		const nodeCount = nodes.length < 6 ? 7 : nodes.length;
		const layerWidth = baseRadius * (nodeCount/2); // Width of the layer
		const spacing = layerWidth / (nodeCount - (nodeCount > 4 ? 3 : 1)); // Spacing between nodes

		// Calculate starting X position (left edge of the layer)
		const startX = centerX - (layerWidth / (nodeCount > 4 ? 1.2 : 2));

		// Calculate actual Y position (above center)
		const layerY = centerY - verticalPosition;

		// Place nodes in a horizontal row above
		nodes.forEach((node, index) => {
			// Calculate x position with even spacing
			node.x = startX + (index * spacing);
			// Fixed y position based on vertical position
			node.y = layerY;
			node.layerNumber = layerNumber;

			// Assign a position value between 200-299 to indicate northern expansion layer
			node.position = 200 + index;

			// Store which expansion layer this node belongs to
			node.expansionLayer = {
				hemisphere: 'northern',
				layer: layerNumber
			};

			// Fix positions permanently
			node.fx = node.x;
			node.fy = node.y;

			// Important: Tag these nodes as connected to the northern trunk (12 o'clock)
			node.trunkConnection = 'northern';
		});
	}

	/**
	 * Determines the appropriate link path based on whether it's a reversed link
	 * @param d - The link data
	 * @returns SVG path string for the link
	 */
	determineIfReversed(d: Link): string {
		// Get source and target nodes, using cached references when available
		const source = d._sourceRef || (typeof d.source === 'object' ? d.source : // @ts-ignore
			this.data.nodes.find(n => n.id === d.source));
		const target = d._targetRef || (typeof d.target === 'object' ? d.target : // @ts-ignore
			this.data.nodes.find(n => n.id === d.target));

		// Cache references to avoid repeated lookups
		d._sourceRef = source;
		d._targetRef = target;

		// Calculate bundle key if not already done
		if (!d._bundleKey && source?.expansionLayer && target?.expansionLayer) {
			const startX = (source.x || 0) * 2;
			const startY = (source.y || 0) * 2;
			const endX = (target.x || 0) * 2;
			const endY = (target.y || 0) * 2;

			// Determine direction quadrant
			const dx = endX - startX;
			const dy = endY - startY;
			const angle = Math.atan2(dy, dx);
			let direction = "";

			if (angle > -Math.PI/8 && angle < Math.PI/8) direction = "east";
			else if (angle >= Math.PI/8 && angle < 3*Math.PI/8) direction = "southeast";
			else if (angle >= 3*Math.PI/8 && angle < 5*Math.PI/8) direction = "south";
			else if (angle >= 5*Math.PI/8 && angle < 7*Math.PI/8) direction = "southwest";
			else if (angle >= 7*Math.PI/8 || angle < -7*Math.PI/8) direction = "west";
			else if (angle >= -7*Math.PI/8 && angle < -5*Math.PI/8) direction = "northwest";
			else if (angle >= -5*Math.PI/8 && angle < -3*Math.PI/8) direction = "north";
			else direction = "northeast";

			// Create and store bundle key
			d._bundleKey = `${source.expansionLayer.hemisphere}-${source.expansionLayer.layer}-${target.expansionLayer.hemisphere}-${target.expansionLayer.layer}-${direction}`;

			// Initialize bundle registry if needed
			// @ts-ignore
			if (!window.linkBundles) window.linkBundles = {};
			// @ts-ignore
			if (!window.linkBundles[d._bundleKey]) {
				// @ts-ignore
				window.linkBundles[d._bundleKey] = {
					links: [],
					pathCalculated: false,
					path: null
				};
			}

			// Add this link to its bundle
			// @ts-ignore
			window.linkBundles[d._bundleKey].links.push(d);
		}

		// Generate the appropriate path
		if (d.isLinkReversed === true) return this.createReversedSimpleLink(d);
		return this.createSimpleLink(d);
	}

	/**
	 * Creates a path for a standard link between nodes
	 * @param d - The link data
	 * @returns SVG path string for the link
	 */
	createSimpleLink(d: Link): string {
		const source = d._sourceRef || (typeof d.source === 'object' ? d.source : // @ts-ignore
			this.data.nodes.find(n => n.id === d.source));
		const target = d._targetRef || (typeof d.target === 'object' ? d.target : // @ts-ignore
			this.data.nodes.find(n => n.id === d.target));

		if (!source || !target) return 'M0,0';

		// Calculate direction vector
		const dx = (target.x || 0) - (source.x || 0);
		const dy = (target.y || 0) - (source.y || 0);
		const length = Math.sqrt(dx * dx + dy * dy);

		if (length === 0) return 'M0,0';

		// Normalized direction vector
		const nx = dx / length;
		const ny = dy / length;

		// Check if source or target is at a critical clock position (1-12 o'clock)
		// or if source is the highlighted root node - these must be preserved
		const isCriticalNode = (node: Node): boolean => {
			return [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].includes(node.position || 0) ||
				node.highlighted === true;
		};

		// Use original path algorithm for critical nodes
		if (isCriticalNode(target)) {
			return this.createOriginalSimpleLink(d, source, target, nx, ny);
		}

		// For links between non-critical nodes, use optimized path algorithm
		return this.createOptimizedLink(d, source, target, nx, ny);
	}

	/**
	 * Preserves the original path algorithm for critical nodes
	 * This ensures backwards compatibility with existing visualizations
	 * @param d - The link data
	 * @param source - The source node
	 * @param target - The target node
	 * @param nx - Normalized x direction
	 * @param ny - Normalized y direction
	 * @returns SVG path string for the link
	 */
	createOriginalSimpleLink(_d: Link, source: Node, target: Node, nx: number, ny: number): string {
		const multiplier = 2;

		// Node dimensions
		const nodeWidth = this.cardWidth + (this.cardMargin * 2);
		const nodeHeight = this.cardHeight + (this.cardMargin * 2);
		const padding = 15; // Space between node edge and link

		// Calculate exit points from nodes
		const getEdgePoint = (node: Node, isSource: boolean): { x: number, y: number } => {
			const nodeX = node.x || 0;
			const nodeY = node.y || 0;

			// Special handling for clock positions at cardinal and intermediate directions
			switch (node.position) {
				case 3: // 3 o'clock (right center)
					return {
						x: (nodeX * multiplier) - nodeWidth/6 - padding,
						y: nodeY * multiplier
					};
				case 9: // 9 o'clock (left center)
					return {
						x: (nodeX * multiplier) + nodeWidth/6 + padding,
						y: nodeY * multiplier
					};
				case 2: // 2 o'clock (top right)
					return {
						x: (nodeX * multiplier) + nodeWidth/2.1,
						y: (nodeY * multiplier) + nodeHeight/3 + padding
					};
				case 10: // 10 o'clock (top left)
					return {
						x: (nodeX * multiplier) - nodeWidth/2.1,
						y: (nodeY * multiplier) + nodeHeight/4 + padding
					};
				case 4: // 4 o'clock (bottom right)
					return {
						x: (nodeX * multiplier) + nodeWidth/2.1,
						y: (nodeY * multiplier) - nodeHeight/3 - padding
					};
				case 8: // 8 o'clock (bottom left)
					return {
						x: (nodeX * multiplier) - nodeWidth/2.1,
						y: (nodeY * multiplier) - nodeHeight/3 - padding
					};
			}

			// For root node (center)
			if (node.highlighted) {
				// If it's the source node, use fixed exit points based on direction
				if (isSource) {
					// Determine if the target is on the left or right side
					const dx = ((target.x || 0) - (source.x || 0));
					const dy = ((target.y || 0) - (source.y || 0));
					const isRightSide = dx > 0;
					const isTopSide = dy < 0;

					// For left/right sides, use fixed exit points at 3 and 9 o'clock
					if (Math.abs(dx) > Math.abs(dy)) {
						return {
							x: nodeX * multiplier + (isRightSide ? (nodeWidth/2 + padding) : -(nodeWidth/2 + padding)),
							y: nodeY * multiplier
						};
					}
					// For top/bottom sides, use fixed exit points at 12 and 6 o'clock
					else {
						return {
							x: nodeX * multiplier,
							y: nodeY * multiplier + (isTopSide ? -(nodeHeight/2 + padding) : (nodeHeight/2 + padding))
						};
					}
				}
				// If it's not a source node, use the original calculation
				return {
					x: nodeX * multiplier,
					y: nodeY * multiplier
				};
			}

			// For non-root nodes not handled by specific position cases above,
			// use the original angle-based calculation
			const dx = ((target.x || 0) - (source.x || 0));
			const dy = ((target.y || 0) - (source.y || 0));
			const angle = Math.atan2(dy, dx) * (180 / Math.PI);

			// Determine which side of the node to exit from based on angle
			if (angle > -45 && angle < 45) { // Right side
				return {
					x: (nodeX * multiplier) + nodeWidth/3 + padding,
					y: nodeY * multiplier
				};
			}
			else if (angle > 45 && angle < 135) { // Bottom side
				return {
					x: nodeX * multiplier,
					y: (nodeY * multiplier) + nodeHeight/4 + padding
				};
			}
			else if (angle > 135 || angle < -135) { // Left side
				return {
					x: (nodeX * multiplier) - nodeWidth/3 - padding,
					y: nodeY * multiplier
				};
			}
			else { // Top side
				return {
					x: nodeX * multiplier,
					y: (nodeY * multiplier) - nodeHeight/4 - padding
				};
			}
		};

		const start = getEdgePoint(source, true);
		const end = getEdgePoint(target, false);

		// Adjust end point to account for arrowhead
		const arrowAdjust = -65; // Increased for better visibility
		const adjustedEndX = end.x + nx * arrowAdjust;
		const adjustedEndY = end.y + ny * arrowAdjust;

		// Special handling for horizontal nodes (3 and 9 o'clock)
		if (source.highlighted && (target.position === 3 || target.position === 9)) {
			const curveRadius = 0;
			const extensionValue = 100; // Shorter horizontal extension for these positions

			// For 3 o'clock (right)
			if (target.position === 3) {
				return `M${start.x},${start.y}
      L${start.x + extensionValue},${start.y}
      C${start.x + extensionValue + curveRadius/2},${start.y}
       ${start.x + extensionValue + curveRadius},${start.y + curveRadius/2}
       ${start.x + extensionValue + curveRadius},${start.y + curveRadius}
      L${adjustedEndX},${adjustedEndY}`;
			}
			// For 9 o'clock (left)
			else if (target.position === 9) {
				return `M${start.x},${start.y}
      L${start.x - extensionValue},${start.y}
      C${start.x - extensionValue - curveRadius/2},${start.y}
       ${start.x - extensionValue - curveRadius},${start.y + curveRadius/2}
       ${start.x - extensionValue - curveRadius},${start.y + curveRadius}
      L${adjustedEndX},${adjustedEndY}`;
			}
		}

		// Check if target is at 2, 5, 8, or 10 o'clock position
		// These positions correspond to specific child nodes in setupNodePositions
		const isSpecialPosition = (): boolean => {
			if (!source.highlighted) return false;

			// Use position property to determine clock position
			return [1, 2, 4, 5, 7, 8, 10, 11].includes(target.position || 0);
		};

		if (isSpecialPosition()) {
			const arrowAdjust = -80; // Increased for better visibility
			const adjustedEndX = end.x + nx * arrowAdjust;
			const adjustedEndY = end.y + ny * arrowAdjust;
			// Define a smaller curve radius for tighter turns
			const curveRadius = 30;

			// Determine horizontal and vertical direction
			const isRightSide = adjustedEndX > start.x;

			// Adjust extension value based on position for better spacing
			let extensionValue = 131;

			// Adjust extension values based on specific clock positions
			if ([8, 10].includes(target.position || 0)) { // Left side positions (8, 10 o'clock)
				extensionValue = extensionValue*3;
			} else if ([2, 4].includes(target.position || 0)) {
				extensionValue = extensionValue*3;
			}

			const horizontalExtension = isRightSide ? extensionValue : -extensionValue;

			// Calculate the x position after the curve
			const xAfterCurve = start.x + horizontalExtension + (isRightSide ? curveRadius : -curveRadius);

			// Adjust vertical curve based on whether target is above or below
			const verticalCurveFactor = Math.sign(adjustedEndY - start.y) * (
				// Increase curve height for top/bottom positions
				[2, 10].includes(target.position || 0) ? curveRadius * 1.5 :
					[4, 8].includes(target.position || 0) ? curveRadius * 1.2 :
						curveRadius
			);

			// Create path: horizontal line, smoothed curve, then straight line to target
			return `M${start.x},${start.y}
      L${start.x + horizontalExtension},${start.y}
      C${start.x + horizontalExtension + (isRightSide ? curveRadius/2 : -curveRadius/2)},${start.y}
       ${xAfterCurve},${start.y + verticalCurveFactor/2}
       ${xAfterCurve},${start.y + verticalCurveFactor}
      L${adjustedEndX},${adjustedEndY}`;
		}
		// Default straight line for other positions
		return `M${start.x},${start.y} L${adjustedEndX},${adjustedEndY}`;
	}

	/**
	 * Creates an optimized SVG path between two nodes with proper bundling and curved edges
	 * @param _link
	 * @param source - The source node
	 * @param target - The target node
	 * @param nx - X component of the normalized direction vector (for arrow adjustment)
	 * @param ny - Y component of the normalized direction vector (for arrow adjustment)
	 * @returns SVG path data string
	 */
	createOptimizedLink(_link: Link, source: Node, target: Node, nx: number, ny: number): string {
		// Constants
		const MULTIPLIER = 2;
		const PADDING = 15;
		const CURVE_RADIUS = 30;
		const ARROW_ADJUST = 0;

		// Node dimensions
		const nodeWidth = this.cardWidth + (this.cardMargin * 2);
		const nodeHeight = this.cardHeight + (this.cardMargin * 2);

		// Get scaled positions
		const startX = (source.x || 0) * MULTIPLIER;
		const startY = (source.y || 0) * MULTIPLIER;
		const endX = (target.x || 0) * MULTIPLIER;
		const endY = (target.y || 0) * MULTIPLIER;

		/**
		 * Determine if this is a hierarchical relationship and its direction
		 * Returns: 'down', 'up', or null (not hierarchical)
		 */
		const determineHierarchyDirection = (): string | null => {
			// Use the expansionLayer property if available
			if (source.expansionLayer && target.expansionLayer) {
				const sourceLayer = source.expansionLayer.layer;
				const targetLayer = target.expansionLayer.layer;

				if (sourceLayer < targetLayer) return 'down';
				if (sourceLayer > targetLayer) return 'up';
			}

			// If no expansion layer, use Y-coordinate difference
			// Only consider it hierarchical if the Y difference is significant
			const yDiff = endY - startY;
			if (Math.abs(yDiff) > nodeHeight * 1.5) {
				return yDiff > 0 ? 'down' : 'up';
			}

			// Consider it hierarchical if source has parentId property equal to target id
			// or target has parentId property equal to source id
			if (target.id === source.id) return 'down';
			if (source.id === target.id) return 'up';

			// Not a clear hierarchical relationship
			return null;
		};

		/**
		 * Determine if nodes are siblings (same parent or same level)
		 */
		const areSiblings = (): boolean => {
			if (source.expansionLayer && target.expansionLayer) {
				// Same layer in the expansion system
				return source.expansionLayer.layer === target.expansionLayer.layer;
			}

			// Nodes with same parent ID
			if (source.id && target.id) {
				return source.id === target.id;
			}

			// Roughly the same Y position (for horizontal alignment)
			return Math.abs(startY - endY) < nodeHeight / 2;
		};

		/**
		 * Get the appropriate edge point based on hierarchy direction
		 */
		const getHierarchicalEdgePoint = (node: Node, isSource: boolean, hierarchyDirection: string | null, isSibling: boolean): { x: number, y: number } => {
			const nodeX = (node.x || 0) * MULTIPLIER;
			const nodeY = (node.y || 0) * MULTIPLIER;
			const threeOclockExitPoint = nodeX + (nodeWidth/2) + PADDING;
			const sidesYExitsPoints = nodeY + (nodeHeight/2) - PADDING - 3;
			const sidesXExitsPoints = nodeX - (nodeWidth/2) - PADDING;

			// For hierarchical relationships
			if (hierarchyDirection === 'down') {
				if (isSource) {
					// Exit from bottom of parent
					return {
						x: threeOclockExitPoint,
						y: sidesYExitsPoints
					};
				}
				else {
					// Enter from top of child
					return {
						x: sidesXExitsPoints,
						y: sidesYExitsPoints-20
					};
				}
			}
			else if (hierarchyDirection === 'up') {
				if (isSource) {
					// Exit from top of child
					return {
						x: sidesXExitsPoints,
						y: sidesYExitsPoints
					};
				} else {
					// Enter from bottom of parent
					return {
						x: threeOclockExitPoint,
						y: sidesYExitsPoints+20
					};
				}
			}
			else if (isSibling) {
				// For siblings, exit/enter from sides
				const leftToRight = nodeX < (isSource ? endX : startX);

				if (leftToRight) {
					// Exit/enter from right side
					return {
						x: nodeX + (nodeWidth / 2) + PADDING,
						y: nodeY
					};
				} else {
					// Exit/enter from left side
					return {
						x: nodeX - (nodeWidth / 2) - PADDING,
						y: nodeY
					};
				}
			}

			// Default to standard angle-based edge points for non-hierarchical relationships
			return getStandardEdgePoint(node, isSource ? endX : startX, isSource ? endY : startY);
		};

		/**
		 * Standard edge point calculation based on angle (for non-hierarchical links)
		 */
		const getStandardEdgePoint = (node: Node, otherNodeX: number, otherNodeY: number): { x: number, y: number } => {
			const nodeX = (node.x || 0) * MULTIPLIER;
			const nodeY = (node.y || 0) * MULTIPLIER;

			// Calculate direction and angle to other node
			const dirX = otherNodeX - nodeX;
			const dirY = otherNodeY - nodeY;
			const angle = Math.atan2(dirY, dirX);

			// Exit point from the node at 3 o'clock position
			const threeOclockExitPoint = nodeX + (nodeWidth/2) + PADDING;
			const sidesXExitsPoints = nodeX - (nodeWidth/2) - PADDING;

			// Determine exit point based on angle (8 directions)
			if (angle > -Math.PI/8 && angle < Math.PI/8) { // Right
				return {
					x: threeOclockExitPoint,
					y: nodeY
				};
			}
			else if (angle >= Math.PI/8 && angle < 3*Math.PI/8) { // Bottom-right
				return {
					x: nodeX + (nodeWidth/2) + PADDING,
					y: nodeY + (nodeHeight/2) - PADDING
				};
			}
			else if (angle >= 3*Math.PI/8 && angle < 5*Math.PI/8) { // Bottom
				return {
					x: nodeX,
					y: nodeY + (nodeHeight/2) + PADDING
				};
			}
			else if (angle >= 5*Math.PI/8 && angle < 7*Math.PI/8) { // Bottom-Left
				return {
					x: nodeX - (nodeWidth/2) - PADDING,
					y: nodeY + (nodeHeight/2) - PADDING
				};
			}
			else if (angle >= 7*Math.PI/8 || angle < -7*Math.PI/8) { // Left
				return {
					x: sidesXExitsPoints,
					y: nodeY
				};
			}
			else if (angle >= -7*Math.PI/8 && angle < -5*Math.PI/8) { // Top-Left
				return {
					x: nodeX - (nodeWidth/2) - PADDING,
					y: nodeY - (nodeHeight/2) + PADDING
				};
			}
			else if (angle >= -5*Math.PI/8 && angle < -3*Math.PI/8) { // Top
				return {
					x: nodeX,
					y: nodeY - (nodeHeight/2) - PADDING
				};
			}
			else { // Top-Right
				return {
					x: nodeX + (nodeWidth/2) + PADDING,
					y: nodeY - (nodeHeight/2) + PADDING
				};
			}
		};

		/**
		 * Creates a curved corner between two line segments
		 */
		const createCurvedCorner = (x1: number, y1: number, x2: number, y2: number, radius = CURVE_RADIUS, hemisphere?: string) => {
			const dx = x2 - x1;
			const dy = y2 - y1;

			// Adjust radius based on hemisphere if provided
			let adjustedRadius = radius*2;
			if (hemisphere) {
				adjustedRadius = hemisphere === 'northern' ? radius : -radius ;
			}

			// For a vertical to horizontal turn (or vice versa)
			if ((Math.abs(dx) < 1 && Math.abs(dy) > 1) || (Math.abs(dx) > 1 && Math.abs(dy) < 1)) {
				let cpx1, cpy1, cpx2, cpy2;

				if (Math.abs(dx) < 1) { // Vertical to horizontal
					const turnDirection = x2 > x1 ? 5 : -5;
					cpx1 = x1;
					cpy1 = y1 + (adjustedRadius * Math.sign(dy));
					cpx2 = x2 + (adjustedRadius * turnDirection);
					cpy2 = y2;
				}
				else { // Horizontal to vertical
					cpx1 = x1 //+ (adjustedRadius * Math.sign(dx));
					cpy1 = y1;
					cpx2 = x2;
					cpy2 = y2 //-(adjustedRadius * turnDirection);
				}

				// Apply different curve styles based on hemisphere
				if (hemisphere === 'northern') {
					// More rounded curves for northern hemisphere
					return {
						curve: `C${cpx1},${cpy1} ${cpx2},${cpy2} ${x2},${y2}`,
						controlPoints: [{ x: cpx1, y: cpy1 }, { x: cpx2, y: cpy2 }]
					};
				}
				else if (hemisphere === 'southern') {
					// More angular curves for southern hemisphere
					return {
						curve: `C${cpx1},${cpy1} ${cpx2},${cpy2} ${x2},${y2}`,
						controlPoints: [{ x: cpx1, y: cpy1 }, { x: cpx2, y: cpy2 }]
					};
				} else {
					// Default behavior when no hemisphere is specified
					return {
						curve: `C${cpx1},${cpy1} ${cpx2},${cpy2} ${x2},${y2}`,
						controlPoints: [{ x: cpx1, y: cpy1 }, { x: cpx2, y: cpy2 }]
					};
				}
			}

			// For other turns, create a simple curved segment with hemisphere-specific styling
			if (hemisphere === 'northern') {
				// More rounded curves for northern hemisphere
				return {
					curve: `S${x2},${y2} ${x2},${y2}`,
					controlPoints: []
				};
			}
			else if (hemisphere === 'southern') {
				// More angular curves for southern hemisphere
				return {
					curve: `Q${(x1 + x2) / 2},${(y1 + y2) / 2} ${x2},${y2}`,
					controlPoints: [{ x: (x1 + x2) / 2, y: (y1 + y2) / 2 }]
				};
			}
			else {
				// Default behavior when no hemisphere is specified
				return {
					curve: `S${x2},${y2} ${x2},${y2}`,
					controlPoints: []
				};
			}
		};

		// Determine hierarchy relationship
		const hierarchyDirection = determineHierarchyDirection();
		const isSibling = areSiblings();


		// Get edge points based on hierarchy
		const start = getHierarchicalEdgePoint(source, true, hierarchyDirection, isSibling);
		const end = getHierarchicalEdgePoint(target, false, hierarchyDirection, isSibling);

		// Arrow adjustment to prevent overlap with target node
		const adjustedEndX = end.x - nx * ARROW_ADJUST;
		const adjustedEndY = end.y - ny * ARROW_ADJUST;

		// Create the appropriate path based on hierarchy type
		if (hierarchyDirection === 'up') {
			return createHierarchicalPath(start, adjustedEndX-(this.cardWidth/1.6), adjustedEndY+(this.cardHeight/1.6), hierarchyDirection);
		}
		if (hierarchyDirection === 'down' ) {
			return createHierarchicalPath(start, adjustedEndX+(this.cardWidth/1.6), adjustedEndY-(this.cardHeight/1.6), hierarchyDirection);
		}
	 else {
			// For non-hierarchical links
			return createStandardPath(start, startX, startY, adjustedEndX, adjustedEndY);
		}

		/**
		 * Creates a path optimized for hierarchical parent-child relationships
		 */
		function createHierarchicalPath(start: { x: number, y: number }, endX: number, endY: number, direction: string): string {

			// Calculate trunk positions
			let trunkY: number;

			if (direction === 'down') {
				// For downward hierarchy, the trunk is between the nodes
				trunkY = startY + (endY - startY)/1.3;
			}
			else {
				// For upward hierarchy, the trunk is also between the nodes
				trunkY = endY + (startY - endY) / 2.3;
			}

			// This is where we could add logic to offset the x position
			// based on the number of children/parents if needed

			// Generate path segments
			const path = [];

			// Start point
			path.push(`M${start.x},${start.y}`);


			// First vertical segment to trunk level
			path.push(`L${start.x},${trunkY}`);

			// Turn from vertical to horizontal
			const turn1 = createCurvedCorner(start.x, trunkY, endX, trunkY, CURVE_RADIUS);
			path.push(turn1.curve);

			// Final vertical segment to end point
			path.push(`L${endX},${endY}`);

			return path.join(' ');
		}


		/**
		 * Creates a standard path for non-hierarchical relationships
		 */
		function createStandardPath(start: { x: number, y: number }, _startX: number, _startY: number, endX: number, endY: number): string {
			const dx = endX - start.x;
			const dy = endY - start.y;
			const startHorizontal = Math.abs(dx) > Math.abs(dy);

			if (startHorizontal) {
				// Horizontal then vertical
				const path1 = `M${start.x},${start.y} L${endX},${start.y}`;
				const turn1 = createCurvedCorner(endX, start.y, endX, endY, CURVE_RADIUS);
				return `${path1} ${turn1.curve}`;
			} else {
				// Vertical then horizontal
				const path1 = `M${start.x},${start.y} L${start.x},${endY}`;
				const turn1 = createCurvedCorner(start.x, endY, endX, endY, CURVE_RADIUS);
				return `${path1} ${turn1.curve}`;
			}
		}
	}

	createOptimizedReversedLink(_link: Link, source: Node, target: Node, nx: number, ny: number): string {
		// Constants
		const MULTIPLIER = 2;
		const PADDING = 15;
		const CURVE_RADIUS = 30;
		const ARROW_ADJUST = 0;


		// Node dimensions
		const nodeWidth = this.cardWidth + (this.cardMargin * 2);
		const nodeHeight = this.cardHeight + (this.cardMargin * 2);

		// For reversed links, we swap the visual source and target
		// We're drawing from target to source, but keeping original data intact
		const visualSource = source ;  // The target becomes the visual source
		const visualTarget = target;  // The source becomes the visual target

		// Get scaled positions (reversed)
		const startX = (visualSource.x || 0) * MULTIPLIER;
		const startY = (visualSource.y || 0) * MULTIPLIER;
		const endX = (visualTarget.x || 0) * MULTIPLIER;
		const endY = (visualTarget.y || 0) * MULTIPLIER;

		// Reverse direction vectors for calculations
		const reversedNx = -nx;
		const reversedNy = -ny;

		/**
		 * Determine if this is a hierarchical relationship and its direction
		 * For reversed links, we invert the hierarchy direction
		 */
		const determineHierarchyDirection = (): string | null => {
			// Use the expansionLayer property if available
			if (visualSource.expansionLayer && visualTarget.expansionLayer) {
				const sourceLayer = visualSource.expansionLayer.layer;
				const targetLayer = visualTarget.expansionLayer.layer;

				// Note: We invert the direction for reversed links
				if (sourceLayer < targetLayer) return 'up';   // Inverted from 'down'
				if (sourceLayer > targetLayer) return 'down'; // Inverted from 'up'
			}

			// If no expansion layer, use Y-coordinate difference
			// Only consider it hierarchical if the Y difference is significant
			const yDiff = endY - startY;
			if (Math.abs(yDiff) > nodeHeight * 1.5) {
				return yDiff > 0 ? 'up' : 'down'; // Inverted directions
			}

			// Consider it hierarchical if visualSource (target) has parentId property equal to visualTarget (source) id
			// or visualTarget (source) has parentId property equal to visualSource (target) id
			// Note: Logic inverted for reversed links
			if (visualTarget.id === visualSource.id) return 'up';   // Inverted from 'down'
			if (visualSource.id === visualTarget.id) return 'down'; // Inverted from 'up'

			// Not a clear hierarchical relationship
			return null;
		};

		/**
		 * Determine if nodes are siblings (same parent or same level)
		 * This doesn't change for reversed links
		 */
		const areSiblings = (): boolean => {
			if (visualSource.expansionLayer && visualTarget.expansionLayer) {
				// Same layer in the expansion system
				return visualSource.expansionLayer.layer === visualTarget.expansionLayer.layer;
			}

			// Nodes with same parent ID
			if (visualSource.id && visualTarget.id) {
				return visualSource.id === visualTarget.id;
			}

			// Roughly the same Y position (for horizontal alignment)
			return Math.abs(startY - endY) < nodeHeight / 2;
		};

		/**
		 * Get the appropriate edge point based on hierarchy direction
		 * For reversed links, we swap isSource parameter logic
		 */
		const getHierarchicalEdgePoint = (node: Node, isVisualSource: boolean, hierarchyDirection: string | null, isSibling: boolean): { x: number, y: number } => {
			const nodeX = (node.x || 0) * MULTIPLIER;
			const nodeY = (node.y || 0) * MULTIPLIER;
			const threeOclockExitPoint = nodeX + (nodeWidth/2) - (PADDING+this.cardWidth/2);
			const sidesYExitsPoints = nodeY + (nodeHeight/2) - PADDING + (this.cardHeight/2);
			const sidesXExitsPoints = nodeX - (nodeWidth/2) - PADDING + (this.cardWidth/1.5);
			const entrySidesXExitsPoints = nodeX + nodeWidth - nodeWidth - PADDING - (this.cardWidth/1.87);
			const bottomYExitPoint = sidesYExitsPoints-(this.cardHeight*2.5);

			// For hierarchical relationships - reversed link logic
			if (hierarchyDirection === 'down') {
				if (isVisualSource) {
					// Exit from top of child (was bottom of parent)
					return {
						x: sidesXExitsPoints,
						y: bottomYExitPoint
					};
				}
				else {
					// Enter from bottom of parent (was top of child)
					return {
						x: entrySidesXExitsPoints,
						y: nodeY - nodeHeight + PADDING*5.6
					};
				}
			}
			else if (hierarchyDirection === 'up') {
				if (isVisualSource) {
					// Exit from bottom of parent (was top of child)
					return {
						x: threeOclockExitPoint,
						y: sidesYExitsPoints + (this.cardHeight)
					};
				} else {
					// Enter from top of child (was bottom of parent)
					return {
						x: nodeX + (nodeWidth) + (PADDING) - (nodeWidth/PADDING)*2.63,
						y: nodeY - nodeHeight + PADDING/10
					};
				}
			}
			else if (isSibling) {
				// For siblings, exit/enter from sides
				const leftToRight = nodeX < (isVisualSource ? endX : startX);

				if (leftToRight) {
					// Exit/enter from right side
					return {
						x: nodeX + (nodeWidth / 2) + PADDING,
						y: nodeY
					};
				} else {
					// Exit/enter from left side
					return {
						x: nodeX - (nodeWidth / 2) - PADDING,
						y: nodeY
					};
				}
			}

			// Default to standard angle-based edge points for non-hierarchical relationships
			return getStandardEdgePoint(node, isVisualSource ? endX : startX, isVisualSource ? endY : startY);
		};

		/**
		 * Standard edge point calculation based on angle (for non-hierarchical links)
		 * This function doesn't change its behavior for reversed links
		 */
		const getStandardEdgePoint = (node: Node, otherNodeX: number, otherNodeY: number): { x: number, y: number } => {
			const nodeX = (node.x || 0) * MULTIPLIER;
			const nodeY = (node.y || 0) * MULTIPLIER;
			// Calculate direction and angle to other node
			const dirX = otherNodeX - nodeX;
			const dirY = otherNodeY - nodeY;
			const angle = Math.atan2(dirY, dirX);

			// Exit point from the node at 3 o'clock position
			const threeOclockExitPoint = nodeX + (nodeWidth/2) + PADDING;
			const sidesXExitsPoints = nodeX - (nodeWidth/2) - PADDING;

			// Determine exit point based on angle (8 directions)
			if (angle > -Math.PI/8 && angle < Math.PI/8) { // Right
				return {
					x: threeOclockExitPoint,
					y: nodeY
				};
			}
			else if (angle >= Math.PI/8 && angle < 3*Math.PI/8) { // Bottom-right
				return {
					x: nodeX + (nodeWidth/4) + PADDING*10,
					y: nodeY + (nodeHeight/2) - PADDING
				};
			}
			else if (angle >= 3*Math.PI/8 && angle < 5*Math.PI/8) { // Bottom
				return {
					x: nodeX,
					y: nodeY + (nodeHeight/2) + PADDING
				};
			}
			else if (angle >= 5*Math.PI/8 && angle < 7*Math.PI/8) { // Bottom-Left
				return {
					x: nodeX - (nodeWidth/2) - PADDING,
					y: nodeY + (nodeHeight/2) - PADDING
				};
			}
			else if (angle >= 7*Math.PI/8 || angle < -7*Math.PI/8) { // Left
				return {
					x: sidesXExitsPoints,
					y: nodeY
				};
			}
			else if (angle >= -7*Math.PI/8 && angle < -5*Math.PI/8) { // Top-Left
				return {
					x: nodeX - (nodeWidth/2) - PADDING,
					y: nodeY - (nodeHeight/2) + PADDING
				};
			}
			else if (angle >= -5*Math.PI/8 && angle < -3*Math.PI/8) { // Top
				return {
					x: nodeX,
					y: nodeY - (nodeHeight/2) - PADDING
				};
			}
			else { // Top-Right
				return {
					x: nodeX + (nodeWidth/2) + PADDING,
					y: nodeY - (nodeHeight/2) + PADDING
				};
			}
		};

		/**
		 * Creates a curved corner between two line segments
		 * This function doesn't need changes for reversed links
		 */
		const createCurvedCorner = (x1: number, y1: number, x2: number, y2: number, radius = CURVE_RADIUS, hemisphere?: string) => {
			const dx = x2 - x1;
			const dy = y2 - y1;

			// Adjust radius based on hemisphere if provided
			let adjustedRadius = radius*2;
			if (hemisphere) {
				adjustedRadius = hemisphere === 'northern' ? radius : -radius;
			}

			// For a vertical to horizontal turn (or vice versa)
			if ((Math.abs(dx) < 1 && Math.abs(dy) > 1) || (Math.abs(dx) > 1 && Math.abs(dy) < 1)) {
				let cpx1, cpy1, cpx2, cpy2;

				if (Math.abs(dx) < 1) { // Vertical to horizontal
					const turnDirection = x2 > x1 ? 1 : -1
					cpx1 = x1;
					cpy1 = y1 + (adjustedRadius * Math.sign(dy));
					cpx2 = x2 + (adjustedRadius * turnDirection);
					cpy2 = y2;
				}
				else { // Horizontal to vertical
					cpx1 = x1;
					cpy1 = y1;
					cpx2 = x2;
					cpy2 = y2;
				}

				// Apply different curve styles based on hemisphere
				if (hemisphere === 'northern') {
					// More rounded curves for northern hemisphere
					return {
						curve: `C${cpx1},${cpy1} ${cpx2},${cpy2} ${x2},${y2}`,
						controlPoints: [{ x: cpx1, y: cpy1 }, { x: cpx2, y: cpy2 }]
					};
				}
				else if (hemisphere === 'southern') {
					// More angular curves for southern hemisphere
					return {
						curve: `C${cpx1},${cpy1} ${cpx2},${cpy2} ${x2},${y2}`,
						controlPoints: [{ x: cpx1, y: cpy1 }, { x: cpx2, y: cpy2 }]
					};
				} else {
					// Default behavior when no hemisphere is specified
					return {
						curve: `C${cpx1},${cpy1} ${cpx2},${cpy2} ${x2},${y2}`,
						controlPoints: [{ x: cpx1, y: cpy1 }, { x: cpx2, y: cpy2 }]
					};
				}
			}

			// For other turns, create a simple curved segment with hemisphere-specific styling
			if (hemisphere === 'northern') {
				// More rounded curves for northern hemisphere
				return {
					curve: `S${x2},${y2} ${x2},${y2}`,
					controlPoints: []
				};
			}
			else if (hemisphere === 'southern') {
				// More angular curves for southern hemisphere
				return {
					curve: `Q${(x1 + x2) / 2},${(y1 + y2) / 2} ${x2},${y2}`,
					controlPoints: [{ x: (x1 + x2) / 2, y: (y1 + y2) / 2 }]
				};
			}
			else {
				// Default behavior when no hemisphere is specified
				return {
					curve: `S${x2},${y2} ${x2},${y2}`,
					controlPoints: []
				};
			}
		};

		// Determine hierarchy relationship for reversed link
		const hierarchyDirection = determineHierarchyDirection();
		const isSibling = areSiblings();

		// Get edge points based on hierarchy (for reversed link)
		const start = getHierarchicalEdgePoint(visualSource, true, hierarchyDirection, isSibling);
		const end = getHierarchicalEdgePoint(visualTarget, false, hierarchyDirection, isSibling);

		// Arrow adjustment to prevent overlap with target node
		// Note: Using reversedNx and reversedNy for reversed direction
		const adjustedEndX = end.x - reversedNx * ARROW_ADJUST;
		const adjustedEndY = end.y - reversedNy * ARROW_ADJUST;

		// Create the appropriate path based on hierarchy type
		if (hierarchyDirection === 'up') {
			return createHierarchicalPath(start, adjustedEndX-(this.cardWidth), adjustedEndY, hierarchyDirection);
		}
		if (hierarchyDirection === 'down') {
			return createHierarchicalPath(start, adjustedEndX+(this.cardWidth/1.6), adjustedEndY-(this.cardHeight/1.6), hierarchyDirection);
		}
		else {
			// For non-hierarchical links
			return createStandardPath(start, startX, startY, adjustedEndX, adjustedEndY);
		}

		/**
		 * Creates a path optimized for hierarchical parent-child relationships
		 */
		function createHierarchicalPath(start: { x: number, y: number }, endX: number, endY: number, direction: string): string {
			// Calculate trunk positions
			let trunkY: number;

			if (direction === 'down') {
				// For downward hierarchy, the trunk is between the nodes
				// Use a fixed value instead of this.cardHeight to avoid 'this' context issues
				trunkY = startY - (endY + startY)/(30/1.2);
			}
			else {
				// For upward hierarchy, the trunk is also between the nodes
				trunkY = endY + (startY - endY) / 1.5;
			}

			// Generate path segments
			const path = [];

			// Start point
			path.push(`M${start.x},${start.y}`);

			// First vertical segment to trunk level
			path.push(`L${start.x},${trunkY}`);

			// Turn from vertical to horizontal
			const turn1 = createCurvedCorner(start.x, trunkY, endX, trunkY, CURVE_RADIUS);
			path.push(turn1.curve);

			// Final vertical segment to end point
			path.push(`L${endX},${endY}`);

			return path.join(' ');
		}

		/**
		 * Creates a standard path for non-hierarchical relationships
		 */
		function createStandardPath(start: { x: number, y: number }, _startX: number, _startY: number, endX: number, endY: number): string {
			const dx = endX - start.x;
			const dy = endY - start.y;
			const startHorizontal = Math.abs(dx) > Math.abs(dy);

			if (startHorizontal) {
				// Horizontal then vertical
				const path1 = `M${start.x},${start.y} L${endX},${start.y}`;
				const turn1 = createCurvedCorner(endX, start.y, endX, endY, CURVE_RADIUS);
				return `${path1} ${turn1.curve}`;
			} else {
				// Vertical then horizontal
				const path1 = `M${start.x},${start.y} L${start.x},${endY}`;
				const turn1 = createCurvedCorner(start.x, endY, endX, endY, CURVE_RADIUS);
				return `${path1} ${turn1.curve}`;
			}
		}
	}

	/**
	 * Creates a reversed link path between nodes
	 * @param d - The link data
	 * @returns SVG path string for the reversed link
	 */
	createReversedSimpleLink(d: Link): string {
		// Get the actual node objects - keep original source and target
		// @ts-ignore
		const source =  d.source  // @ts-ignore
		const target =  d.target  // @ts-ignore

		if (!source || !target) return 'M0,0';

		// Calculate direction vector - SAME as original, not reversed
		// @ts-ignore
		const dx = (target.x || 0) - (source.x || 0); // @ts-ignore
		const dy = (target.y || 0) - (source.y || 0);
		const length = Math.sqrt(dx * dx + dy * dy);


		if (length === 0) return 'M0,0';

		// Normalized direction vector
		const nx = dx / length;
		const ny = dy / length;

		// Check if source or target is at a critical clock position (1-12 o'clock)
		const isCriticalNode = (node: Node): boolean => {
			return [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].includes(node.position || 0) ||
				node.highlighted;
		};

		// If either source  is a critical node, use the original algorithm
		// @ts-ignore
		if (isCriticalNode(source)) {
			// @ts-ignore
			return this.createOriginalReversedLink(d, source, target, nx, ny);
		}

		// For links between non-critical nodes, use the optimized path algorithm
		// @ts-ignore
		return this.createOptimizedReversedLink(d, source, target, nx, ny);
	}

	/**
	 * Preserves the original reversed link algorithm for critical clock position nodes
	 * @param _d
	 * @param source - The source node
	 * @param target - The target node
	 * @param nx - Normalized x direction
	 * @param ny - Normalized y direction
	 * @returns SVG path string for the reversed link
	 */
	createOriginalReversedLink(_d: Link, source: Node, target: Node, nx: number, ny: number): string {
		// Node dimensions
		const multiplier = 2;
		const nodeWidth = this.cardWidth + (this.cardMargin * 2);
		const nodeHeight = this.cardHeight + (this.cardMargin * 2);
		const padding = 15; // Space between node edge and link
		const dx = (target.x || 0) - (source.x || 0);
		const dy = (target.y || 0) - (source.y || 0);

		// Calculate exit points from nodes - SAME as original
		// @ts-ignore
		const getEdgePoint = (node: Node, isSource: boolean): { x: number, y: number } => {
			const nodeX = node.x || 0;
			const nodeY = node.y || 0;

			// Special handling for 3 and 9 o'clock positions
			if (node.position === 3) { // 3 o'clock position
				return {
					x: (nodeX * multiplier) - nodeWidth/2 - padding,
					y: nodeY * multiplier
				};
			}
			else if (node.position === 9) { // 9 o'clock position
				return {
					x: (nodeX * multiplier) + nodeWidth/2 + padding,
					y: nodeY * multiplier
				};
			}

			let xValue: number, yValue: number;
			if (node.highlighted && [1, 2, 4, 5, 7, 8, 10, 11].includes(source.position || 0)) {
				// Determine if the target is on the left or right side
				const isRightSide = dx > 0;
				const isTopSide = dy < 0;

				if (isRightSide) {
					xValue = nodeX * multiplier - (nodeWidth/2 + padding);
				} else {
					xValue = nodeX * multiplier + (nodeWidth/2 + padding);
				}

				if (isTopSide) {
					yValue = nodeY * multiplier + (nodeHeight/2 + padding);
				} else {
					yValue = nodeY * multiplier - (nodeHeight/2 + padding);
				}

				// Return the calculated position for source node
				return {
					x: xValue,
					y: yValue
				};
			}

			// For non-root nodes, use the original position-based exit points
			switch(node.position) {
				case 1: // 1 o'clock
					return {
						x: (nodeX * multiplier) - nodeWidth/4 + nodeWidth/4,
						y: (nodeY * multiplier) + nodeHeight
					};
				case 2: // 2 o'clock
					return {
						x: (nodeX * multiplier) - nodeWidth/4 + nodeWidth/4,
						y: (nodeY * multiplier) + nodeHeight
					};
				case 4: // 4 o'clock
					return {
						x: (nodeX * multiplier) - nodeWidth/4 + nodeWidth/4,
						y: (nodeY * multiplier) - nodeHeight
					};
				case 5: // 5 o'clock
					return {
						x: (nodeX * multiplier) - nodeWidth/4 + nodeWidth/4,
						y: (nodeY * multiplier) - nodeHeight
					};
				case 7: // 7 o'clock
					return {
						x: (nodeX * multiplier) - nodeWidth/4 + nodeWidth/4,
						y: (nodeY * multiplier) - nodeHeight
					};
				case 8: // 8 o'clock
					return {
						x: (nodeX * multiplier) - nodeWidth/4 + nodeWidth/4,
						y: (nodeY * multiplier) - nodeHeight
					};
				case 10: // 10 o'clock
					return {
						x: (nodeX * multiplier) + nodeWidth/4 - nodeWidth/4,
						y: (nodeY * multiplier) + nodeHeight
					};
				case 11: // 11 o'clock
					return {
						x: (nodeX * multiplier) + nodeWidth/4 - nodeWidth/4,
						y: (nodeY * multiplier) + nodeHeight
					};
				case 6: // 6 o'clock (bottom)
					return {
						x: nodeX * multiplier,
						y: (nodeY * multiplier) - nodeHeight/2 - padding
					};
				case 12: // 12 o'clock (top)
					return {
						x: nodeX * multiplier,
						y: (nodeY * multiplier) + nodeHeight/2 + padding
					};
				default:
					// Fall back to angle-based logic for any other positions
					const angle = Math.atan2(dy, dx) * (180 / Math.PI);
					if (angle > -45 && angle < 45) { // Right side
						return {
							x: (nodeX * multiplier) + nodeWidth/2 + padding,
							y: nodeY * multiplier
						};
					}
					else if (angle > 45 && angle < 135) { // Bottom side
						return {
							x: nodeX * multiplier,
							y: (nodeY * multiplier) + nodeHeight/2 + padding
						};
					}
					else if (angle > 135 || angle < -135) { // Left side
						return {
							x: (nodeX * multiplier) - nodeWidth/2 - padding,
							y: nodeY * multiplier
						};
					}
					else { // Top side
						return {
							x: nodeX * multiplier,
							y: (nodeY * multiplier) - nodeHeight/2 - padding
						};
					}
			}
		};

		const originalStart = getEdgePoint(target, true);
		const originalEnd = getEdgePoint(source, false);

		// Reverse the start and end points
		const start = originalEnd;  // Target becomes start
		const end = originalStart;  // Source becomes end

		const reversedNx = -nx;
		const reversedNy = -ny;

		const arrowAdjust = 70;
		const adjustedEndX = end.x + reversedNx * arrowAdjust;
		const adjustedEndY = end.y + reversedNy * arrowAdjust;

		// Special handling for horizontal nodes (3 and 9 o'clock)
		if ([3, 9].includes(source.position || 0)) {
			const curveRadius = 0;
			const add = 16;
			let entryPointX: number, entryPointY: number;

			// Specific handling for 3 o'clock (right side) nodes
			if (source.position === 3) {
				entryPointX = end.x + (nodeWidth + padding + add);
				entryPointY = end.y;

				// Create a curved path coming from the left side of the node
				return `M${start.x},${start.y}
          L${start.x - 60},${start.y}
          C${start.x - 60 - curveRadius/2},${start.y}
            ${start.x - 60 - curveRadius},${entryPointY}
            ${entryPointX},${entryPointY}`;
			}

			// Specific handling for 9 o'clock (left side) nodes
			if (source.position === 9) {
				entryPointX = end.x - (nodeWidth + padding + add);
				entryPointY = end.y;

				// Create a curved path coming from the right side of the node
				return `M${start.x},${start.y}
          L${start.x + 60},${start.y}
          C${start.x + 60 + curveRadius/2},${start.y}
            ${start.x + 60 + curveRadius},${entryPointY}
            ${entryPointX},${entryPointY}`;
			}
		}

		const isSpecialPosition = (): boolean => {
			return [1, 2, 4, 5, 7, 8, 10, 11].includes(source.position || 0);
		};

		if (isSpecialPosition()) {
			const arrowAdjust = 8;

			let entryPointX: number, entryPointY: number;
			let eastHemisphereXLink = (end.x - nodeWidth) + (nodeWidth + padding) - nodeWidth/8;
			let rightHemisphereXLink = (end.x + nodeWidth) - (nodeWidth + padding) + nodeWidth/8;
			let eastHemisphereYLink = end.y + nodeHeight;

			// Group positions by which side of the node they're on
			if ([1, 2].includes(source.position || 0)) {
				entryPointX = rightHemisphereXLink;
				entryPointY = (end.y + nodeHeight/2) + (nodeHeight/padding) + padding;
			}
			else if ([4, 5].includes(source.position || 0)) {
				entryPointX = rightHemisphereXLink;
				entryPointY = (end.y - nodeHeight/2) - (nodeHeight/padding) - padding;
			}
			else if ([10, 11].includes(source.position || 0)) {
				entryPointX = eastHemisphereXLink;
				entryPointY = eastHemisphereYLink;
			}
			else if ([7, 8].includes(source.position || 0)) {
				entryPointX = eastHemisphereXLink;
				entryPointY = (end.y - nodeHeight/2) - (nodeHeight/padding) - padding;
			}
			else if ([6].includes(source.position || 0)) {
				entryPointX = end.x;
				entryPointY = end.y + (nodeHeight/2 + padding);
			}
			else {
				// Links from top (11, 12, 1 o'clock) enter at a single point on the bottom
				entryPointX = end.x;
				entryPointY = end.y - (nodeHeight/2 + padding);
			}

			const adjustedEndX = entryPointX - (reversedNx * arrowAdjust);
			const adjustedEndY = entryPointY + (reversedNy * arrowAdjust);

			// Radius for the curved corner
			const curveRadius = 30;

			// Create the path with a curved elbow
			return `M${start.x},${start.y}
					L${start.x},${adjustedEndY - Math.sign(adjustedEndY - start.y) * curveRadius}
					C${start.x},${adjustedEndY - Math.sign(adjustedEndY - start.y) * curveRadius/2}
					 ${start.x + Math.sign(adjustedEndX - start.x) * curveRadius/2},${adjustedEndY}
					 ${start.x + Math.sign(adjustedEndX - start.x) * curveRadius},${adjustedEndY}
					L${adjustedEndX},${adjustedEndY}`;
		}

		return `M${start.x},${start.y} L${adjustedEndX},${adjustedEndY}`;
	}

}
(window as any).DataSourceVisulizationNetwork = DataSourceVisulizationNetwork;
export { DataSourceVisulizationNetwork, initializeVisualization };
