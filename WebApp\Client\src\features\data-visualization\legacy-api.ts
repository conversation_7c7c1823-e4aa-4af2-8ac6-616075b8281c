/**
 * Legacy API Facade for D3 Visualization Service
 * Provides backward compatibility while using the new modular implementation
 */

import { DataSourceVisualizationNetwork, VisualizationOptions } from './data-source-visualization-network';
import { TypeGuards } from './models/network-types';
import { StringLocalizer } from '@/shared/string-localizer';

// Initialize localizer for compatibility
const _localizer: StringLocalizer = new StringLocalizer('DataSourceVisulizationNetwork');

/**
 * Legacy API facade that wraps the new modular implementation
 */
export class VisualizationLegacyAPI {
  private static currentInstance: DataSourceVisualizationNetwork | null = null;

  /**
   * Initialize visualization (legacy function)
   * Maintains exact same API as the original function
   */
  static async initializeVisualization(id: string, key: string): Promise<void> {
    if (!id) {
      console.error('ID is required for visualization initialization');
      return;
    }

    // Show loading overlay if available
    if (typeof window !== 'undefined' && (window as any).Overlay?.showWait) {
      (window as any).Overlay.showWait(_localizer.localize("Loading"));
    }

    const container = document.getElementById('visualizationChartContainer');
    if (!container) {
      console.error('Visualization container not found');
      return;
    }

    // Store key globally for compatibility
    if (typeof window !== 'undefined') {
      (window as any).key = key;
    }

    const loadingElement = container.querySelector('.loading');
    loadingElement?.classList.remove('hide');

    try {
      // Fetch data from API
      const response = await fetch(`/api/${key}/${id}/DataNetwork`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // Validate data
      if (!TypeGuards.isDataNetwork(data)) {
        throw new Error('Invalid data received from API');
      }

      // Hide loading overlay
      if (typeof window !== 'undefined' && (window as any).Overlay?.hideWait) {
        await (window as any).Overlay.hideWait();
      }

      // Destroy existing visualization if present
      if (this.currentInstance) {
        this.currentInstance.destroy();
      }

      // Create new visualization with legacy-compatible options
      const options: VisualizationOptions = {
        config: {
          dataSourceType: key as 'pages' | 'dependantSources'
        },
        enableDebugMode: false, // Can be enabled via config
        performanceMode: 'auto'
      };

      this.currentInstance = new DataSourceVisualizationNetwork(
        'visualizationChartContainer', 
        data, 
        options
      );

      // Store global references for legacy compatibility
      if (typeof window !== 'undefined') {
        (window as any).DataSourceVisulizationNetwork = DataSourceVisualizationNetwork;
        (window as any).currentVisualizationInstance = this.currentInstance;
      }

      // Hide loading element
      loadingElement?.classList.add('hide');

    } catch (error) {
      console.error('Error initializing visualization:', error);

      // Hide loading overlay and element
      if (typeof window !== 'undefined' && (window as any).Overlay?.hideWait) {
        await (window as any).Overlay.hideWait();
      }

      loadingElement?.classList.add('hide');

      // Show error message
      if (container) {
        container.innerHTML = `
          <div class="error-message" style="
            display: flex; 
            align-items: center; 
            justify-content: center; 
            height: 100%; 
            color: var(--clr-red-600);
            font-family: var(--font-family);
          ">
            <div style="text-align: center;">
              <h3>Visualization Error</h3>
              <p>Failed to load visualization data. Please try again.</p>
            </div>
          </div>
        `;
      }

      throw error;
    }
  }

  /**
   * Refresh filter panel (legacy function)
   * Maintains exact same API as the original function
   */
  static async refreshFilterPanel(urlKey: string = "DataVisualization"): Promise<void> {
    const filterPanel = document.getElementById('data-visualization-filter') as HTMLElement & {
      url?: string;
      reload?: () => void;
    };

    if (!filterPanel) return;

    try {
      // Show loading state
      filterPanel.setAttribute('skeleton', 'true');

      // Get current data source ID
      const dataSourceId = (typeof window !== 'undefined' && (window as any).visualizationMainDatasource)
        ? (window as any).visualizationMainDatasource.id
        : null;

      if (!dataSourceId) {
        console.warn('No main data source found for filter refresh');
        return;
      }

      // Create base filters
      const baseFilters = [{
        FilterColumn: 'filterByDataSourceId',
        Operator: 'Equals',
        CompareValue: dataSourceId
      }];

      // Make API call to get updated data
      const response = await fetch(`/Api/${urlKey}/?filters=${encodeURIComponent(JSON.stringify(baseFilters))}`);
      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`);
      }

      const data = await response.json();

      // Update the filter panel with new data
      if (data?.rows || data?.data?.rows) {
        // Update filter panel's internal data
        filterPanel.url = `/Api/${urlKey}/?filters=${encodeURIComponent(JSON.stringify(baseFilters))}`;

        // If the component has a reload method, use it
        if (typeof filterPanel.reload === 'function') {
          filterPanel.reload();
        } else {
          // Force refresh by dispatching a custom event
          filterPanel.dispatchEvent(new CustomEvent('refresh-data', {
            detail: { data: data.rows || data.data.rows }
          }));
        }
      }
    } catch (error) {
      console.error('Error refreshing filter panel:', error);
    } finally {
      // Remove loading state
      filterPanel.removeAttribute('skeleton');
    }
  }

  /**
   * Hide visualization filter (legacy function)
   */
  static hideVisualizationFilter(): void {
    const filterPanel = document.getElementById('data-visualization-filter');
    if (filterPanel) {
      filterPanel.style.display = 'none';
    }
  }

  /**
   * Get current visualization instance
   */
  static getCurrentInstance(): DataSourceVisualizationNetwork | null {
    return this.currentInstance;
  }

  /**
   * Destroy current visualization instance
   */
  static destroyCurrentInstance(): void {
    if (this.currentInstance) {
      this.currentInstance.destroy();
      this.currentInstance = null;
    }
  }
}

/**
 * Export legacy functions to global scope for backward compatibility
 */
if (typeof window !== 'undefined') {
  // Legacy global functions
  (window as any).initializeVisualization = VisualizationLegacyAPI.initializeVisualization;
  (window as any).refreshFilterPanel = VisualizationLegacyAPI.refreshFilterPanel;
  (window as any).hideVisualizationFilter = VisualizationLegacyAPI.hideVisualizationFilter;

  // Store the class for direct access if needed
  (window as any).VisualizationLegacyAPI = VisualizationLegacyAPI;
}

/**
 * Named exports for ES6 modules
 */
export {
  VisualizationLegacyAPI as default,
  VisualizationLegacyAPI
};

// Re-export the main class for convenience
export { DataSourceVisualizationNetwork } from './data-source-visualization-network';

/**
 * Legacy function exports for direct import
 */
export const initializeVisualization = VisualizationLegacyAPI.initializeVisualization;
export const refreshFilterPanel = VisualizationLegacyAPI.refreshFilterPanel;
export const hideVisualizationFilter = VisualizationLegacyAPI.hideVisualizationFilter;
