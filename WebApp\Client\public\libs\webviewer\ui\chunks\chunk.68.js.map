{"version": 3, "sources": ["webpack:///./src/ui/src/components/LoadingModal/LoadingModal.scss?83ea", "webpack:///./src/ui/src/components/LoadingModal/LoadingModal.scss", "webpack:///./src/ui/src/components/LoadingModal/LoadingModal.js", "webpack:///./src/ui/src/components/LoadingModal/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "LoadingModal", "isOpen", "useSelector", "state", "selectors", "isElementOpen", "DataElements", "LOADING_MODAL", "shallowEqual", "isDisabled", "isElementDisabled", "dispatch", "useDispatch", "useEffect", "actions", "closeElements", "SIGNATURE_MODAL", "PRINT_MODAL", "ERROR_MODAL", "className", "classNames", "data-element"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,01IAA21I,KAGp3I0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,0GCmCRC,G,QApCM,WACnB,IAAMC,EAASC,aAAY,SAACC,GAAK,OAAKC,IAAUC,cAAcF,EAAOG,IAAaC,iBAAgBC,KAC5FC,EAAaP,aAAY,SAACC,GAAK,OAAKC,IAAUM,kBAAkBP,EAAOG,IAAaC,iBAAgBC,KAEpGG,EAAWC,cAYjB,OAVAC,qBAAU,WACJZ,GACFU,EAASG,IAAQC,cAAc,CAC7BT,IAAaU,gBACbV,IAAaW,YACbX,IAAaY,iBAGhB,CAACjB,IAEAQ,EACK,KAIP,yBACEU,UAAWC,IAAW,CACpB,OAAS,EACT,cAAgB,EAChB,KAAQnB,IAEVoB,eAAcf,IAAaC,eAE3B,yBAAKY,UAAU,aACb,yBAAKA,UAAU,sBCrCRnB", "file": "chunks/chunk.68.js", "sourcesContent": ["var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./LoadingModal.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".open.LoadingModal{visibility:visible}.closed.LoadingModal{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.LoadingModal .footer .modal-button.confirm:hover{background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.LoadingModal .footer .modal-button.confirm{background:var(--primary-button);border-color:var(--primary-button);color:var(--primary-button-text)}.LoadingModal .footer .modal-button.confirm.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.LoadingModal .footer .modal-button.confirm.disabled span{color:var(--primary-button-text)}.LoadingModal .footer .modal-button.cancel:hover,.LoadingModal .footer .modal-button.secondary-btn-custom:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.LoadingModal .footer .modal-button.cancel,.LoadingModal .footer .modal-button.secondary-btn-custom{border:none;box-shadow:inset 0 0 0 1px var(--primary-button);color:var(--primary-button)}.LoadingModal .footer .modal-button.cancel.disabled,.LoadingModal .footer .modal-button.secondary-btn-custom.disabled{cursor:default;border:none;box-shadow:inset 0 0 0 1px rgba(43,115,171,.5);color:rgba(43,115,171,.5)}.LoadingModal .footer .modal-button.cancel.disabled span,.LoadingModal .footer .modal-button.secondary-btn-custom.disabled span{color:rgba(43,115,171,.5)}.LoadingModal{position:fixed;left:0;bottom:0;z-index:100;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:var(--modal-negative-space)}.LoadingModal .modal-container .wrapper .modal-content{padding:10px}.LoadingModal .footer{display:flex;flex-direction:row;justify-content:flex-end;width:100%;margin-top:13px}.LoadingModal .footer.modal-footer{padding:16px;margin:0;border-top:1px solid var(--divider)}.LoadingModal .footer .modal-button{display:flex;justify-content:center;align-items:center;padding:6px 18px;margin:8px 0 0;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:30px;cursor:pointer}.LoadingModal .footer .modal-button.confirm{margin-left:4px}.LoadingModal .footer .modal-button.secondary-btn-custom{border-radius:4px;padding:2px 20px 4px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .LoadingModal .footer .modal-button{padding:23px 8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .LoadingModal .footer .modal-button{padding:23px 8px}}.LoadingModal .swipe-indicator{background:var(--swipe-indicator-bg);border-radius:2px;height:4px;width:38px;position:absolute;top:12px;margin-left:auto;margin-right:auto;left:0;right:0}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .LoadingModal .swipe-indicator{display:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .LoadingModal .swipe-indicator{display:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .LoadingModal .swipe-indicator{width:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .LoadingModal .swipe-indicator{width:32px}}.LoadingModal .container{display:flex;flex-direction:column;align-items:center;padding:10px}.LoadingModal .inner-wrapper{margin:10px;border-top:5px solid var(--border);border:5px solid var(--border);border-top-color:var(--focus-border);border-radius:50%;width:54px;height:54px;animation:spin 1.2s ease infinite}@keyframes spin{0%{transform:rotate(0deg)}to{transform:rotate(1turn)}}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React, { useEffect } from 'react';\nimport { shallowEqual, useDispatch, useSelector } from 'react-redux';\nimport actions from 'actions';\nimport selectors from 'selectors';\nimport classNames from 'classnames';\nimport DataElements from 'constants/dataElement';\n\nimport './LoadingModal.scss';\n\nconst LoadingModal = () => {\n  const isOpen = useSelector((state) => selectors.isElementOpen(state, DataElements.LOADING_MODAL), shallowEqual);\n  const isDisabled = useSelector((state) => selectors.isElementDisabled(state, DataElements.LOADING_MODAL), shallowEqual);\n\n  const dispatch = useDispatch();\n\n  useEffect(() => {\n    if (isOpen) {\n      dispatch(actions.closeElements([\n        DataElements.SIGNATURE_MODAL,\n        DataElements.PRINT_MODAL,\n        DataElements.ERROR_MODAL,\n      ]));\n    }\n  }, [isOpen]);\n\n  if (isDisabled) {\n    return null;\n  }\n\n  return (\n    <div\n      className={classNames({\n        'Modal': true,\n        'LoadingModal': true,\n        'open': isOpen,\n      })}\n      data-element={DataElements.LOADING_MODAL}\n    >\n      <div className=\"container\">\n        <div className=\"inner-wrapper\"></div>\n      </div>\n    </div>\n  );\n};\n\nexport default LoadingModal;", "import LoadingModal from './LoadingModal';\n\nexport default LoadingModal;\n"], "sourceRoot": ""}