{"version": 3, "sources": ["webpack:///./src/ui/src/helpers/adjustFreeTextBoundingBox.js"], "names": ["annotation", "FreeTextAnnotation", "window", "Core", "Annotations", "getAutoSizeType", "AutoSizeTypes", "NONE", "doc", "core", "getDocument", "pageNumber", "pageInfo", "getPageInfo", "pageMatrix", "getPageMatrix", "pageRotation", "getPageRotation", "fitText"], "mappings": "2FAAA,WAEe,aAACA,GACd,IAAQC,EAAuBC,OAAOC,KAAKC,YAAnCH,mBACR,GAAID,aAAsBC,GAAsBD,EAAWK,oBAAsBJ,EAAmBK,cAAcC,KAAM,CACtH,IAAMC,EAAMC,IAAKC,cACXC,EAAaX,EAAuB,WACpCY,EAAWJ,EAAIK,YAAYF,GAC3BG,EAAaN,EAAIO,cAAcJ,GAC/BK,EAAeR,EAAIS,gBAAgBN,GACzCX,EAAWkB,QAAQN,EAAUE,EAAYE", "file": "chunks/chunk.6.js", "sourcesContent": ["import core from 'core';\n\nexport default (annotation) => {\n  const { FreeTextAnnotation } = window.Core.Annotations;\n  if (annotation instanceof FreeTextAnnotation && annotation.getAutoSizeType() !== FreeTextAnnotation.AutoSizeTypes.NONE) {\n    const doc = core.getDocument();\n    const pageNumber = annotation['PageNumber'];\n    const pageInfo = doc.getPageInfo(pageNumber);\n    const pageMatrix = doc.getPageMatrix(pageNumber);\n    const pageRotation = doc.getPageRotation(pageNumber);\n    annotation.fitText(pageInfo, pageMatrix, pageRotation);\n  }\n};"], "sourceRoot": ""}