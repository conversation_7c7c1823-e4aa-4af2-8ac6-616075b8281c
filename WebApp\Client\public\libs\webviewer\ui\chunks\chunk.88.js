(window.webpackJsonp=window.webpackJsonp||[]).push([[88],{1973:function(e,t,a){"use strict";a.r(t);a(60),a(44);var r=a(0),n=a.n(r),l=a(84),s=a(4),o=a.n(s),i=a(68),c=a(71);function d(){return(d=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)Object.prototype.hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(this,arguments)}var y={borderStyle:o.a.string,isFlyoutItem:o.a.bool,style:o.a.object,className:o.a.string},p=Object(r.forwardRef)((function(e,t){var a=e.isFlyoutItem,r=e.borderStyle,s=e.style,o=e.className,y=c.b.cellBorderStyle,p=y.dataElement,u=y.icon,b=y.title,f=function(){};return a?n.a.createElement(i.a,d({},e,{ref:t,onClick:f,additionalClass:""})):n.a.createElement(l.a,{key:r,isActive:!1,onClick:f,dataElement:p,title:b,img:u,ariaPressed:!1,style:s,className:o})}));p.propTypes=y,p.displayName="BorderStyleButton",t.default=p}}]);
//# sourceMappingURL=chunk.88.js.map