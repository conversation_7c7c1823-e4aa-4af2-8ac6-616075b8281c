# Data Visualization Service Integration Plan

## Current State Analysis

### ✅ What's Working Well
- **Service Architecture**: Well-designed service layer with clear separation of concerns
- **Node Services**: Comprehensive node services with positioning, rendering, and events
- **Type Safety**: Strong TypeScript implementation throughout
- **Configuration**: Flexible configuration system

### ⚠️ Critical Issues Found

1. **Code Duplication**: Main managers have duplicate code that should use services
2. **Incomplete Integration**: Services exist but aren't being used by managers
3. **Missing Link Services**: Empty `modules/services/link/` directory
4. **Inconsistent Patterns**: Some managers use services, others don't

### 🔍 Specific Problems

#### Node Manager Issues
- `node-manager.ts` has 1,022 lines with duplicate rendering code
- Should delegate to `NodeRenderer` service but implements rendering directly
- Positioning logic duplicated between manager and `NodePositioningService`
- Event handling not using `NodeEventService`

#### Link Manager Issues
- No corresponding link services in `modules/services/link/`
- All link logic embedded in `link-manager.ts` (1,596 lines)
- Missing service layer for link positioning, rendering, bundling

#### Service Completion Issues
- `node-interaction-service.ts` is empty (1 line)
- Services exist but aren't imported/used by managers
- No service orchestration pattern

## Integration Strategy

### Phase 1: Complete Node Service Integration (Priority 1)

#### 1.1 Refactor NodeManager to Use Services ✅ STARTED
**Status**: In progress
**Files**: `modules/node-manager.ts`

**Tasks**:
- [x] Import specialized services
- [x] Initialize services in constructor
- [x] Setup event forwarding
- [ ] Replace duplicate positioning code with service calls
- [ ] Replace duplicate rendering code with service calls
- [ ] Remove duplicate methods from manager

#### 1.2 Complete Missing Node Services
**Files**: `modules/services/node/node-interaction-service.ts`

**Tasks**:
- [ ] Implement node interaction service
- [ ] Add hover state management
- [ ] Add click/double-click handling
- [ ] Add drag and drop support

#### 1.3 Enhance Node Event Integration
**Files**: `modules/services/node/node-events-service.ts`

**Tasks**:
- [ ] Add more event types (drag, drop, focus, blur)
- [ ] Implement event bubbling/capturing
- [ ] Add event validation and sanitization

### Phase 2: Create Link Services (Priority 2)

#### 2.1 Create Link Service Architecture
**New Files**:
- `modules/services/link/link-positioning-service.ts`
- `modules/services/link/link-rendering-service.ts`
- `modules/services/link/link-bundling-service.ts`
- `modules/services/link/link-routing-service.ts`

#### 2.2 Extract Link Logic from LinkManager
**Tasks**:
- [ ] Move path calculation to `link-routing-service.ts`
- [ ] Move bundling logic to `link-bundling-service.ts`
- [ ] Move rendering to `link-rendering-service.ts`
- [ ] Refactor `link-manager.ts` to orchestrate services

### Phase 3: Service Pattern Standardization (Priority 3)

#### 3.1 Create Service Base Classes
**New Files**:
- `modules/services/base/base-service.ts`
- `modules/services/base/base-rendering-service.ts`
- `modules/services/base/base-event-service.ts`

#### 3.2 Implement Service Registry
**New Files**:
- `modules/services/service-registry.ts`
- `modules/services/service-factory.ts`

## Implementation Tasks

### Immediate Actions (This Sprint)

#### Task 1: Complete NodeManager Refactoring
**Assignee**: Senior Developer
**Estimated Time**: 4-6 hours

```typescript
// Remove these duplicate methods from node-manager.ts:
- renderCustomNodes()
- renderNodeBackground()
- renderLeftBorder()
- renderNodeText()
- renderNodeIcon()
- renderInactiveOverlay()

// Replace with:
public createNodes(g, data) {
  return this.renderService.createNodes(g, data);
}
```

#### Task 2: Create Link Services
**Assignee**: Developer familiar with D3
**Estimated Time**: 8-12 hours

**Link Positioning Service**:
```typescript
export class LinkPositioningService {
  calculateLinkPath(link: Link, nodes: Node[]): string
  getNodeEdgePoint(node: Node, target: Node): Position
  calculateBezierPath(start: Position, end: Position): string
}
```

**Link Rendering Service**:
```typescript
export class LinkRenderingService {
  createLinks(g: SVGGElement, data: DataNetwork): d3.Selection
  updateLinkPositions(selection: d3.Selection): void
  setupMarkers(svg: SVGSVGElement): void
}
```

#### Task 3: Service Integration Testing
**Assignee**: QA Engineer
**Estimated Time**: 4-6 hours

**Test Cases**:
- [ ] Node positioning works with service
- [ ] Node rendering works with service
- [ ] Events are properly forwarded
- [ ] Performance is maintained
- [ ] Memory usage is acceptable

### Medium Priority (Next Sprint)

#### Task 4: Link Service Implementation
- [ ] Implement all link services
- [ ] Refactor link-manager.ts
- [ ] Test link functionality
- [ ] Performance validation

#### Task 5: Service Pattern Standardization
- [ ] Create base service classes
- [ ] Implement service registry
- [ ] Update all services to use patterns
- [ ] Documentation updates

### Future Enhancements

#### Task 6: Advanced Service Features
- [ ] Service dependency injection
- [ ] Service lifecycle management
- [ ] Service configuration hot-reloading
- [ ] Service performance monitoring

## Code Examples

### Before (Current - Problematic)
```typescript
// node-manager.ts - 1,022 lines with duplicate code
export class NodeManager {
  private renderCustomNodes(selection) {
    // 200+ lines of rendering code that duplicates NodeRenderer
  }
  
  private setupNodePositions(nodes, x, y) {
    // 300+ lines of positioning code that duplicates NodePositioningService
  }
}
```

### After (Target - Clean)
```typescript
// node-manager.ts - ~200 lines, orchestrates services
export class NodeManager {
  constructor(config) {
    this.positioningService = new NodePositioningService(config);
    this.renderService = new NodeRenderer(config);
    this.eventService = new NodeEventService();
  }
  
  public createNodes(g, data) {
    return this.renderService.createNodes(g, data);
  }
  
  public setupNodePositions(nodes, x, y) {
    this.positioningService.setupNodePositions(nodes, x, y);
  }
}
```

## Success Metrics

### Code Quality
- [ ] **Reduce code duplication**: <5% duplicate code between managers and services
- [ ] **Improve maintainability**: Cyclomatic complexity <10 per method
- [ ] **Increase test coverage**: >80% coverage for all services

### Performance
- [ ] **Maintain rendering performance**: No regression in frame rate
- [ ] **Memory efficiency**: <10% increase in memory usage
- [ ] **Initialization time**: <500ms for typical datasets

### Developer Experience
- [ ] **Clear service boundaries**: Each service has single responsibility
- [ ] **Consistent patterns**: All services follow same architectural patterns
- [ ] **Good documentation**: All public APIs documented

## Risk Mitigation

### Technical Risks
1. **Performance Regression**: Continuous benchmarking during refactoring
2. **Breaking Changes**: Maintain public API compatibility
3. **Service Complexity**: Keep services focused and simple

### Mitigation Strategies
1. **Incremental Refactoring**: One service at a time
2. **Comprehensive Testing**: Unit and integration tests
3. **Performance Monitoring**: Before/after comparisons
4. **Rollback Plan**: Git branches for each phase

## Next Steps

1. **Complete NodeManager refactoring** (started above)
2. **Create link services** following node service patterns
3. **Implement service integration tests**
4. **Document service architecture**
5. **Plan service registry implementation**

This integration will complete the modularization by ensuring the service layer is properly utilized, eliminating code duplication, and creating a truly maintainable architecture.
