(window.webpackJsonp=window.webpackJsonp||[]).push([[95],{1968:function(t,e,a){"use strict";a.r(e);a(60),a(44);var n=a(0),l=a.n(n),i=a(6),r=a(5),s=a(84),o=a(4),c=a.n(o),m=a(2),u=a(68),g=a(71);function p(){return(p=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var a=arguments[e];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(t[n]=a[n])}return t}).apply(this,arguments)}var b={isFlyoutItem:c.a.bool,alignment:c.a.string,style:c.a.object,className:c.a.string},d=Object(n.forwardRef)((function(t,e){var a=t.isFlyoutItem,n=t.alignment,o=t.style,c=t.className,b=Object(i.d)(),d="cellTextAlignment";n&&(d=n);var f=g.b[d],y=f.dataElement,E=f.icon,v=f.title,w=function(){b(m.a.setFlyoutToggleElement(y)),b(m.a.toggleElement(r.a.CELL_TEXT_ALIGNMENT_FLYOUT))};return a?l.a.createElement(u.a,p({},t,{ref:e,onClick:w,additionalClass:""})):l.a.createElement(s.a,{ariaCurrent:!1,isActive:!1,dataElement:y,title:v,img:E,onClick:w,style:o,className:c})}));d.propTypes=b,d.displayName="TextAlignmentButton",e.default=d}}]);
//# sourceMappingURL=chunk.95.js.map