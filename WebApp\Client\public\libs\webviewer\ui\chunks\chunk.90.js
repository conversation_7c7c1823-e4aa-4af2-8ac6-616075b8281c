(window.webpackJsonp=window.webpackJsonp||[]).push([[90],{1974:function(e,t,a){"use strict";a.r(t);a(97),a(16),a(60),a(44);var n=a(0),s=a.n(n),r=a(84),o=a(4),i=a.n(o),l=a(35),c=a(68),u=a(71);function p(){return(p=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e}).apply(this,arguments)}var y={styleType:i.a.oneOf(Object.values(l.s)).isRequired,isFlyoutItem:i.a.bool,style:i.a.object,className:i.a.string},f=Object(n.forwardRef)((function(e,t){var a=e.isFlyoutItem,n=e.styleType,o=e.style,i=e.className,l=n.charAt(0).toLowerCase()+n.slice(1),y=u.b["".concat(l,"Button")],f=y.dataElement,b=y.icon,d=y.title,m=function(){};return a?s.a.createElement(c.a,p({},e,{ref:t,onClick:m,additionalClass:""})):s.a.createElement(r.a,{ariaCurrent:!1,isActive:!1,dataElement:f,title:d,img:b,onClick:m,style:o,className:i})}));f.propTypes=y,f.displayName="CellDecoratorButton",t.default=f}}]);
//# sourceMappingURL=chunk.90.js.map