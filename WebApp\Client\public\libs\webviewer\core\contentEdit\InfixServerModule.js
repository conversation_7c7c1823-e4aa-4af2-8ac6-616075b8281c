(function(){/*
 The buffer module from node.js, for the browser.

 <AUTHOR> <http://feross.org>
 @license  MIT
*/
var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.arrayIteratorImpl=function(r){var m=0;return function(){return m<r.length?{done:!1,value:r[m++]}:{done:!0}}};$jscomp.arrayIterator=function(r){return{next:$jscomp.arrayIteratorImpl(r)}};$jscomp.makeIterator=function(r){var m="undefined"!=typeof Symbol&&Symbol.iterator&&r[Symbol.iterator];if(m)return m.call(r);if("number"==typeof r.length)return $jscomp.arrayIterator(r);throw Error(String(r)+" is not an iterable or ArrayLike");};$jscomp.ASSUME_ES5=!1;
$jscomp.ASSUME_NO_NATIVE_MAP=!1;$jscomp.ASSUME_NO_NATIVE_SET=!1;$jscomp.SIMPLE_FROUND_POLYFILL=!1;$jscomp.ISOLATE_POLYFILLS=!1;$jscomp.FORCE_POLYFILL_PROMISE=!1;$jscomp.FORCE_POLYFILL_PROMISE_WHEN_NO_UNHANDLED_REJECTION=!1;$jscomp.getGlobal=function(r){r=["object"==typeof globalThis&&globalThis,r,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var m=0;m<r.length;++m){var l=r[m];if(l&&l.Math==Math)return l}throw Error("Cannot find global object");};
$jscomp.global=$jscomp.getGlobal(this);$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(r,m,l){if(r==Array.prototype||r==Object.prototype)return r;r[m]=l.value;return r};$jscomp.IS_SYMBOL_NATIVE="function"===typeof Symbol&&"symbol"===typeof Symbol("x");$jscomp.TRUST_ES6_POLYFILLS=!$jscomp.ISOLATE_POLYFILLS||$jscomp.IS_SYMBOL_NATIVE;$jscomp.polyfills={};$jscomp.propertyToPolyfillSymbol={};$jscomp.POLYFILL_PREFIX="$jscp$";
var $jscomp$lookupPolyfilledValue=function(r,m,l){if(!l||null!=r){l=$jscomp.propertyToPolyfillSymbol[m];if(null==l)return r[m];l=r[l];return void 0!==l?l:r[m]}};$jscomp.polyfill=function(r,m,l,k){m&&($jscomp.ISOLATE_POLYFILLS?$jscomp.polyfillIsolated(r,m,l,k):$jscomp.polyfillUnisolated(r,m,l,k))};
$jscomp.polyfillUnisolated=function(r,m,l,k){l=$jscomp.global;r=r.split(".");for(k=0;k<r.length-1;k++){var g=r[k];if(!(g in l))return;l=l[g]}r=r[r.length-1];k=l[r];m=m(k);m!=k&&null!=m&&$jscomp.defineProperty(l,r,{configurable:!0,writable:!0,value:m})};
$jscomp.polyfillIsolated=function(r,m,l,k){var g=r.split(".");r=1===g.length;k=g[0];k=!r&&k in $jscomp.polyfills?$jscomp.polyfills:$jscomp.global;for(var n=0;n<g.length-1;n++){var d=g[n];if(!(d in k))return;k=k[d]}g=g[g.length-1];l=$jscomp.IS_SYMBOL_NATIVE&&"es6"===l?k[g]:null;m=m(l);null!=m&&(r?$jscomp.defineProperty($jscomp.polyfills,g,{configurable:!0,writable:!0,value:m}):m!==l&&(void 0===$jscomp.propertyToPolyfillSymbol[g]&&(l=1E9*Math.random()>>>0,$jscomp.propertyToPolyfillSymbol[g]=$jscomp.IS_SYMBOL_NATIVE?
$jscomp.global.Symbol(g):$jscomp.POLYFILL_PREFIX+l+"$"+g),$jscomp.defineProperty(k,$jscomp.propertyToPolyfillSymbol[g],{configurable:!0,writable:!0,value:m})))};
$jscomp.polyfill("Promise",function(r){function m(){this.batch_=null}function l(d){return d instanceof g?d:new g(function(h,t){h(d)})}if(r&&(!($jscomp.FORCE_POLYFILL_PROMISE||$jscomp.FORCE_POLYFILL_PROMISE_WHEN_NO_UNHANDLED_REJECTION&&"undefined"===typeof $jscomp.global.PromiseRejectionEvent)||!$jscomp.global.Promise||-1===$jscomp.global.Promise.toString().indexOf("[native code]")))return r;m.prototype.asyncExecute=function(d){if(null==this.batch_){this.batch_=[];var h=this;this.asyncExecuteFunction(function(){h.executeBatch_()})}this.batch_.push(d)};
var k=$jscomp.global.setTimeout;m.prototype.asyncExecuteFunction=function(d){k(d,0)};m.prototype.executeBatch_=function(){for(;this.batch_&&this.batch_.length;){var d=this.batch_;this.batch_=[];for(var h=0;h<d.length;++h){var t=d[h];d[h]=null;try{t()}catch(u){this.asyncThrow_(u)}}}this.batch_=null};m.prototype.asyncThrow_=function(d){this.asyncExecuteFunction(function(){throw d;})};var g=function(d){this.state_=0;this.result_=void 0;this.onSettledCallbacks_=[];this.isRejectionHandled_=!1;var h=this.createResolveAndReject_();
try{d(h.resolve,h.reject)}catch(t){h.reject(t)}};g.prototype.createResolveAndReject_=function(){function d(u){return function(v){t||(t=!0,u.call(h,v))}}var h=this,t=!1;return{resolve:d(this.resolveTo_),reject:d(this.reject_)}};g.prototype.resolveTo_=function(d){if(d===this)this.reject_(new TypeError("A Promise cannot resolve to itself"));else if(d instanceof g)this.settleSameAsPromise_(d);else{a:switch(typeof d){case "object":var h=null!=d;break a;case "function":h=!0;break a;default:h=!1}h?this.resolveToNonPromiseObj_(d):
this.fulfill_(d)}};g.prototype.resolveToNonPromiseObj_=function(d){var h=void 0;try{h=d.then}catch(t){this.reject_(t);return}"function"==typeof h?this.settleSameAsThenable_(h,d):this.fulfill_(d)};g.prototype.reject_=function(d){this.settle_(2,d)};g.prototype.fulfill_=function(d){this.settle_(1,d)};g.prototype.settle_=function(d,h){if(0!=this.state_)throw Error("Cannot settle("+d+", "+h+"): Promise already settled in state"+this.state_);this.state_=d;this.result_=h;2===this.state_&&this.scheduleUnhandledRejectionCheck_();
this.executeOnSettledCallbacks_()};g.prototype.scheduleUnhandledRejectionCheck_=function(){var d=this;k(function(){if(d.notifyUnhandledRejection_()){var h=$jscomp.global.console;"undefined"!==typeof h&&h.error(d.result_)}},1)};g.prototype.notifyUnhandledRejection_=function(){if(this.isRejectionHandled_)return!1;var d=$jscomp.global.CustomEvent,h=$jscomp.global.Event,t=$jscomp.global.dispatchEvent;if("undefined"===typeof t)return!0;"function"===typeof d?d=new d("unhandledrejection",{cancelable:!0}):
"function"===typeof h?d=new h("unhandledrejection",{cancelable:!0}):(d=$jscomp.global.document.createEvent("CustomEvent"),d.initCustomEvent("unhandledrejection",!1,!0,d));d.promise=this;d.reason=this.result_;return t(d)};g.prototype.executeOnSettledCallbacks_=function(){if(null!=this.onSettledCallbacks_){for(var d=0;d<this.onSettledCallbacks_.length;++d)n.asyncExecute(this.onSettledCallbacks_[d]);this.onSettledCallbacks_=null}};var n=new m;g.prototype.settleSameAsPromise_=function(d){var h=this.createResolveAndReject_();
d.callWhenSettled_(h.resolve,h.reject)};g.prototype.settleSameAsThenable_=function(d,h){var t=this.createResolveAndReject_();try{d.call(h,t.resolve,t.reject)}catch(u){t.reject(u)}};g.prototype.then=function(d,h){function t(w,E){return"function"==typeof w?function(C){try{u(w(C))}catch(y){v(y)}}:E}var u,v,x=new g(function(w,E){u=w;v=E});this.callWhenSettled_(t(d,u),t(h,v));return x};g.prototype.catch=function(d){return this.then(void 0,d)};g.prototype.callWhenSettled_=function(d,h){function t(){switch(u.state_){case 1:d(u.result_);
break;case 2:h(u.result_);break;default:throw Error("Unexpected state: "+u.state_);}}var u=this;null==this.onSettledCallbacks_?n.asyncExecute(t):this.onSettledCallbacks_.push(t);this.isRejectionHandled_=!0};g.resolve=l;g.reject=function(d){return new g(function(h,t){t(d)})};g.race=function(d){return new g(function(h,t){for(var u=$jscomp.makeIterator(d),v=u.next();!v.done;v=u.next())l(v.value).callWhenSettled_(h,t)})};g.all=function(d){var h=$jscomp.makeIterator(d),t=h.next();return t.done?l([]):new g(function(u,
v){function x(C){return function(y){w[C]=y;E--;0==E&&u(w)}}var w=[],E=0;do w.push(void 0),E++,l(t.value).callWhenSettled_(x(w.length-1),v),t=h.next();while(!t.done)})};return g},"es6","es3");
$jscomp.polyfill("Array.from",function(r){return r?r:function(m,l,k){l=null!=l?l:function(h){return h};var g=[],n="undefined"!=typeof Symbol&&Symbol.iterator&&m[Symbol.iterator];if("function"==typeof n){m=n.call(m);for(var d=0;!(n=m.next()).done;)g.push(l.call(k,n.value,d++))}else for(n=m.length,d=0;d<n;d++)g.push(l.call(k,m[d],d));return g}},"es6","es3");
$jscomp.checkStringArgs=function(r,m,l){if(null==r)throw new TypeError("The 'this' value for String.prototype."+l+" must not be null or undefined");if(m instanceof RegExp)throw new TypeError("First argument to String.prototype."+l+" must not be a regular expression");return r+""};
$jscomp.polyfill("String.prototype.endsWith",function(r){return r?r:function(m,l){var k=$jscomp.checkStringArgs(this,m,"endsWith");m+="";void 0===l&&(l=k.length);l=Math.max(0,Math.min(l|0,k.length));for(var g=m.length;0<g&&0<l;)if(k[--l]!=m[--g])return!1;return 0>=g}},"es6","es3");$jscomp.initSymbol=function(){};
$jscomp.polyfill("Symbol",function(r){if(r)return r;var m=function(n,d){this.$jscomp$symbol$id_=n;$jscomp.defineProperty(this,"description",{configurable:!0,writable:!0,value:d})};m.prototype.toString=function(){return this.$jscomp$symbol$id_};var l="jscomp_symbol_"+(1E9*Math.random()>>>0)+"_",k=0,g=function(n){if(this instanceof g)throw new TypeError("Symbol is not a constructor");return new m(l+(n||"")+"_"+k++,n)};return g},"es6","es3");
$jscomp.polyfill("Symbol.iterator",function(r){if(r)return r;r=Symbol("Symbol.iterator");for(var m="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),l=0;l<m.length;l++){var k=$jscomp.global[m[l]];"function"===typeof k&&"function"!=typeof k.prototype[r]&&$jscomp.defineProperty(k.prototype,r,{configurable:!0,writable:!0,value:function(){return $jscomp.iteratorPrototype($jscomp.arrayIteratorImpl(this))}})}return r},"es6",
"es3");$jscomp.iteratorPrototype=function(r){r={next:r};r[Symbol.iterator]=function(){return this};return r};$jscomp.checkEs6ConformanceViaProxy=function(){try{var r={},m=Object.create(new $jscomp.global.Proxy(r,{get:function(l,k,g){return l==r&&"q"==k&&g==m}}));return!0===m.q}catch(l){return!1}};$jscomp.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS=!1;$jscomp.ES6_CONFORMANCE=$jscomp.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS&&$jscomp.checkEs6ConformanceViaProxy();
$jscomp.owns=function(r,m){return Object.prototype.hasOwnProperty.call(r,m)};$jscomp.MapEntry=function(){};$jscomp.underscoreProtoCanBeSet=function(){var r={a:!0},m={};try{return m.__proto__=r,m.a}catch(l){}return!1};$jscomp.setPrototypeOf=$jscomp.TRUST_ES6_POLYFILLS&&"function"==typeof Object.setPrototypeOf?Object.setPrototypeOf:$jscomp.underscoreProtoCanBeSet()?function(r,m){r.__proto__=m;if(r.__proto__!==m)throw new TypeError(r+" is not extensible");return r}:null;
$jscomp.assign=$jscomp.TRUST_ES6_POLYFILLS&&"function"==typeof Object.assign?Object.assign:function(r,m){for(var l=1;l<arguments.length;l++){var k=arguments[l];if(k)for(var g in k)$jscomp.owns(k,g)&&(r[g]=k[g])}return r};$jscomp.polyfill("Array.prototype.fill",function(r){return r?r:function(m,l,k){var g=this.length||0;0>l&&(l=Math.max(0,g+l));if(null==k||k>g)k=g;k=Number(k);0>k&&(k=Math.max(0,g+k));for(l=Number(l||0);l<k;l++)this[l]=m;return this}},"es6","es3");
$jscomp.typedArrayFill=function(r){return r?r:Array.prototype.fill};$jscomp.polyfill("Int8Array.prototype.fill",$jscomp.typedArrayFill,"es6","es5");$jscomp.polyfill("Uint8Array.prototype.fill",$jscomp.typedArrayFill,"es6","es5");$jscomp.polyfill("Uint8ClampedArray.prototype.fill",$jscomp.typedArrayFill,"es6","es5");$jscomp.polyfill("Int16Array.prototype.fill",$jscomp.typedArrayFill,"es6","es5");$jscomp.polyfill("Uint16Array.prototype.fill",$jscomp.typedArrayFill,"es6","es5");
$jscomp.polyfill("Int32Array.prototype.fill",$jscomp.typedArrayFill,"es6","es5");$jscomp.polyfill("Uint32Array.prototype.fill",$jscomp.typedArrayFill,"es6","es5");$jscomp.polyfill("Float32Array.prototype.fill",$jscomp.typedArrayFill,"es6","es5");$jscomp.polyfill("Float64Array.prototype.fill",$jscomp.typedArrayFill,"es6","es5");
(function(r){function m(k){if(l[k])return l[k].exports;var g=l[k]={i:k,l:!1,exports:{}};r[k].call(g.exports,g,g.exports,m);g.l=!0;return g.exports}var l={};m.m=r;m.c=l;m.d=function(k,g,n){m.o(k,g)||Object.defineProperty(k,g,{enumerable:!0,get:n})};m.r=function(k){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(k,Symbol.toStringTag,{value:"Module"});Object.defineProperty(k,"__esModule",{value:!0})};m.t=function(k,g){g&1&&(k=m(k));if(g&8||g&4&&"object"===typeof k&&k&&k.__esModule)return k;
var n=Object.create(null);m.r(n);Object.defineProperty(n,"default",{enumerable:!0,value:k});if(g&2&&"string"!=typeof k)for(var d in k)m.d(n,d,function(h){return k[h]}.bind(null,d));return n};m.n=function(k){var g=k&&k.__esModule?function(){return k["default"]}:function(){return k};m.d(g,"a",g);return g};m.o=function(k,g){return Object.prototype.hasOwnProperty.call(k,g)};m.p="/core/contentEdit";return m(m.s=9)})([function(r,m,l){l.d(m,"b",function(){return g});l.d(m,"a",function(){return n});var k=
l(2),g=function(d,h){Object(k.a)("disableLogs")||(h?console.warn("".concat(d,": ").concat(h)):console.warn(d))},n=function(d,h){}},function(r,m,l){l.d(m,"a",function(){return Q});l.d(m,"b",function(){return T});var k=l(6),g=l(0),n=l(4),d=l(3),h="undefined"===typeof window?self:window,t=h.importScripts,u=!1,v=function(z,q){u||(t("".concat(h.basePath,"decode.min.js")),u=!0);z=Object(d.b)(z);z=self.BrotliDecode(z);return q?z:Object(d.a)(z)},x=function(z,q){return Object(k.a)(void 0,void 0,Promise,function(){var e;
return Object(k.b)(this,function(B){switch(B.label){case 0:return u?[3,2]:[4,Object(n.a)("".concat(self.Core.getWorkerPath(),"external/decode.min.js"),"Failed to download decode.min.js",window)];case 1:B.sent(),u=!0,B.label=2;case 2:return e=self.BrotliDecode(Object(d.b)(z)),[2,q?e:Object(d.a)(e)]}})})};(function(){function z(){this.remainingDataArrays=[]}z.prototype.processRaw=function(q){return q};z.prototype.processBrotli=function(q){this.remainingDataArrays.push(q);return null};z.prototype.GetNextChunk=
function(q){this.decodeFunction||(this.decodeFunction=0===q[0]&&97===q[1]&&115===q[2]&&109===q[3]?this.processRaw:this.processBrotli);return this.decodeFunction(q)};z.prototype.End=function(){if(this.remainingDataArrays.length){for(var q=this.arrays,e=0,B=0;B<q.length;++B)e+=q[B].length;e=new Uint8Array(e);var A=0;for(B=0;B<q.length;++B){var F=q[B];e.set(F,A);A+=F.length}return v(e,!0)}return null};return z})();var w=function(z,q,e){void 0===q&&(q=!0);void 0===e&&(e=!1);var B=new XMLHttpRequest;B.open("GET",
z,q);z=e&&B.overrideMimeType;B.responseType=z?"text":"arraybuffer";z&&B.overrideMimeType("text/plain; charset=x-user-defined");return B},E=function(z,q,e){return new Promise(function(B,A){var F=w(z,q,e);F.send();F.onload=function(){200===this.status||0===this.status?B(F.response):A(Error("Download Failed ".concat(z)))};F.onerror=function(){A(Error("Network error occurred ".concat(z)))}})},C=function(z,q){var e=q.decompressFunction,B=q.shouldOutputArray,A=q.compressedMaximum,F="undefined"!==typeof t?
Date.now():null;try{var J=B?y(z):z.join("");Object(g.a)("worker","Result length is ".concat(J.length));if(J.length<A){var L=e(J,B);Object(g.b)("There may be some degradation of performance. Your server has not been configured to serve .gz. and .br. files with the expected Content-Encoding. See https://docs.apryse.com/documentation/web/faq/content-encoding/ for instructions on how to resolve this.");t&&Object(g.a)("worker","Decompressed length is ".concat(L.length));J=L}else B||(J=Object(d.a)(J));
if(t){var O=q.paths.join(", ");Object(g.a)("worker","".concat(O," Decompression took ").concat(Date.now()-F," ms"))}return J}catch(N){throw Error("Failed to decompress: ".concat(N));}},y=function(z){z=z.reduce(function(q,e){e=new Uint8Array(e);return q.concat(Array.from(e))},[]);return new Uint8Array(z)},K=function(z){var q=!z.shouldOutputArray,e=z.paths,B=z.isAsync;B?e=Promise.all(e.map(function(A){return E(A,B,q)})).then(function(A){return C(A,z)}).catch(function(A){throw Error("Failed to fetch or decompress files: ".concat(A.message));
}):(e=e.map(function(A){var F=w(A,B,q);F.send();if(200===F.status||0===F.status)return F.response;throw Error("Failed to load ".concat(A));}),e=C(e,z));return e},Q=function(z){var q=z.lastIndexOf("/");-1===q&&(q=0);var e=z.slice(q).replace(".",".br.");t||(e.endsWith(".js.mem")?e=e.replace(".js.mem",".mem"):e.endsWith(".js")&&(e=e.concat(".mem")));return z.slice(0,q)+e},R=function(z){return z.map(function(q){return Q(q)})},G=function(z,q){q.decompressFunction=t?v:x;q.paths=R(z);return K(q)},M=function(z,
q,e,B){return z.catch(function(A){Object(g.b)(A);return B(q,e)})},T=function(z,q,e,B){z=Array.isArray(z)?z:[z];a:{var A=[G];q={compressedMaximum:q,isAsync:e,shouldOutputArray:B};if(q.isAsync){var F=A[0](z,q);for(e=1;e<A.length;++e)F=M(F,z,q,A[e])}else{for(e=0;e<A.length;e++){B=A[e];try{F=B(z,q);break a}catch(J){Object(g.b)(J.message)}}throw Error("None of the worker files were able to load. ");}}return F}},function(r,m,l){l.d(m,"a",function(){return n});l.d(m,"b",function(){return d});var k={},g=
{flattenedResources:!1,CANVAS_CACHE_SIZE:void 0,maxPagesBefore:void 0,maxPagesAhead:void 0,disableLogs:!1,wvsQueryParameters:{},_trnDebugMode:!1,_logFiltersEnabled:null},n=function(h){return g[h]},d=function(h,t){var u;g[h]=t;null===(u=k[h])||void 0===u?void 0:u.forEach(function(v){v(t)})}},function(r,m,l){l.d(m,"b",function(){return k});l.d(m,"a",function(){return g});var k=function(n){if("string"===typeof n){for(var d=new Uint8Array(n.length),h=n.length,t=0;t<h;t++)d[t]=n.charCodeAt(t);return d}return n},
g=function(n){if("string"!==typeof n){for(var d="",h=0,t=n.length,u;h<t;)u=n.subarray(h,h+1024),h+=1024,d+=String.fromCharCode.apply(null,u);return d}return n}},function(r,m,l){function k(n,d,h){return new Promise(function(t){if(!n)return t();var u=h.document.createElement("script");u.type="text/javascript";u.onload=function(){t()};u.onerror=function(){d&&Object(g.b)(d);t()};u.src=n;h.document.getElementsByTagName("head")[0].appendChild(u)})}l.d(m,"a",function(){return k});var g=l(0)},function(r,
m,l){function k(t,u,v,x){return g(t,u,v,x,!!WebAssembly.instantiateStreaming,void 0,void 0).then(function(w){Object(n.a)("load","WASM compilation took ".concat(Date.now()-NaN," ms"));return w})}function g(t,u,v,x,w,E,C){E=E||Date.now();if(w&&!x)return Object(n.a)("load","Try instantiateStreaming"),fetch(Object(d.a)(t)).then(function(y){return WebAssembly.instantiateStreaming(y,u)}).catch(function(y){Object(n.a)("load","instantiateStreaming Failed ".concat(t," message ").concat(y.message));return g(t,
u,v,x,!1,E,C)});w=x?x.map(function(y,K){return"".concat(y,"PDFNetCWasm-chunk-").concat(K,".wasm")}):t;return Object(d.b)(w,v,!0,!0).then(function(y){C=Date.now();Object(n.a)("load","Request took ".concat(C-E," ms"));return WebAssembly.instantiate(y,u)})}l.d(m,"a",function(){return k});var n=l(0),d=l(1),h=l(4);l.d(m,"b",function(){return h.a})},function(r,m,l){function k(n,d,h,t){function u(v){return v instanceof h?v:new h(function(x){x(v)})}return new (h||(h=Promise))(function(v,x){function w(y){try{C(t.next(y))}catch(K){x(K)}}
function E(y){try{C(t["throw"](y))}catch(K){x(K)}}function C(y){y.done?v(y.value):u(y.value).then(w,E)}C((t=t.apply(n,d||[])).next())})}function g(n,d){function h(C){return function(y){return t([C,y])}}function t(C){if(v)throw new TypeError("Generator is already executing.");for(;E&&(E=0,C[0]&&(u=0)),u;)try{if(v=1,x&&(w=C[0]&2?x["return"]:C[0]?x["throw"]||((w=x["return"])&&w.call(x),0):x.next)&&!(w=w.call(x,C[1])).done)return w;if(x=0,w)C=[C[0]&2,w.value];switch(C[0]){case 0:case 1:w=C;break;case 4:return u.label++,
{value:C[1],done:!1};case 5:u.label++;x=C[1];C=[0];continue;case 7:C=u.ops.pop();u.trys.pop();continue;default:if(!(w=u.trys,w=0<w.length&&w[w.length-1])&&(6===C[0]||2===C[0])){u=0;continue}if(3===C[0]&&(!w||C[1]>w[0]&&C[1]<w[3]))u.label=C[1];else if(6===C[0]&&u.label<w[1])u.label=w[1],w=C;else if(w&&u.label<w[2])u.label=w[2],u.ops.push(C);else{w[2]&&u.ops.pop();u.trys.pop();continue}}C=d.call(n,u)}catch(y){C=[6,y],x=0}finally{v=w=0}if(C[0]&5)throw C[1];return{value:C[0]?C[1]:void 0,done:!0}}var u=
{label:0,sent:function(){if(w[0]&1)throw w[1];return w[1]},trys:[],ops:[]},v,x,w,E=Object.create(("function"===typeof Iterator?Iterator:Object).prototype);return E.next=h(0),E["throw"]=h(1),E["return"]=h(2),"function"===typeof Symbol&&(E[Symbol.iterator]=function(){return this}),E}l.d(m,"a",function(){return k});l.d(m,"b",function(){return g})},function(r,m,l){l.d(m,"a",function(){return h});var k=l(1),g=l(5),n=l(8),d=function(){function t(u){var v=this;this.promise=u.then(function(x){v.response=
x;v.status=200})}t.prototype.addEventListener=function(u,v){this.promise.then(v)};return t}(),h=function(t,u,v,x){if(Object(n.a)()&&!v){self.Module.instantiateWasm=function(E,C){return Object(g.a)("".concat(t,"Wasm.wasm"),E,u["Wasm.wasm"],x).then(function(y){C(y.instance)})};if(u.disableObjectURLBlobs){importScripts("".concat(t,"Wasm.js"));return}v=Object(k.b)("".concat(t,"Wasm.js.mem"),u["Wasm.js.mem"],!1,!1)}else{if(u.disableObjectURLBlobs){importScripts("".concat((self.Module.asmjsPrefix?self.Module.asmjsPrefix:
"")+t,".js"));return}v=Object(k.b)("".concat((self.Module.asmjsPrefix?self.Module.asmjsPrefix:"")+t,".js.mem"),u[".js.mem"],!1);var w=Object(k.b)("".concat((self.Module.memoryInitializerPrefixURL?self.Module.memoryInitializerPrefixURL:"")+t,".mem"),u[".mem"],!0,!0);self.Module.memoryInitializerRequest=new d(w)}v=new Blob([v],{type:"application/javascript"});importScripts(URL.createObjectURL(v))}},function(r,m,l){l.d(m,"a",function(){return C});var k,g="undefined"===typeof window?self:window;r=function(){var y=
navigator.userAgent.toLowerCase();return(y=/(msie) ([\w.]+)/.exec(y)||/(trident)(?:.*? rv:([\w.]+)|)/.exec(y))?parseInt(y[2],10):y}();var n=function(){var y=g.navigator.userAgent.match(/OPR/),K=g.navigator.userAgent.match(/Maxthon/),Q=g.navigator.userAgent.match(/Edge/);return g.navigator.userAgent.match(/Chrome\/(.*?) /)&&!y&&!K&&!Q}();(function(){if(!n)return null;var y=g.navigator.userAgent.match(/Chrome\/([0-9]+)\./);return y?parseInt(y[1],10):y})();var d=!!navigator.userAgent.match(/Edge/i)||
navigator.userAgent.match(/Edg\/(.*?)/)&&g.navigator.userAgent.match(/Chrome\/(.*?) /);(function(){if(!d)return null;var y=g.navigator.userAgent.match(/Edg\/([0-9]+)\./);return y?parseInt(y[1],10):y})();m=/iPad|iPhone|iPod/.test(g.navigator.platform)||"MacIntel"===navigator.platform&&1<navigator.maxTouchPoints||/iPad|iPhone|iPod/.test(g.navigator.userAgent);var h=function(){var y=g.navigator.userAgent.match(/.*\/([0-9\.]+)\s(Safari|Mobile).*/i);return y?parseFloat(y[1]):y}(),t=/^((?!chrome|android).)*safari/i.test(g.navigator.userAgent)||
/^((?!chrome|android).)*$/.test(g.navigator.userAgent)&&m;t&&/^((?!chrome|android).)*safari/i.test(navigator.userAgent)&&parseInt(null===(k=navigator.userAgent.match(/Version\/(\d+)/))||void 0===k?void 0:k[1],10);var u=g.navigator.userAgent.match(/Firefox/);(function(){if(!u)return null;var y=g.navigator.userAgent.match(/Firefox\/([0-9]+)\./);return y?parseInt(y[1],10):y})();r||/Android|webOS|Touch|IEMobile|Silk/i.test(navigator.userAgent);navigator.userAgent.match(/(iPad|iPhone|iPod)/i);g.navigator.userAgent.indexOf("Android");
var v=/Mac OS X 10_13_6.*\(KHTML, like Gecko\)$/.test(g.navigator.userAgent),x=g.navigator.userAgent.match(/(iPad|iPhone).+\sOS\s((\d+)(_\d)*)/i)?14<=parseInt(g.navigator.userAgent.match(/(iPad|iPhone).+\sOS\s((\d+)(_\d)*)/i)[3],10):!1,w=!(!self.WebAssembly||!self.WebAssembly.validate),E=-1<g.navigator.userAgent.indexOf("Edge/16")||-1<g.navigator.userAgent.indexOf("MSAppHost"),C=function(){return w&&!E&&!(!x&&(t&&14>h||v))}},function(r,m,l){r.exports=l(10)},function(r,m,l){l.r(m);(function(k){function g(q){return q.split("<File>")[1].split("</File>")[0]}
function n(q,e,B){var A=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;q="<InfixServer>".concat(q,"</InfixServer>");null==A&&(A="importCommand"+B+".xml");FS.writeFile(A,q);z.ccall("wasmRunXML","number",["string","string"],[A,e]);FS.unlink(A)}function d(q){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:!0,B=FS.readFile(q).buffer;e&&FS.unlink(q);B||console.warn("No buffer results found for: ".concat(q));return B}function h(q){1==Q?postMessage({cmd:"isReady",taskId:q}):setTimeout(function(){return h(q)},
300)}function t(q,e){var B=q.galleyId,A=q.cmd,F=q.subCmd,J=q.caretStart,L=q.caretEnd,O=q.resultsFile,N=q.commandXML,S=q.commandFile,V=q.pageNumber;q=q.taskId;if("editText"===A)var U=F;else if("performUndoRedo"===A||"transformTextBox"===A)U=A;n(N,O,0,S);A=d(O);postMessage({cmd:"editText",subCmd:U,caretStart:J,caretEnd:L,galleyId:B,commandXML:!1===e?null:N,resultsXML:A,pageNumber:V,taskId:q},[A])}function u(q,e,B,A){n(q,B,0,e);e=d(B);A||(q=null);postMessage({cmd:"insertTextBox",commandXML:q,resultsXML:e},
[e])}function v(q,e){var B=q.galleyId,A=q.resultsFile,F=q.willTriggerTextContentUpdated,J=q.pageNumber;n(q.commandXML,A,0,q.commandFile);A=d(A);postMessage({pageNumber:J,cmd:e,resultsXML:A,galleyId:B,willTriggerTextContentUpdated:F,taskId:q.taskId},[A])}function x(q,e,B,A,F,J){if(e){B=new Uint8Array(B);e="inputFile"+q+".pdf";FS.writeFile(e,B);A=new Uint8Array(A);var L=(new TextDecoder("utf-8")).decode(A);B="objects"+q+".xml";A="results"+q+".xml";L='\n  <Commands>\n    <Command Name="LoadPDF"><File>'.concat(e,
'</File></Command>\n    <Command Name="Page BBox"><StartPage>1</StartPage><EndPage>1</EndPage></Command>\n    ').concat(L?'<Command Name="AddTableBoxes">'.concat(L,"</Command>"):"",'\n    <Command Name="Edit Page">\n      <WebFontURL>').concat(T||"https://www.pdftron.com/webfonts/v2/","</WebFontURL>\n      <Output>").concat(B,"</Output>\n      <ImagesAndText/>\n      <IgnoreRotation>false</IgnoreRotation>\n      ").concat(K?"<ForceReflow/>":"","\n    </Command>\n  </Commands>");n(L,A,1);G=q;F&&(F=
d(e,!1),e=d(B),A=d(A),postMessage({cmd:"exportFile",pageNumber:q,exportPerformed:!0,pdfBuffer:F,objectXML:e,resultsXML:A,taskId:J},[F,A]))}else postMessage({cmd:"exportFile",pageNumber:q,exportPerformed:!1})}function w(q){var e=q.pdfFile,B=q.tableData,A=q.subCmd,F=q.pageNumber,J=q.commandXML,L=q.objectID,O=q.isText,N=q.isUpdatingRect,S=q.oid,V=q.canUndoRedo,U=q.outputFName;q=q.taskId;F!==G&&x(F,!0,e,B,!1);var a="results"+F+".xml";e=O?"transformTextBox":"transformObject";n(J,a,F);B=d(U);a=d(a);postMessage({cmd:e,
pageNumber:F,pdfBuffer:B,resultsXML:a,id:L,isUpdatingRect:N,isText:O,commandXML:J,subCmd:A,canUndoRedo:V,oid:S,outputFName:U,taskId:q},[B,a])}l.d(m,"extractFileNameFromCommand",function(){return g});var E=l(7),C=l(1),y="undefined"===typeof window?self:window;y.Core=y.Core||{};var K=!0,Q=!1,R=null,G=-1,M=!1,T,z={noInitialRun:!0,onRuntimeInitialized:function(){Q=!0},fetchSelf:function(){Object(E.a)("InfixServer",{"Wasm.wasm":1E8,"Wasm.js.mem":1E5,".js.mem":5E6,".mem":3E6,disableObjectURLBlobs:M},!!navigator.userAgent.match(/Edge/i))},
locateFile:function(q){return q},getPreloadedPackage:function(q,e){"InfixServerWasm.br.mem"==q&&(q="InfixServerWasm.mem");return Object(C.b)("".concat(R||"").concat(q),e,!1,!0).buffer}};self.Module=z;self.basePath="../external/";onmessage=function(q){var e=q.data;switch(e.cmd){case "isReady":R=e.resourcePath;z.fetchSelf();h(e.taskId);break;case "initialiseInfixServer":var B=e.l,A=e.taskId;z.callMain([""]);z.ccall("wasmInitInfixServer","number",["string","string","string"],["infixcore.cfg",B,"results.xml"]);
var F=d("results.xml");postMessage({cmd:"initialiseInfixServer",resultsXML:F,taskId:A},[F]);break;case "disableObjectURLBlobs":M=e.disableURLBlobs;break;case "loadAvailableFonts":var J=e.commandXML,L=e.taskId;T=e.webFontURL;n(J,"results0.xml",0);var O=d("results0.xml");postMessage({cmd:"loadAvailableFonts",resultsXML:O,taskId:L},[O]);break;case "exportFile":x(e.pageNumber,e.performExport,e.pdfFile,e.tableData,!0,e.taskId);break;case "applyTransformMatrix":var N=e.oid,S=e.pageNumber,V=e.taskId;n(e.commandXML,
"results"+S+".xml",S);postMessage({cmd:"applyTransformMatrix",pageNumber:S,id:N,taskId:V});break;case "transformObject":w(e);break;case "deleteObject":var U=e.pdfFile,a=e.pageNumber,b=e.objectID,c=e.tableData,f=e.isUndoRedo,p=e.isPageDeleted,D=e.taskId;a!==G&&x(a,!0,U,c,!1);var H="outputFile"+a+".pdf",I="results"+a+".xml";var P='<Commands><Command Name="DeleteObject">'+"<OID>".concat(b,"</OID></Command>");P+='<Command Name="SavePDF"><File>'.concat(H,"</File>");n(P+"</Command></Commands>",I,a);var W=
d(H),Z=d(I);postMessage({cmd:"deleteObject",pageNumber:a,pdfBuffer:W,resultsXML:Z,oid:b,isUndoRedo:f,isPageDeleted:p,taskId:D},[W,Z]);break;case "insertTextBox":u(e.commandXML,e.commandFile,e.resultsFile,!0);break;case "insertNewTextBox":var Bb=e.pdfFile,X=e.pageNumber,Cb=e.topVal,Db=e.leftVal,Eb=e.bottomVal,Fb=e.rightVal,Gb=e.font,Hb=e.fontSize,Ib=e.importData,Jb=e.content,Kb=e.canUndoRedo,Lb=e.taskId,Mb=(new TextEncoder).encode("").buffer;X!=G&&x(X,!0,Bb,Mb,!1);var qa="results"+X+".xml",ra="exported"+
X+".xml",sa="outputFile"+X+".pdf";var Y='<Commands><Command Name="Insert Text Box">'+"<Rect><Top>".concat(Cb,"</Top><Left>").concat(Db,"</Left>");Y+="<Bottom>".concat(Eb,"</Bottom><Right>").concat(Fb,"</Right></Rect>");Y+="<Size>".concat(Hb,"</Size><FontName>").concat(Gb,"</FontName>");var ta="editText"+X+".xml";FS.writeFile(ta,Ib);Y+="<File>".concat(ta,"</File><TransXML>coreTransXML.cfg</TransXML>");Y+="<ExportFile>".concat(ra,"</ExportFile><TransXML>coreTransXML.cfg</TransXML>");Y=Y+'<StartPage>1</StartPage><EndPage>LastPage</EndPage><AutoSubstitute/><AutoDeleteParas/><Fitting><Shrink><FontSize Min="0.65">true</FontSize><Leading>False</Leading></Shrink><Stretch><FontSize>False</FontSize><Leading>False</Leading></Stretch></Fitting><ResetLetterSpacing/><IgnoreFlightCheck/><MissingFont>Noto Sans Regular</MissingFont><SubstituteAllChars/><TargetLang>en</TargetLang></Command>'+
'<Command Name="SavePDF"><File>'.concat(sa,"</File></Command></Commands>");n(Y,qa,X);var ua=d(sa),va=d(qa),wa=d(ra);postMessage({cmd:"insertNewTextBox",pageNumber:X,pdfBuffer:ua,exportXML:wa,resultsXML:va,contentHTML:Jb,commandXML:Y,canUndoRedo:Kb,taskId:Lb},[ua,wa,va]);break;case "AlignContentBox":var Nb=e.pdfFile,fa=e.pageNumber,Ob=e.galleyId,Pb=e.tableData,ka=e.commandXML,Qb=e.taskId;fa!=G&&x(fa,!0,Nb,Pb,!1);var Rb=g(ka);n(ka,"results.xml",fa);var xa=d("results.xml"),ya=d(Rb);postMessage({cmd:"updateContentBox",
subCmd:"Set Para Attribs",pageNumber:fa,pdfBuffer:ya,resultsXML:xa,galleyId:Ob,commandXML:ka,taskId:Qb},[ya,xa]);break;case "RenderContentBox":var Sb=e.pdfFile,ha=e.pageNumber,Tb=e.galleyId,Ub=e.tableData,za=e.commandXML,Vb=e.taskId;ha!=G&&x(ha,!0,Sb,Ub,!1);var Wb=g(za);n(za,"results.xml",ha);var Aa=d(Wb),Ba=d("results.xml");postMessage({cmd:"renderContentBox",pageNumber:ha,pdfBuffer:Aa,resultsXML:Ba,galleyId:Tb,taskId:Vb},[Aa,Ba]);break;case "AlignParagraph":var Xb=e.pdfFile,ia=e.pageNumber,Yb=e.galleyId,
Zb=e.tableData,Ca=e.commandXML,$b=e.taskId;ia!=G&&x(ia,!0,Xb,Zb,!1);n(Ca,"results.xml",ia);var Da=d("results.xml");postMessage({cmd:"editText",subCmd:"Set Para Attribs",galleyId:Yb,commandXML:Ca,pageNumber:ia,taskId:$b,resultsXML:Da},[Da]);break;case "DecorateContentBox":var la=e.commandXML,ac=e.pdfFile,ja=e.pageNumber,Ea=e.galleyId,bc=e.tableData,cc=e.taskId,dc=e.subCmd;ja!=G&&x(ja,!0,ac,bc,!1);var ec=g(la);n(la,"results.xml",ja);var Fa=d("results.xml"),Ga=d(ec);postMessage({cmd:"updateContentBox",
pageNumber:ja,pdfBuffer:Ga,commandXML:la,resultsXML:Fa,subCmd:dc,id:Ea,galleyId:Ea,taskId:cc},[Ga,Fa]);break;case "insertImage":var fc=e.pdfFile,aa=e.pageNumber,ma=e.newImage,gc=e.canUndoRedo,hc=e.taskId,Ha=e.commandXML,ic=e.imageFileName,jc=e.outputFileName,kc=(new TextEncoder).encode("").buffer;aa!=G&&x(aa,!0,fc,kc,!1);var Ia="results"+aa+".xml";FS.writeFile(ic,k.from(ma));n(Ha,Ia,aa);var Ja=d(jc),Ka=d(Ia);postMessage({cmd:"insertImage",pageNumber:aa,pdfBuffer:Ja,resultsXML:Ka,commandXML:Ha,canUndoRedo:gc,
newImage:ma,taskId:hc},[Ja,Ka,ma]);break;case "runCommand":var lc=e.subCmd,La=e.resultsFile;n(e.commandXML,La,0,e.commandFile);var Ma=d(La);postMessage({cmd:"runCommand",subCmd:lc,resultsXML:Ma},[Ma]);break;case "renderEditGalley":var Na=e.resultsFile;n(e.commandXML,Na,0,e.commandFile);var Oa=d(Na),Pa=d(e.imageFName);postMessage({cmd:"renderEditGalley",resultsXML:Oa,imageData:Pa,galleyId:e.galleyId,taskId:e.taskId},[Oa,Pa]);break;case "renderFullPage":var Qa=e.resultsFile;n(e.commandXML,Qa,0,e.commandFile);
var Ra=d(Qa),Sa=d(e.imageFName);postMessage({cmd:"renderFullPage",resultsXML:Ra,imageData:Sa,outputWidth:e.width,outputHeight:e.height},[Ra,Sa]);break;case "textAttributes":var mc=e.id,nc=e.numChars,Ta=e.resultsFile,oc=e.taskId;n(e.commandXML,Ta,0,e.commandFile);var Ua=d(Ta);postMessage({cmd:"textAttributes",id:mc,numChars:nc,resultsXML:Ua,taskId:oc},[Ua]);break;case "editText":t(e,!0);break;case "editObject":var pc=e.subCmd,qc=e.oid,Va=e.resultsFile,Wa=e.commandXML;n(Wa,Va,0,e.commandFile);var Xa=
d(Va);postMessage({cmd:"editObject",subCmd:pc,oid:qc,commandXML:Wa,resultsXML:Xa},[Xa]);break;case "performUndoRedo":switch(e.subCmd){case "editText":t(e,!1);break;case "transformObject":e.subCmd="performUndoRedo";w(e);break;case "insertTextBoxRedo":var Ya=e.commandXML,ba=e.pageNumber,rc=e.taskId,Za="results"+ba+".xml",sc="exported"+ba+".xml",tc="outputFile"+ba+".pdf";n(Ya,Za,ba);var $a=d(tc),ab=d(Za),bb=d(sc);postMessage({cmd:"insertNewTextBox",subCmd:"performUndoRedo",pageNumber:ba,pdfBuffer:$a,
exportXML:bb,resultsXML:ab,commandXML:Ya,taskId:rc},[$a,bb,ab]);break;case "insertImageRedo":var cb=e.commandXML,ca=e.pageNumber,db=e.newImage,uc="outputFile"+ca+".pdf",eb="results"+ca+".xml",fb="imageFile"+ca+".jpg";FS.writeFile(fb,k.from(db));n(cb,eb,ca);var gb=d(uc),hb=d(eb);FS.unlink(fb);postMessage({cmd:"insertImage",pageNumber:ca,pdfBuffer:gb,resultsXML:hb,commandXML:cb,newImage:db},[gb,hb])}break;case "insertTextBoxRedo":u(e.commandXML,e.commandFile,e.resultsFile,!1);break;case "copyText":v(e,
"copyText");break;case "getUpdatedText":v(e,"getUpdatedText");break;case "dumpTextBox":var vc=e.galleyId,ib=e.resultsFile,wc=e.taskId;n(e.commandXML,ib,0,e.commandFile);var jb=d(ib);postMessage({cmd:"dumpTextBox",galleyId:vc,resultsXML:jb,taskId:wc},[jb]);break;case "transformTextBox":t(e,!1);break;case "savePDF":var kb=e.resultsFile,xc=e.pdfFileName,yc=new Uint8Array(e.pdfFile);FS.writeFile(e.pdfFileName,yc);n(e.commandXML,kb,0,e.commandFile);var lb=d(xc),mb=d(kb);postMessage({cmd:"savePDF",pdfBuffer:lb,
resultsXML:mb},[lb,mb]);break;case "loadPDF":var nb=e.resultsFile,zc=new Uint8Array(e.pdfFile);FS.writeFile(e.pdfFileName,zc);n(e.commandXML,nb,0,e.commandFile);var ob=d(nb);postMessage({cmd:"loadPDF",resultsXML:ob},[ob]);break;case "loadHyperlinkURL":var Ac=e.id,na=e.resultsFile,Bc=e.taskId;n(e.commandXML,na,0,e.commandFile);var pb=FS.readFile(na).buffer;FS.unlink(na);postMessage({id:Ac,cmd:"loadHyperlinkURL",resultsXML:pb,taskId:Bc},[pb]);break;case "setTypographyContentBox":var Cc=e.pdfFile,da=
e.pageNumber,qb=e.galleyId,Dc=e.subCmd,Ec=e.tableData,Fc=e.taskId,oa=e.commandXML;da!=G&&x(da,!0,Cc,Ec,!1);var rb="results"+da+".xml";n(oa,rb,da);var Gc=g(oa),sb=d(Gc),tb=d(rb);postMessage({cmd:"setTypographyContentBox",subCmd:Dc,pageNumber:da,pdfBuffer:sb,commandXML:oa,resultsXML:tb,id:qb,galleyId:qb,taskId:Fc},[sb,tb]);break;case "updateDocumentContent":var ea=e.pageNumber,Hc=e.galleyId,Ic=e.outputFileName,ub=e.commandXML,Jc=e.isSearchReplace,Kc=e.callbackMapId,Lc=e.pdfPage,Mc=e.tableArray,Nc=e.taskId;
ea!=G&&x(ea,!0,Lc,Mc,!1);var vb="results"+ea+".xml";n(ub,vb,ea);var wb=d(Ic),xb=d(vb);postMessage({cmd:"updateContentBox",pageNumber:ea,pdfBuffer:wb,commandXML:ub,resultsXML:xb,galleyId:Hc,callbackMapId:Kc,isSearchReplace:Jc,taskId:Nc},[wb,xb]);break;case "getInfixVersion":var Oc=e.taskId,yb=e.commandXML;n(yb,"results1.xml",1);var zb=d("results1.xml");postMessage({cmd:"getInfixVersion",commandXML:yb,resultsXML:zb,taskId:Oc},[zb]);break;case "reloadPage":var pa=e.pageNumber,Pc=new Uint8Array(e.pdfFile),
Ab="inputFile"+pa+".pdf";FS.writeFile(Ab,Pc);var Qc="objects"+pa+".xml",Rc="results"+pa+".xml",Sc='\n  <Commands>\n    <Command Name="LoadPDF"><File>'.concat(Ab,'</File></Command>\n    <Command Name="Page BBox"><StartPage>1</StartPage><EndPage>1</EndPage></Command>\n    <Command Name="Edit Page">\n    <Output>').concat(Qc,"</Output><ImagesOnly/>").concat(K?"<ForceReflow/>":"","</Command>\n  </Commands>");n(Sc,Rc,1);break;case "setTextReflow":K=e.textReflow;postMessage({taskId:e.taskId});break;case "getTextReflow":postMessage({taskId:e.taskId,
textReflow:K})}}}).call(this,l(11).Buffer)},function(r,m,l){(function(k){function g(){try{var a=new Uint8Array(1);a.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}};return 42===a.foo()&&"function"===typeof a.subarray&&0===a.subarray(1,1).byteLength}catch(b){return!1}}function n(a,b){if((d.TYPED_ARRAY_SUPPORT?2147483647:1073741823)<b)throw new RangeError("Invalid typed array length");d.TYPED_ARRAY_SUPPORT?(a=new Uint8Array(b),a.__proto__=d.prototype):(null===a&&(a=new d(b)),a.length=
b);return a}function d(a,b,c){if(!(d.TYPED_ARRAY_SUPPORT||this instanceof d))return new d(a,b,c);if("number"===typeof a){if("string"===typeof b)throw Error("If encoding is specified then the first argument must be a string");return u(this,a)}return h(this,a,b,c)}function h(a,b,c,f){if("number"===typeof b)throw new TypeError('"value" argument must not be a number');if("undefined"!==typeof ArrayBuffer&&b instanceof ArrayBuffer){b.byteLength;if(0>c||b.byteLength<c)throw new RangeError("'offset' is out of bounds");
if(b.byteLength<c+(f||0))throw new RangeError("'length' is out of bounds");b=void 0===c&&void 0===f?new Uint8Array(b):void 0===f?new Uint8Array(b,c):new Uint8Array(b,c,f);d.TYPED_ARRAY_SUPPORT?(a=b,a.__proto__=d.prototype):a=v(a,b);return a}if("string"===typeof b){f=a;a=c;if("string"!==typeof a||""===a)a="utf8";if(!d.isEncoding(a))throw new TypeError('"encoding" must be a valid string encoding');c=E(b,a)|0;f=n(f,c);b=f.write(b,a);b!==c&&(f=f.slice(0,b));return f}return x(a,b)}function t(a){if("number"!==
typeof a)throw new TypeError('"size" argument must be a number');if(0>a)throw new RangeError('"size" argument must not be negative');}function u(a,b){t(b);a=n(a,0>b?0:w(b)|0);if(!d.TYPED_ARRAY_SUPPORT)for(var c=0;c<b;++c)a[c]=0;return a}function v(a,b){var c=0>b.length?0:w(b.length)|0;a=n(a,c);for(var f=0;f<c;f+=1)a[f]=b[f]&255;return a}function x(a,b){if(d.isBuffer(b)){var c=w(b.length)|0;a=n(a,c);if(0===a.length)return a;b.copy(a,0,0,c);return a}if(b){if("undefined"!==typeof ArrayBuffer&&b.buffer instanceof
ArrayBuffer||"length"in b)return(c="number"!==typeof b.length)||(c=b.length,c=c!==c),c?n(a,0):v(a,b);if("Buffer"===b.type&&S(b.data))return v(a,b.data)}throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.");}function w(a){if(a>=(d.TYPED_ARRAY_SUPPORT?2147483647:1073741823))throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+(d.TYPED_ARRAY_SUPPORT?2147483647:1073741823).toString(16)+" bytes");return a|0}function E(a,b){if(d.isBuffer(a))return a.length;
if("undefined"!==typeof ArrayBuffer&&"function"===typeof ArrayBuffer.isView&&(ArrayBuffer.isView(a)||a instanceof ArrayBuffer))return a.byteLength;"string"!==typeof a&&(a=""+a);var c=a.length;if(0===c)return 0;for(var f=!1;;)switch(b){case "ascii":case "latin1":case "binary":return c;case "utf8":case "utf-8":case void 0:return A(a).length;case "ucs2":case "ucs-2":case "utf16le":case "utf-16le":return 2*c;case "hex":return c>>>1;case "base64":return J(a).length;default:if(f)return A(a).length;b=(""+
b).toLowerCase();f=!0}}function C(a,b,c){var f=!1;if(void 0===b||0>b)b=0;if(b>this.length)return"";if(void 0===c||c>this.length)c=this.length;if(0>=c)return"";c>>>=0;b>>>=0;if(c<=b)return"";for(a||(a="utf8");;)switch(a){case "hex":a=b;b=c;c=this.length;if(!a||0>a)a=0;if(!b||0>b||b>c)b=c;f="";for(c=a;c<b;++c)a=f,f=this[c],f=16>f?"0"+f.toString(16):f.toString(16),f=a+f;return f;case "utf8":case "utf-8":return R(this,b,c);case "ascii":a="";for(c=Math.min(this.length,c);b<c;++b)a+=String.fromCharCode(this[b]&
127);return a;case "latin1":case "binary":a="";for(c=Math.min(this.length,c);b<c;++b)a+=String.fromCharCode(this[b]);return a;case "base64":return b=0===b&&c===this.length?O.fromByteArray(this):O.fromByteArray(this.slice(b,c)),b;case "ucs2":case "ucs-2":case "utf16le":case "utf-16le":b=this.slice(b,c);c="";for(a=0;a<b.length;a+=2)c+=String.fromCharCode(b[a]+256*b[a+1]);return c;default:if(f)throw new TypeError("Unknown encoding: "+a);a=(a+"").toLowerCase();f=!0}}function y(a,b,c){var f=a[b];a[b]=
a[c];a[c]=f}function K(a,b,c,f,p){if(0===a.length)return-1;"string"===typeof c?(f=c,c=0):2147483647<c?c=2147483647:-2147483648>c&&(c=-2147483648);c=+c;isNaN(c)&&(c=p?0:a.length-1);0>c&&(c=a.length+c);if(c>=a.length){if(p)return-1;c=a.length-1}else if(0>c)if(p)c=0;else return-1;"string"===typeof b&&(b=d.from(b,f));if(d.isBuffer(b))return 0===b.length?-1:Q(a,b,c,f,p);if("number"===typeof b)return b&=255,d.TYPED_ARRAY_SUPPORT&&"function"===typeof Uint8Array.prototype.indexOf?p?Uint8Array.prototype.indexOf.call(a,
b,c):Uint8Array.prototype.lastIndexOf.call(a,b,c):Q(a,[b],c,f,p);throw new TypeError("val must be string, number or Buffer");}function Q(a,b,c,f,p){function D(W,Z){return 1===H?W[Z]:W.readUInt16BE(Z*H)}var H=1,I=a.length,P=b.length;if(void 0!==f&&(f=String(f).toLowerCase(),"ucs2"===f||"ucs-2"===f||"utf16le"===f||"utf-16le"===f)){if(2>a.length||2>b.length)return-1;H=2;I/=2;P/=2;c/=2}if(p)for(f=-1;c<I;c++)if(D(a,c)===D(b,-1===f?0:c-f)){if(-1===f&&(f=c),c-f+1===P)return f*H}else-1!==f&&(c-=c-f),f=-1;
else for(c+P>I&&(c=I-P);0<=c;c--){I=!0;for(f=0;f<P;f++)if(D(a,c+f)!==D(b,f)){I=!1;break}if(I)return c}return-1}function R(a,b,c){c=Math.min(a.length,c);for(var f=[];b<c;){var p=a[b],D=null,H=239<p?4:223<p?3:191<p?2:1;if(b+H<=c)switch(H){case 1:128>p&&(D=p);break;case 2:var I=a[b+1];128===(I&192)&&(p=(p&31)<<6|I&63,127<p&&(D=p));break;case 3:I=a[b+1];var P=a[b+2];128===(I&192)&&128===(P&192)&&(p=(p&15)<<12|(I&63)<<6|P&63,2047<p&&(55296>p||57343<p)&&(D=p));break;case 4:I=a[b+1];P=a[b+2];var W=a[b+3];
128===(I&192)&&128===(P&192)&&128===(W&192)&&(p=(p&15)<<18|(I&63)<<12|(P&63)<<6|W&63,65535<p&&1114112>p&&(D=p))}null===D?(D=65533,H=1):65535<D&&(D-=65536,f.push(D>>>10&1023|55296),D=56320|D&1023);f.push(D);b+=H}a=f.length;if(a<=V)f=String.fromCharCode.apply(String,f);else{c="";for(b=0;b<a;)c+=String.fromCharCode.apply(String,f.slice(b,b+=V));f=c}return f}function G(a,b,c){if(0!==a%1||0>a)throw new RangeError("offset is not uint");if(a+b>c)throw new RangeError("Trying to access beyond buffer length");
}function M(a,b,c,f,p,D){if(!d.isBuffer(a))throw new TypeError('"buffer" argument must be a Buffer instance');if(b>p||b<D)throw new RangeError('"value" argument is out of bounds');if(c+f>a.length)throw new RangeError("Index out of range");}function T(a,b,c,f){0>b&&(b=65535+b+1);for(var p=0,D=Math.min(a.length-c,2);p<D;++p)a[c+p]=(b&255<<8*(f?p:1-p))>>>8*(f?p:1-p)}function z(a,b,c,f){0>b&&(b=4294967295+b+1);for(var p=0,D=Math.min(a.length-c,4);p<D;++p)a[c+p]=b>>>8*(f?p:3-p)&255}function q(a,b,c,f,
p,D){if(c+f>a.length)throw new RangeError("Index out of range");if(0>c)throw new RangeError("Index out of range");}function e(a,b,c,f,p){p||q(a,b,c,4,3.4028234663852886E38,-3.4028234663852886E38);N.write(a,b,c,f,23,4);return c+4}function B(a,b,c,f,p){p||q(a,b,c,8,1.7976931348623157E308,-1.7976931348623157E308);N.write(a,b,c,f,52,8);return c+8}function A(a,b){b=b||Infinity;for(var c,f=a.length,p=null,D=[],H=0;H<f;++H){c=a.charCodeAt(H);if(55295<c&&57344>c){if(!p){if(56319<c){-1<(b-=3)&&D.push(239,
191,189);continue}else if(H+1===f){-1<(b-=3)&&D.push(239,191,189);continue}p=c;continue}if(56320>c){-1<(b-=3)&&D.push(239,191,189);p=c;continue}c=(p-55296<<10|c-56320)+65536}else p&&-1<(b-=3)&&D.push(239,191,189);p=null;if(128>c){if(0>--b)break;D.push(c)}else if(2048>c){if(0>(b-=2))break;D.push(c>>6|192,c&63|128)}else if(65536>c){if(0>(b-=3))break;D.push(c>>12|224,c>>6&63|128,c&63|128)}else if(1114112>c){if(0>(b-=4))break;D.push(c>>18|240,c>>12&63|128,c>>6&63|128,c&63|128)}else throw Error("Invalid code point");
}return D}function F(a){for(var b=[],c=0;c<a.length;++c)b.push(a.charCodeAt(c)&255);return b}function J(a){var b=O,c=b.toByteArray;a=(a.trim?a.trim():a.replace(/^\s+|\s+$/g,"")).replace(U,"");if(2>a.length)a="";else for(;0!==a.length%4;)a+="=";return c.call(b,a)}function L(a,b,c,f){for(var p=0;p<f&&!(p+c>=b.length||p>=a.length);++p)b[p+c]=a[p];return p}var O=l(13),N=l(14),S=l(15);m.Buffer=d;m.SlowBuffer=function(a){+a!=a&&(a=0);return d.alloc(+a)};m.INSPECT_MAX_BYTES=50;d.TYPED_ARRAY_SUPPORT=void 0!==
k.TYPED_ARRAY_SUPPORT?k.TYPED_ARRAY_SUPPORT:g();m.kMaxLength=d.TYPED_ARRAY_SUPPORT?2147483647:1073741823;d.poolSize=8192;d._augment=function(a){a.__proto__=d.prototype;return a};d.from=function(a,b,c){return h(null,a,b,c)};d.TYPED_ARRAY_SUPPORT&&(d.prototype.__proto__=Uint8Array.prototype,d.__proto__=Uint8Array,"undefined"!==typeof Symbol&&Symbol.species&&d[Symbol.species]===d&&Object.defineProperty(d,Symbol.species,{value:null,configurable:!0}));d.alloc=function(a,b,c){t(a);a=0>=a?n(null,a):void 0!==
b?"string"===typeof c?n(null,a).fill(b,c):n(null,a).fill(b):n(null,a);return a};d.allocUnsafe=function(a){return u(null,a)};d.allocUnsafeSlow=function(a){return u(null,a)};d.isBuffer=function(a){return!(null==a||!a._isBuffer)};d.compare=function(a,b){if(!d.isBuffer(a)||!d.isBuffer(b))throw new TypeError("Arguments must be Buffers");if(a===b)return 0;for(var c=a.length,f=b.length,p=0,D=Math.min(c,f);p<D;++p)if(a[p]!==b[p]){c=a[p];f=b[p];break}return c<f?-1:f<c?1:0};d.isEncoding=function(a){switch(String(a).toLowerCase()){case "hex":case "utf8":case "utf-8":case "ascii":case "latin1":case "binary":case "base64":case "ucs2":case "ucs-2":case "utf16le":case "utf-16le":return!0;
default:return!1}};d.concat=function(a,b){if(!S(a))throw new TypeError('"list" argument must be an Array of Buffers');if(0===a.length)return d.alloc(0);var c;if(void 0===b)for(c=b=0;c<a.length;++c)b+=a[c].length;b=d.allocUnsafe(b);var f=0;for(c=0;c<a.length;++c){var p=a[c];if(!d.isBuffer(p))throw new TypeError('"list" argument must be an Array of Buffers');p.copy(b,f);f+=p.length}return b};d.byteLength=E;d.prototype._isBuffer=!0;d.prototype.swap16=function(){var a=this.length;if(0!==a%2)throw new RangeError("Buffer size must be a multiple of 16-bits");
for(var b=0;b<a;b+=2)y(this,b,b+1);return this};d.prototype.swap32=function(){var a=this.length;if(0!==a%4)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var b=0;b<a;b+=4)y(this,b,b+3),y(this,b+1,b+2);return this};d.prototype.swap64=function(){var a=this.length;if(0!==a%8)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var b=0;b<a;b+=8)y(this,b,b+7),y(this,b+1,b+6),y(this,b+2,b+5),y(this,b+3,b+4);return this};d.prototype.toString=function(){var a=this.length|
0;return 0===a?"":0===arguments.length?R(this,0,a):C.apply(this,arguments)};d.prototype.equals=function(a){if(!d.isBuffer(a))throw new TypeError("Argument must be a Buffer");return this===a?!0:0===d.compare(this,a)};d.prototype.inspect=function(){var a="",b=m.INSPECT_MAX_BYTES;0<this.length&&(a=this.toString("hex",0,b).match(/.{2}/g).join(" "),this.length>b&&(a+=" ... "));return"<Buffer "+a+">"};d.prototype.compare=function(a,b,c,f,p){if(!d.isBuffer(a))throw new TypeError("Argument must be a Buffer");
void 0===b&&(b=0);void 0===c&&(c=a?a.length:0);void 0===f&&(f=0);void 0===p&&(p=this.length);if(0>b||c>a.length||0>f||p>this.length)throw new RangeError("out of range index");if(f>=p&&b>=c)return 0;if(f>=p)return-1;if(b>=c)return 1;b>>>=0;c>>>=0;f>>>=0;p>>>=0;if(this===a)return 0;var D=p-f,H=c-b,I=Math.min(D,H);f=this.slice(f,p);a=a.slice(b,c);for(b=0;b<I;++b)if(f[b]!==a[b]){D=f[b];H=a[b];break}return D<H?-1:H<D?1:0};d.prototype.includes=function(a,b,c){return-1!==this.indexOf(a,b,c)};d.prototype.indexOf=
function(a,b,c){return K(this,a,b,c,!0)};d.prototype.lastIndexOf=function(a,b,c){return K(this,a,b,c,!1)};d.prototype.write=function(a,b,c,f){if(void 0===b)f="utf8",c=this.length,b=0;else if(void 0===c&&"string"===typeof b)f=b,c=this.length,b=0;else if(isFinite(b))b|=0,isFinite(c)?(c|=0,void 0===f&&(f="utf8")):(f=c,c=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var p=this.length-b;if(void 0===c||c>p)c=p;if(0<a.length&&(0>c||0>b)||b>this.length)throw new RangeError("Attempt to write outside buffer bounds");
f||(f="utf8");for(p=!1;;)switch(f){case "hex":a:{b=Number(b)||0;f=this.length-b;c?(c=Number(c),c>f&&(c=f)):c=f;f=a.length;if(0!==f%2)throw new TypeError("Invalid hex string");c>f/2&&(c=f/2);for(f=0;f<c;++f){p=parseInt(a.substr(2*f,2),16);if(isNaN(p)){a=f;break a}this[b+f]=p}a=f}return a;case "utf8":case "utf-8":return L(A(a,this.length-b),this,b,c);case "ascii":return L(F(a),this,b,c);case "latin1":case "binary":return L(F(a),this,b,c);case "base64":return L(J(a),this,b,c);case "ucs2":case "ucs-2":case "utf16le":case "utf-16le":f=
a;p=this.length-b;for(var D=[],H=0;H<f.length&&!(0>(p-=2));++H){var I=f.charCodeAt(H);a=I>>8;I%=256;D.push(I);D.push(a)}return L(D,this,b,c);default:if(p)throw new TypeError("Unknown encoding: "+f);f=(""+f).toLowerCase();p=!0}};d.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var V=4096;d.prototype.slice=function(a,b){var c=this.length;a=~~a;b=void 0===b?c:~~b;0>a?(a+=c,0>a&&(a=0)):a>c&&(a=c);0>b?(b+=c,0>b&&(b=0)):b>c&&(b=c);b<a&&(b=a);if(d.TYPED_ARRAY_SUPPORT)b=
this.subarray(a,b),b.__proto__=d.prototype;else{c=b-a;b=new d(c,void 0);for(var f=0;f<c;++f)b[f]=this[f+a]}return b};d.prototype.readUIntLE=function(a,b,c){a|=0;b|=0;c||G(a,b,this.length);c=this[a];for(var f=1,p=0;++p<b&&(f*=256);)c+=this[a+p]*f;return c};d.prototype.readUIntBE=function(a,b,c){a|=0;b|=0;c||G(a,b,this.length);c=this[a+--b];for(var f=1;0<b&&(f*=256);)c+=this[a+--b]*f;return c};d.prototype.readUInt8=function(a,b){b||G(a,1,this.length);return this[a]};d.prototype.readUInt16LE=function(a,
b){b||G(a,2,this.length);return this[a]|this[a+1]<<8};d.prototype.readUInt16BE=function(a,b){b||G(a,2,this.length);return this[a]<<8|this[a+1]};d.prototype.readUInt32LE=function(a,b){b||G(a,4,this.length);return(this[a]|this[a+1]<<8|this[a+2]<<16)+16777216*this[a+3]};d.prototype.readUInt32BE=function(a,b){b||G(a,4,this.length);return 16777216*this[a]+(this[a+1]<<16|this[a+2]<<8|this[a+3])};d.prototype.readIntLE=function(a,b,c){a|=0;b|=0;c||G(a,b,this.length);c=this[a];for(var f=1,p=0;++p<b&&(f*=256);)c+=
this[a+p]*f;c>=128*f&&(c-=Math.pow(2,8*b));return c};d.prototype.readIntBE=function(a,b,c){a|=0;b|=0;c||G(a,b,this.length);c=b;for(var f=1,p=this[a+--c];0<c&&(f*=256);)p+=this[a+--c]*f;p>=128*f&&(p-=Math.pow(2,8*b));return p};d.prototype.readInt8=function(a,b){b||G(a,1,this.length);return this[a]&128?-1*(255-this[a]+1):this[a]};d.prototype.readInt16LE=function(a,b){b||G(a,2,this.length);a=this[a]|this[a+1]<<8;return a&32768?a|4294901760:a};d.prototype.readInt16BE=function(a,b){b||G(a,2,this.length);
a=this[a+1]|this[a]<<8;return a&32768?a|4294901760:a};d.prototype.readInt32LE=function(a,b){b||G(a,4,this.length);return this[a]|this[a+1]<<8|this[a+2]<<16|this[a+3]<<24};d.prototype.readInt32BE=function(a,b){b||G(a,4,this.length);return this[a]<<24|this[a+1]<<16|this[a+2]<<8|this[a+3]};d.prototype.readFloatLE=function(a,b){b||G(a,4,this.length);return N.read(this,a,!0,23,4)};d.prototype.readFloatBE=function(a,b){b||G(a,4,this.length);return N.read(this,a,!1,23,4)};d.prototype.readDoubleLE=function(a,
b){b||G(a,8,this.length);return N.read(this,a,!0,52,8)};d.prototype.readDoubleBE=function(a,b){b||G(a,8,this.length);return N.read(this,a,!1,52,8)};d.prototype.writeUIntLE=function(a,b,c,f){a=+a;b|=0;c|=0;f||M(this,a,b,c,Math.pow(2,8*c)-1,0);f=1;var p=0;for(this[b]=a&255;++p<c&&(f*=256);)this[b+p]=a/f&255;return b+c};d.prototype.writeUIntBE=function(a,b,c,f){a=+a;b|=0;c|=0;f||M(this,a,b,c,Math.pow(2,8*c)-1,0);f=c-1;var p=1;for(this[b+f]=a&255;0<=--f&&(p*=256);)this[b+f]=a/p&255;return b+c};d.prototype.writeUInt8=
function(a,b,c){a=+a;b|=0;c||M(this,a,b,1,255,0);d.TYPED_ARRAY_SUPPORT||(a=Math.floor(a));this[b]=a&255;return b+1};d.prototype.writeUInt16LE=function(a,b,c){a=+a;b|=0;c||M(this,a,b,2,65535,0);d.TYPED_ARRAY_SUPPORT?(this[b]=a&255,this[b+1]=a>>>8):T(this,a,b,!0);return b+2};d.prototype.writeUInt16BE=function(a,b,c){a=+a;b|=0;c||M(this,a,b,2,65535,0);d.TYPED_ARRAY_SUPPORT?(this[b]=a>>>8,this[b+1]=a&255):T(this,a,b,!1);return b+2};d.prototype.writeUInt32LE=function(a,b,c){a=+a;b|=0;c||M(this,a,b,4,4294967295,
0);d.TYPED_ARRAY_SUPPORT?(this[b+3]=a>>>24,this[b+2]=a>>>16,this[b+1]=a>>>8,this[b]=a&255):z(this,a,b,!0);return b+4};d.prototype.writeUInt32BE=function(a,b,c){a=+a;b|=0;c||M(this,a,b,4,4294967295,0);d.TYPED_ARRAY_SUPPORT?(this[b]=a>>>24,this[b+1]=a>>>16,this[b+2]=a>>>8,this[b+3]=a&255):z(this,a,b,!1);return b+4};d.prototype.writeIntLE=function(a,b,c,f){a=+a;b|=0;f||(f=Math.pow(2,8*c-1),M(this,a,b,c,f-1,-f));f=0;var p=1,D=0;for(this[b]=a&255;++f<c&&(p*=256);)0>a&&0===D&&0!==this[b+f-1]&&(D=1),this[b+
f]=(a/p>>0)-D&255;return b+c};d.prototype.writeIntBE=function(a,b,c,f){a=+a;b|=0;f||(f=Math.pow(2,8*c-1),M(this,a,b,c,f-1,-f));f=c-1;var p=1,D=0;for(this[b+f]=a&255;0<=--f&&(p*=256);)0>a&&0===D&&0!==this[b+f+1]&&(D=1),this[b+f]=(a/p>>0)-D&255;return b+c};d.prototype.writeInt8=function(a,b,c){a=+a;b|=0;c||M(this,a,b,1,127,-128);d.TYPED_ARRAY_SUPPORT||(a=Math.floor(a));0>a&&(a=255+a+1);this[b]=a&255;return b+1};d.prototype.writeInt16LE=function(a,b,c){a=+a;b|=0;c||M(this,a,b,2,32767,-32768);d.TYPED_ARRAY_SUPPORT?
(this[b]=a&255,this[b+1]=a>>>8):T(this,a,b,!0);return b+2};d.prototype.writeInt16BE=function(a,b,c){a=+a;b|=0;c||M(this,a,b,2,32767,-32768);d.TYPED_ARRAY_SUPPORT?(this[b]=a>>>8,this[b+1]=a&255):T(this,a,b,!1);return b+2};d.prototype.writeInt32LE=function(a,b,c){a=+a;b|=0;c||M(this,a,b,4,2147483647,-2147483648);d.TYPED_ARRAY_SUPPORT?(this[b]=a&255,this[b+1]=a>>>8,this[b+2]=a>>>16,this[b+3]=a>>>24):z(this,a,b,!0);return b+4};d.prototype.writeInt32BE=function(a,b,c){a=+a;b|=0;c||M(this,a,b,4,2147483647,
-2147483648);0>a&&(a=4294967295+a+1);d.TYPED_ARRAY_SUPPORT?(this[b]=a>>>24,this[b+1]=a>>>16,this[b+2]=a>>>8,this[b+3]=a&255):z(this,a,b,!1);return b+4};d.prototype.writeFloatLE=function(a,b,c){return e(this,a,b,!0,c)};d.prototype.writeFloatBE=function(a,b,c){return e(this,a,b,!1,c)};d.prototype.writeDoubleLE=function(a,b,c){return B(this,a,b,!0,c)};d.prototype.writeDoubleBE=function(a,b,c){return B(this,a,b,!1,c)};d.prototype.copy=function(a,b,c,f){c||(c=0);f||0===f||(f=this.length);b>=a.length&&
(b=a.length);b||(b=0);0<f&&f<c&&(f=c);if(f===c||0===a.length||0===this.length)return 0;if(0>b)throw new RangeError("targetStart out of bounds");if(0>c||c>=this.length)throw new RangeError("sourceStart out of bounds");if(0>f)throw new RangeError("sourceEnd out of bounds");f>this.length&&(f=this.length);a.length-b<f-c&&(f=a.length-b+c);var p=f-c;if(this===a&&c<b&&b<f)for(f=p-1;0<=f;--f)a[f+b]=this[f+c];else if(1E3>p||!d.TYPED_ARRAY_SUPPORT)for(f=0;f<p;++f)a[f+b]=this[f+c];else Uint8Array.prototype.set.call(a,
this.subarray(c,c+p),b);return p};d.prototype.fill=function(a,b,c,f){if("string"===typeof a){"string"===typeof b?(f=b,b=0,c=this.length):"string"===typeof c&&(f=c,c=this.length);if(1===a.length){var p=a.charCodeAt(0);256>p&&(a=p)}if(void 0!==f&&"string"!==typeof f)throw new TypeError("encoding must be a string");if("string"===typeof f&&!d.isEncoding(f))throw new TypeError("Unknown encoding: "+f);}else"number"===typeof a&&(a&=255);if(0>b||this.length<b||this.length<c)throw new RangeError("Out of range index");
if(c<=b)return this;b>>>=0;c=void 0===c?this.length:c>>>0;a||(a=0);if("number"===typeof a)for(f=b;f<c;++f)this[f]=a;else for(a=d.isBuffer(a)?a:A((new d(a,f)).toString()),p=a.length,f=0;f<c-b;++f)this[f+b]=a[f%p];return this};var U=/[^+\/0-9A-Za-z-_]/g}).call(this,l(12))},function(r,m){m=function(){return this}();try{m=m||(new Function("return this"))()}catch(l){"object"===typeof window&&(m=window)}r.exports=m},function(r,m,l){function k(h){var t=h.length;if(0<t%4)throw Error("Invalid string. Length must be a multiple of 4");
h=h.indexOf("=");-1===h&&(h=t);return[h,h===t?0:4-h%4]}m.byteLength=function(h){h=k(h);var t=h[1];return 3*(h[0]+t)/4-t};m.toByteArray=function(h){var t=k(h);var u=t[0];t=t[1];var v=new d(3*(u+t)/4-t),x=0,w=0<t?u-4:u,E;for(E=0;E<w;E+=4)u=n[h.charCodeAt(E)]<<18|n[h.charCodeAt(E+1)]<<12|n[h.charCodeAt(E+2)]<<6|n[h.charCodeAt(E+3)],v[x++]=u>>16&255,v[x++]=u>>8&255,v[x++]=u&255;2===t&&(u=n[h.charCodeAt(E)]<<2|n[h.charCodeAt(E+1)]>>4,v[x++]=u&255);1===t&&(u=n[h.charCodeAt(E)]<<10|n[h.charCodeAt(E+1)]<<
4|n[h.charCodeAt(E+2)]>>2,v[x++]=u>>8&255,v[x++]=u&255);return v};m.fromByteArray=function(h){for(var t=h.length,u=t%3,v=[],x=0,w=t-u;x<w;x+=16383){for(var E=v,C=E.push,y,K=h,Q=x+16383>w?w:x+16383,R=[],G=x;G<Q;G+=3)y=(K[G]<<16&16711680)+(K[G+1]<<8&65280)+(K[G+2]&255),R.push(g[y>>18&63]+g[y>>12&63]+g[y>>6&63]+g[y&63]);y=R.join("");C.call(E,y)}1===u?(h=h[t-1],v.push(g[h>>2]+g[h<<4&63]+"==")):2===u&&(h=(h[t-2]<<8)+h[t-1],v.push(g[h>>10]+g[h>>4&63]+g[h<<2&63]+"="));return v.join("")};var g=[],n=[],d=
"undefined"!==typeof Uint8Array?Uint8Array:Array;for(r=0;64>r;++r)g[r]="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"[r],n["ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charCodeAt(r)]=r;n[45]=62;n[95]=63},function(r,m){m.read=function(l,k,g,n,d){var h=8*d-n-1;var t=(1<<h)-1,u=t>>1,v=-7;d=g?d-1:0;var x=g?-1:1,w=l[k+d];d+=x;g=w&(1<<-v)-1;w>>=-v;for(v+=h;0<v;g=256*g+l[k+d],d+=x,v-=8);h=g&(1<<-v)-1;g>>=-v;for(v+=n;0<v;h=256*h+l[k+d],d+=x,v-=8);if(0===g)g=1-u;else{if(g===
t)return h?NaN:Infinity*(w?-1:1);h+=Math.pow(2,n);g-=u}return(w?-1:1)*h*Math.pow(2,g-n)};m.write=function(l,k,g,n,d,h){var t,u=8*h-d-1,v=(1<<u)-1,x=v>>1,w=23===d?Math.pow(2,-24)-Math.pow(2,-77):0;h=n?0:h-1;var E=n?1:-1,C=0>k||0===k&&0>1/k?1:0;k=Math.abs(k);isNaN(k)||Infinity===k?(k=isNaN(k)?1:0,n=v):(n=Math.floor(Math.log(k)/Math.LN2),1>k*(t=Math.pow(2,-n))&&(n--,t*=2),k=1<=n+x?k+w/t:k+w*Math.pow(2,1-x),2<=k*t&&(n++,t/=2),n+x>=v?(k=0,n=v):1<=n+x?(k=(k*t-1)*Math.pow(2,d),n+=x):(k=k*Math.pow(2,x-1)*
Math.pow(2,d),n=0));for(;8<=d;l[g+h]=k&255,h+=E,k/=256,d-=8);n=n<<d|k;for(u+=d;0<u;l[g+h]=n&255,h+=E,n/=256,u-=8);l[g+h-E]|=128*C}},function(r,m){var l={}.toString;r.exports=Array.isArray||function(k){return"[object Array]"==l.call(k)}}]);}).call(this || window)
