/**
 * Data Visualization Feature - Public API
 * 
 * This module provides a clean, organized API for the data visualization feature.
 * It exports both the new modular implementation and legacy compatibility functions.
 */

// Main visualization class and types
export {
  DataSourceVisualizationNetwork,
  type VisualizationOptions
} from './data-source-visualization-network';

// Legacy API for backward compatibility
export {
  VisualizationLegacyAPI,
  initializeVisualization,
  refreshFilterPanel,
  hideVisualizationFilter
} from './legacy-api';

// Core types and interfaces
export {
  type Node,
  type Link,
  type DataNetwork,
  type Position,
  type VisualizationState,
  type NodeInteractionEvent,
  type VisualizationStateEvent,
  TypeGuards,
  NetworkUtils
} from './models/network-types';

// Configuration types and utilities
export {
  type VisualizationConfig,
  DEFAULT_CONFIG,
  ConfigUtils
} from './models/visualization-configuration';

// Individual managers (for advanced usage)
export { NodeManager } from './modules/node-manager';
export { LinkManager } from './modules/link-manager';
export { InteractionManager } from './modules/interaction-manager';
export { UIControlsManager } from './modules/ui-controls-manager';
export { TooltipManager } from './modules/tooltip-manager';
export { DataProcessor } from './modules/data-processor';
export { AnimationManager } from './modules/animation-manager';

/**
 * Factory functions for common use cases
 */

/**
 * Creates a standard visualization with default settings
 */
export function createVisualization(
  containerId: string,
  data: DataNetwork,
  options?: VisualizationOptions
): DataSourceVisualizationNetwork {
  return DataSourceVisualizationNetwork.create(containerId, data, options);
}

/**
 * Creates a high-performance visualization optimized for large datasets
 */
export function createHighPerformanceVisualization(
  containerId: string,
  data: DataNetwork,
  options?: VisualizationOptions
): DataSourceVisualizationNetwork {
  return DataSourceVisualizationNetwork.createHighPerformance(containerId, data, options);
}

/**
 * Creates an accessible visualization with enhanced accessibility features
 */
export function createAccessibleVisualization(
  containerId: string,
  data: DataNetwork,
  options?: VisualizationOptions
): DataSourceVisualizationNetwork {
  return DataSourceVisualizationNetwork.createAccessible(containerId, data, options);
}

/**
 * Utility functions
 */

/**
 * Validates if data conforms to the DataNetwork interface
 */
export function validateDataNetwork(data: any): data is DataNetwork {
  return TypeGuards.isDataNetwork(data);
}

/**
 * Creates a default configuration for a given container
 */
export function createDefaultConfig(containerId: string): VisualizationConfig {
  const container = document.getElementById(containerId);
  if (!container) {
    throw new Error(`Container with ID '${containerId}' not found`);
  }

  const rect = container.getBoundingClientRect();
  return ConfigUtils.mergeConfig({
    containerId,
    containerWidth: rect.width || 800,
    containerHeight: rect.height || 600
  }, DEFAULT_CONFIG);
}

/**
 * Migration helpers for transitioning from legacy implementation
 */
export const MigrationHelpers = {
  /**
   * Checks if the legacy service is still being used
   */
  isLegacyServiceActive(): boolean {
    return typeof window !== 'undefined' && 
           !!(window as any).DataSourceVisulizationNetwork &&
           typeof (window as any).DataSourceVisulizationNetwork === 'function';
  },

  /**
   * Gets the current active visualization instance
   */
  getCurrentVisualization(): DataSourceVisualizationNetwork | null {
    if (typeof window !== 'undefined') {
      return (window as any).currentVisualizationInstance || null;
    }
    return null;
  },

  /**
   * Safely destroys any existing visualization
   */
  destroyExistingVisualization(): void {
    const current = this.getCurrentVisualization();
    if (current && typeof current.destroy === 'function') {
      current.destroy();
    }
  },

  /**
   * Migrates legacy configuration to new format
   */
  migrateLegacyConfig(legacyConfig: any): Partial<VisualizationConfig> {
    // Convert legacy configuration format to new format
    return {
      // Map legacy properties to new configuration structure
      dataSourceType: legacyConfig.key || 'pages',
      debug: {
        enableLogging: legacyConfig.debug || false,
        showPerformanceMetrics: legacyConfig.showMetrics || false
      },
      performance: {
        enableLOD: legacyConfig.enableLOD !== false,
        enableVirtualization: legacyConfig.enableVirtualization !== false
      }
    };
  }
};

/**
 * Development and debugging utilities
 */
export const DevTools = {
  /**
   * Enables debug mode for development
   */
  enableDebugMode(): void {
    if (typeof window !== 'undefined') {
      (window as any).visualizationDebugMode = true;
      console.log('Data Visualization Debug Mode Enabled');
    }
  },

  /**
   * Gets performance metrics from current visualization
   */
  getPerformanceMetrics(): any {
    const current = MigrationHelpers.getCurrentVisualization();
    return current?.getPerformanceMetrics() || null;
  },

  /**
   * Gets visualization statistics
   */
  getVisualizationStats(): any {
    const current = MigrationHelpers.getCurrentVisualization();
    return current?.getVisualizationStats() || null;
  },

  /**
   * Logs current state for debugging
   */
  logCurrentState(): void {
    const current = MigrationHelpers.getCurrentVisualization();
    if (current) {
      console.group('Data Visualization State');
      console.log('Performance:', this.getPerformanceMetrics());
      console.log('Statistics:', this.getVisualizationStats());
      console.log('State:', current.getState());
      console.groupEnd();
    } else {
      console.warn('No active visualization found');
    }
  }
};

/**
 * Version information
 */
export const VERSION = {
  major: 2,
  minor: 0,
  patch: 0,
  prerelease: 'beta',
  toString(): string {
    return `${this.major}.${this.minor}.${this.patch}${this.prerelease ? `-${this.prerelease}` : ''}`;
  }
};

// Log version information in development
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  console.log(`Data Visualization Feature v${VERSION.toString()} loaded`);
}
