{"version": 3, "sources": ["webpack:///./src/ui/src/components/TextPopup/TextPopup.scss?7956", "webpack:///./src/ui/src/components/TextPopup/TextPopup.scss", "webpack:///./src/ui/src/components/TextPopup/TextPopup.js", "webpack:///./src/ui/src/components/TextPopup/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "React", "memo", "withTranslation", "t", "selectedTextQuads", "useSelector", "state", "selectors", "isElementDisabled", "DataElements", "TEXT_POPUP", "isElementOpen", "getPopupItems", "isRightClickAnnotationPopupEnabled", "getActiveDocumentViewerKey", "isMultiViewerMode", "shallowEqual", "isDisabled", "isOpen", "popupItems", "activeDocumentViewerKey", "dispatch", "useDispatch", "useState", "left", "top", "position", "setPosition", "popupRef", "useRef", "useOnClickOutside", "actions", "closeElement", "useEffect", "closeElements", "ANNOTATION_POPUP", "CONTEXT_MENU_POPUP", "INLINE_COMMENT_POPUP", "current", "getTextPopupPositionBasedOn", "textPopup", "className", "classNames", "Popup", "TextPopup", "open", "closed", "data-element", "ref", "style", "onClick", "role", "aria-label", "FocusTrap", "locked", "CustomizablePopup", "dataElement", "childrenClassName", "ActionButton", "label", "title", "img", "copyText", "createTextAnnotationAndSelect", "Core", "Annotations", "TextHighlightAnnotation", "TextUnderlineAnnotation", "TextSquigglyAnnotation", "TextStrikeoutAnnotation", "openElement", "LINK_MODAL", "core", "isCreateRedactionEnabled", "fillColor", "RedactionAnnotation", "isIE", "isMobile", "cancel"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,k8FAAm8F,KAG59F0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,smFCYvB,IA2JeC,MAAMC,KAAKC,eA3JR,SAAH,GAAiC,IAA3BC,EAAC,EAADA,EAAGC,EAAiB,EAAjBA,kBAkBrB,IAVGC,aACF,SAACC,GAAK,MAAK,CACTC,IAAUC,kBAAkBF,EAAOG,IAAaC,YAChDH,IAAUI,cAAcL,EAAOG,IAAaC,YAC5CH,IAAUK,cAAcN,EAAOG,IAAaC,YAC5CH,IAAUM,mCAAmCP,GAC7CC,IAAUO,2BAA2BR,GACrCC,IAAUQ,kBAAkBT,MAE9BU,KACD,GAhBCC,EAAU,KACVC,EAAM,KACNC,EAAU,KACVN,EAAkC,KAClCO,EAAuB,KACvBL,EAAiB,KAabM,EAAWC,cAC4C,IAA7BC,mBAAS,CAAEC,KAAM,EAAGC,IAAK,IAAI,GAAtDC,EAAQ,KAAEC,EAAW,KACtBC,EAAWC,mBACjBC,YAAkBF,GAAU,WAC1BP,EAASU,IAAQC,aAAavB,IAAaC,gBAS7CuB,qBAAU,WACJf,GACFG,EAASU,IAAQG,cAAc,CAC7BzB,IAAa0B,iBACb1B,IAAa2B,mBACb3B,IAAa4B,0BAGhB,CAAChB,EAAUH,IAEde,qBAAU,WAfJL,EAASU,SAAWnB,EAAWrC,OAAS,GAAKsB,GAC/CuB,EAAYY,YAA4BnC,EAAmBwB,EAAUR,MAgBtE,CAAChB,EAAmBgB,EAAyBL,IAIhD,GAAIE,EACF,OAAO,KAGT,IAAMuB,EACJ,yBACEC,UAAWC,IAAW,CACpBC,OAAO,EACPC,WAAW,EACXC,KAAM3B,EACN4B,QAAS5B,EACT,cAAeL,EACf,iBAAkBA,IAEpBkC,eAActC,IAAaC,WAC3BsC,IAAKpB,EACLqB,MAAK,KAAOvB,GACZwB,QAnBY,WAAH,OAAS7B,EAASU,IAAQC,aAAavB,IAAaC,cAoB7DyC,KAAK,UACLC,aAAYjD,EAAE,wBAEd,kBAACkD,EAAA,EAAS,CAACC,OAAQpC,GAA2B,IAAjBQ,EAASD,KAA+B,IAAlBC,EAASF,MAC1D,yBAAKiB,UAAU,aACb,kBAACc,EAAA,EAAiB,CAChBC,YAAa/C,IAAaC,WAC1B+C,kBAAkB,oBAElB,kBAACC,EAAA,EAAY,CACXjB,UAAU,mBACVe,YAAY,iBACZG,MAAO9C,EAAqC,cAAgB,GAC5D+C,MAAQ/C,EAAqD,GAAhB,cAC7CgD,IAAI,qBACJX,QAAS,kBAAMY,YAAS1C,IACxB+B,KAAK,WAEP,kBAACO,EAAA,EAAY,CACXjB,UAAU,mBACVe,YAAY,0BACZG,MAAO9C,EAAqC,uBAAyB,GACrE+C,MAAQ/C,EAA8D,GAAzB,uBAC7CgD,IAAI,sBACJX,QAAS,kBAAMa,YAA8B1C,EAAU9C,OAAOyF,KAAKC,YAAYC,wBAAyB9C,IACxG+B,KAAK,WAEP,kBAACO,EAAA,EAAY,CACXjB,UAAU,mBACVe,YAAY,0BACZG,MAAO9C,EAAqC,uBAAyB,GACrE+C,MAAQ/C,EAA8D,GAAzB,uBAC7CgD,IAAI,wCACJX,QAAS,kBAAMa,YAA8B1C,EAAU9C,OAAOyF,KAAKC,YAAYE,wBAAyB/C,IACxG+B,KAAK,WAEP,kBAACO,EAAA,EAAY,CACXjB,UAAU,mBACVe,YAAY,yBACZG,MAAO9C,EAAqC,sBAAwB,GACpE+C,MAAQ/C,EAA6D,GAAxB,sBAC7CgD,IAAI,uCACJX,QAAS,kBAAMa,YAA8B1C,EAAU9C,OAAOyF,KAAKC,YAAYG,uBAAwBhD,IACvG+B,KAAK,WAEP,kBAACO,EAAA,EAAY,CACXjB,UAAU,mBACVkB,MAAO9C,EAAqC,uBAAyB,GACrE+C,MAAQ/C,EAA8D,GAAzB,uBAC7CgD,IAAI,4CACJX,QAAS,kBAAMa,YAA8B1C,EAAU9C,OAAOyF,KAAKC,YAAYI,wBAAyBjD,IACxGoC,YAAY,0BACZL,KAAK,WAEP,kBAACO,EAAA,EAAY,CACXjB,UAAU,mBACVkB,MAAO9C,EAAqC,YAAc,GAC1D+C,MAAQ/C,EAAmD,GAAd,YAC7CgD,IAAI,iBACJX,QAAS,kBAAM7B,EAASU,IAAQuC,YAAY7D,IAAa8D,cACzDf,YAAY,aACZL,KAAK,WAENqB,IAAKC,4BACJ,kBAACf,EAAA,EAAY,CACXjB,UAAU,mBACVe,YAAY,uBACZG,MAAO9C,EAAqC,oCAAsC,GAClF+C,MAAQ/C,EAA2E,GAAtC,oCAC7C6D,UAAU,SACVb,IAAI,kCACJX,QAAS,kBAAMa,YAA8B1C,EAAU9C,OAAOyF,KAAKC,YAAYU,oBAAqBvD,IACpG+B,KAAK,eASnB,OAAOyB,KAAQC,cACbrC,EAEA,kBAAC,IAAS,CAACsC,OAAO,iEAAiEtC,OC3KxEI", "file": "chunks/chunk.79.js", "sourcesContent": ["var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./TextPopup.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".open.TextPopup{visibility:visible}.closed.TextPopup{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.TextPopup{position:absolute;z-index:70;display:flex;justify-content:center;align-items:center}.TextPopup:empty{padding:0}.TextPopup .buttons{display:flex}.TextPopup .Button{margin:4px;width:32px;height:32px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .TextPopup .Button{width:42px;height:42px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .TextPopup .Button{width:42px;height:42px}}.TextPopup .Button:hover{background:var(--popup-button-hover)}.TextPopup .Button:hover:disabled{background:none}.TextPopup .Button .Icon{width:18px;height:18px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .TextPopup .Button .Icon{width:24px;height:24px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .TextPopup .Button .Icon{width:24px;height:24px}}.is-vertical.TextPopup .Button.main-menu-button{width:100%;border-radius:0;justify-content:flex-start;padding-left:var(--padding-small);padding-right:var(--padding-small);margin:0 0 var(--padding-tiny) 0}.is-vertical.TextPopup .Button.main-menu-button:first-child{margin-top:var(--padding-tiny)}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .is-vertical.TextPopup .Button.main-menu-button{width:100%;height:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .is-vertical.TextPopup .Button.main-menu-button{width:100%;height:32px}}.is-vertical.TextPopup .Button.main-menu-button .Icon{margin-right:10px}.is-vertical.TextPopup .Button.main-menu-button span{white-space:nowrap}@keyframes bottom-up{0%{transform:translateY(100%)}to{transform:translateY(0)}}@keyframes up-bottom{0%{transform:translateY(0)}to{transform:translateY(100%)}}.TextPopup{box-shadow:0 0 3px 0 var(--document-box-shadow);background:var(--component-background);border-radius:4px}.TextPopup.is-horizontal .container{display:inherit}.TextPopup.is-vertical{flex-direction:column;align-items:flex-start}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React, { useState, useEffect, useRef } from 'react';\nimport { useSelector, useDispatch, shallowEqual } from 'react-redux';\nimport { withTranslation } from 'react-i18next';\nimport FocusTrap from 'components/FocusTrap';\nimport Draggable from 'react-draggable';\nimport classNames from 'classnames';\n\nimport ActionButton from 'components/ActionButton';\nimport CustomizablePopup from 'components/CustomizablePopup';\n\nimport core from 'core';\nimport { getTextPopupPositionBasedOn } from 'helpers/getPopupPosition';\nimport createTextAnnotationAndSelect from 'helpers/createTextAnnotationAndSelect';\nimport copyText from 'helpers/copyText';\nimport useOnClickOutside from 'hooks/useOnClickOutside';\nimport actions from 'actions';\nimport selectors from 'selectors';\nimport { isMobile, isIE } from 'helpers/device';\nimport DataElements from 'src/constants/dataElement';\n\nimport './TextPopup.scss';\n\nconst TextPopup = ({ t, selectedTextQuads }) => {\n  const [\n    isDisabled,\n    isOpen,\n    popupItems,\n    isRightClickAnnotationPopupEnabled,\n    activeDocumentViewerKey,\n    isMultiViewerMode,\n  ] = useSelector(\n    (state) => [\n      selectors.isElementDisabled(state, DataElements.TEXT_POPUP),\n      selectors.isElementOpen(state, DataElements.TEXT_POPUP),\n      selectors.getPopupItems(state, DataElements.TEXT_POPUP),\n      selectors.isRightClickAnnotationPopupEnabled(state),\n      selectors.getActiveDocumentViewerKey(state),\n      selectors.isMultiViewerMode(state),\n    ],\n    shallowEqual,\n  );\n\n  const dispatch = useDispatch();\n  const [position, setPosition] = useState({ left: 0, top: 0 });\n  const popupRef = useRef();\n  useOnClickOutside(popupRef, () => {\n    dispatch(actions.closeElement(DataElements.TEXT_POPUP));\n  });\n\n  const setPopupPositionAndShow = () => {\n    if (popupRef.current && popupItems.length > 0 && selectedTextQuads) {\n      setPosition(getTextPopupPositionBasedOn(selectedTextQuads, popupRef, activeDocumentViewerKey));\n    }\n  };\n\n  useEffect(() => {\n    if (isOpen) {\n      dispatch(actions.closeElements([\n        DataElements.ANNOTATION_POPUP,\n        DataElements.CONTEXT_MENU_POPUP,\n        DataElements.INLINE_COMMENT_POPUP,\n      ]));\n    }\n  }, [dispatch, isOpen]);\n\n  useEffect(() => {\n    setPopupPositionAndShow();\n  }, [selectedTextQuads, activeDocumentViewerKey, isMultiViewerMode]);\n\n  const onClose = () => dispatch(actions.closeElement(DataElements.TEXT_POPUP));\n\n  if (isDisabled) {\n    return null;\n  }\n\n  const textPopup = (\n    <div\n      className={classNames({\n        Popup: true,\n        TextPopup: true,\n        open: isOpen,\n        closed: !isOpen,\n        'is-vertical': isRightClickAnnotationPopupEnabled,\n        'is-horizontal': !isRightClickAnnotationPopupEnabled,\n      })}\n      data-element={DataElements.TEXT_POPUP}\n      ref={popupRef}\n      style={{ ...position }}\n      onClick={onClose}\n      role=\"listbox\"\n      aria-label={t('component.textPopup')}\n    >\n      <FocusTrap locked={isOpen && position.top !== 0 && position.left !== 0}>\n        <div className=\"container\">\n          <CustomizablePopup\n            dataElement={DataElements.TEXT_POPUP}\n            childrenClassName='main-menu-button'\n          >\n            <ActionButton\n              className=\"main-menu-button\"\n              dataElement=\"copyTextButton\"\n              label={isRightClickAnnotationPopupEnabled ? 'action.copy' : ''}\n              title={!isRightClickAnnotationPopupEnabled ? 'action.copy' : ''}\n              img=\"ic_copy_black_24px\"\n              onClick={() => copyText(activeDocumentViewerKey)}\n              role=\"option\"\n            />\n            <ActionButton\n              className=\"main-menu-button\"\n              dataElement=\"textHighlightToolButton\"\n              label={isRightClickAnnotationPopupEnabled ? 'annotation.highlight' : ''}\n              title={!isRightClickAnnotationPopupEnabled ? 'annotation.highlight' : ''}\n              img=\"icon-tool-highlight\"\n              onClick={() => createTextAnnotationAndSelect(dispatch, window.Core.Annotations.TextHighlightAnnotation, activeDocumentViewerKey)}\n              role=\"option\"\n            />\n            <ActionButton\n              className=\"main-menu-button\"\n              dataElement=\"textUnderlineToolButton\"\n              label={isRightClickAnnotationPopupEnabled ? 'annotation.underline' : ''}\n              title={!isRightClickAnnotationPopupEnabled ? 'annotation.underline' : ''}\n              img=\"icon-tool-text-manipulation-underline\"\n              onClick={() => createTextAnnotationAndSelect(dispatch, window.Core.Annotations.TextUnderlineAnnotation, activeDocumentViewerKey)}\n              role=\"option\"\n            />\n            <ActionButton\n              className=\"main-menu-button\"\n              dataElement=\"textSquigglyToolButton\"\n              label={isRightClickAnnotationPopupEnabled ? 'annotation.squiggly' : ''}\n              title={!isRightClickAnnotationPopupEnabled ? 'annotation.squiggly' : ''}\n              img=\"icon-tool-text-manipulation-squiggly\"\n              onClick={() => createTextAnnotationAndSelect(dispatch, window.Core.Annotations.TextSquigglyAnnotation, activeDocumentViewerKey)}\n              role=\"option\"\n            />\n            <ActionButton\n              className=\"main-menu-button\"\n              label={isRightClickAnnotationPopupEnabled ? 'annotation.strikeout' : ''}\n              title={!isRightClickAnnotationPopupEnabled ? 'annotation.strikeout' : ''}\n              img=\"icon-tool-text-manipulation-strikethrough\"\n              onClick={() => createTextAnnotationAndSelect(dispatch, window.Core.Annotations.TextStrikeoutAnnotation, activeDocumentViewerKey)}\n              dataElement=\"textStrikeoutToolButton\"\n              role=\"option\"\n            />\n            <ActionButton\n              className=\"main-menu-button\"\n              label={isRightClickAnnotationPopupEnabled ? 'tool.Link' : ''}\n              title={!isRightClickAnnotationPopupEnabled ? 'tool.Link' : ''}\n              img=\"icon-tool-link\"\n              onClick={() => dispatch(actions.openElement(DataElements.LINK_MODAL))}\n              dataElement=\"linkButton\"\n              role=\"option\"\n            />\n            {core.isCreateRedactionEnabled() && (\n              <ActionButton\n                className=\"main-menu-button\"\n                dataElement=\"textRedactToolButton\"\n                label={isRightClickAnnotationPopupEnabled ? 'option.redaction.markForRedaction' : ''}\n                title={!isRightClickAnnotationPopupEnabled ? 'option.redaction.markForRedaction' : ''}\n                fillColor=\"868E96\"\n                img=\"icon-tool-select-area-redaction\"\n                onClick={() => createTextAnnotationAndSelect(dispatch, window.Core.Annotations.RedactionAnnotation, activeDocumentViewerKey)}\n                role=\"option\"\n              />\n            )}\n          </CustomizablePopup>\n        </div>\n      </FocusTrap>\n    </div>\n  );\n\n  return isIE || isMobile() ? (\n    textPopup\n  ) : (\n    <Draggable cancel=\".Button, .cell, .sliders-container svg, select, button, input\">{textPopup}</Draggable>\n  );\n};\n\nexport default React.memo(withTranslation()(TextPopup));\n", "import TextPopup from './TextPopup';\n\nexport default TextPopup;\n"], "sourceRoot": ""}