.commandbar {
  display: flex;
  flex-direction: column;
  width: 100%;
  font-size: 14px;
  font-family: "Arial";
  flex-shrink: 0;
  background-color: #f8f8f8;
}
 
.titlebar-frame {
  display: flex;
  flex-direction: row;
  color: white;
  background-color: royalblue;
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 34px;
  padding-right: 30px;
  margin-bottom: 4px;
  border-bottom: 1px solid royalblue;
  flex-shrink: 0;
  white-space: nowrap;
  -webkit-touch-callout: none; /* iOS Safari */
  -webkit-user-select: none; /* Safari */
  -khtml-user-select: none; /* Konqueror HTML */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* Internet Explorer/Edge */
  user-select: none; /* Non-prefixed version, currently supported by Chrome and Opera */
}

.titlebar-frame span {
  align-self: center;
  font-family: "Arial";
  font-weight: 700;
  font-size: 18px;
}

.noselect {
  -webkit-touch-callout: none; /* iOS Safari */
  -webkit-user-select: none; /* Safari */
  -khtml-user-select: none; /* Konqueror HTML */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* Internet Explorer/Edge */
  user-select: none; /* Non-prefixed version, currently supported by Chrome and Opera */
}

.toolbar {
  display: flex;
  flex-direction: row;
  padding-left: 35px;
  padding-right: 20px;
  padding-top: 5px;
  padding-bottom: 5px;
  white-space: nowrap;
  -webkit-touch-callout: none; /* iOS Safari */
  -webkit-user-select: none; /* Safari */
  -khtml-user-select: none; /* Konqueror HTML */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* Internet Explorer/Edge */
  user-select: none; /* Non-prefixed version, currently supported by Chrome and Opera */
  font-size: 14px;
  font-family: "Arial";
  flex-shrink: 0;
}

#editorWrapper {
  height: 100%;
  width: 100%;
  flex: 1 1 auto;
  overflow: hidden;
  background-color: #e8e8e8;
  padding-left: 4px;
  padding-top: 0px;
  padding-right: 4px;
  padding-bottom: 4px;
  z-index: 60 !important; /* CanvasManager.appendAllCanvas automatically sets this to 20 so we force it to be 60. */
}

.inner-body {
  background-color: white;
  width: 100%;
  height: 100%;
  overflow: hidden;
}


.cell-creator {
  width: 10px;
  height: 10px;
  margin: 2px;
  border: 1px solid black;
}

.cell-selected {
  border-color: royalblue;
  background: rgba(0, 0, 255, 0.2);
}

.cell-hidden {
  display: none;
}

.grid-wrapper {
  padding: 10px;
}

.btn {
  min-width: 75px;
}

/* Full width */
.full-width {
  width: 100%;
}