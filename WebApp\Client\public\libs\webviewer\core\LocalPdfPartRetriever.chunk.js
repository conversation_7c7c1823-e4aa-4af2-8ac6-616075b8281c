/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[8],{617:function(ya,ua,n){n.r(ua);var na=n(0);ya=n(53);var ma=n(537),oa=n(298),ka=n(26),ia=window;n=function(){function fa(x){var y=this;this.UEa=function(r){return r&&("image"===r.type.split("/")[0].toLowerCase()||r.name&&!!r.name.match(/.(jpg|jpeg|png|gif)$/i))};this.file=x;this.mFa=new Promise(function(r){return Object(na.b)(y,void 0,void 0,function(){var e;return Object(na.d)(this,function(a){switch(a.label){case 0:return this.UEa(this.file)?
[4,Object(oa.b)(x)]:[3,2];case 1:e=a.aa(),this.file=ka.q?new Blob([e],{type:x.type}):new File([e],null===x||void 0===x?void 0:x.name,{type:x.type}),a.label=2;case 2:return r(!0),[2]}})})})}fa.prototype.getFileData=function(x){var y=this,r=new FileReader;r.onload=function(e){y.trigger(fa.Events.DOCUMENT_LOADING_PROGRESS,[e.loaded,e.loaded]);x(new Uint8Array(e.target.result))};r.onprogress=function(e){e.lengthComputable&&y.trigger(fa.Events.DOCUMENT_LOADING_PROGRESS,[e.loaded,0<e.total?e.total:0])};
r.readAsArrayBuffer(this.file)};fa.prototype.getFile=function(){return Object(na.b)(this,void 0,Promise,function(){return Object(na.d)(this,function(x){switch(x.label){case 0:return[4,this.mFa];case 1:return x.aa(),ia.da.isJSWorker?[2,this.file.path]:[2,this.file]}})})};fa.Events={DOCUMENT_LOADING_PROGRESS:"documentLoadingProgress"};return fa}();Object(ya.a)(n);Object(ma.a)(n);Object(ma.b)(n);ua["default"]=n}}]);}).call(this || window)
