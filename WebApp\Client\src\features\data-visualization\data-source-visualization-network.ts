/**
 * Data Source Visualization Network - Main Orchestrator Class
 * Coordinates all modules to create a complete visualization system
 */

import * as d3 from 'd3';
import {
	Node,
	Link,
	DataNetwork,
	NodeInteractionEvent,
	VisualizationState,
	VisualizationStateEvent,
	Position,
	TypeGuards,
	NetworkUtils
} from './models/network-types';
import { VisualizationConfig, DEFAULT_CONFIG, ConfigUtils } from './models/visualization-configuration';
import { NodeManager } from './modules/node-manager';
import { LinkManager } from './modules/link-manager';
import { InteractionManager } from './modules/interaction-manager';
import { UIControlsManager } from './modules/ui-controls-manager';
import { TooltipManager } from './modules/tooltip-manager';
import { DataProcessor } from './modules/data-processor';
import { AnimationManager } from './modules/animation-manager';

/**
 * External API functions (from legacy system)
 */
declare global {
	interface Window {
		initializeVisualization: (id: string, key: string) => Promise<void>;
		refreshFilterPanel: (urlKey?: string) => Promise<void>;
		hideVisualizationFilter: () => void;
		DataSourceVisulizationNetwork: typeof DataSourceVisualizationNetwork;
	}
}

/**
 * Initialization options for the visualization
 */
export interface VisualizationOptions {
	config?: Partial<VisualizationConfig>;
	initialState?: Partial<VisualizationState>;
	enableDebugMode?: boolean;
	performanceMode?: 'auto' | 'high' | 'balanced' | 'performance';
}

/**
 * Main orchestrator class that coordinates all visualization modules
 */
export class DataSourceVisualizationNetwork {
	// Configuration and state
	private config: VisualizationConfig;
	private state: VisualizationState;
	private containerId: string;
	private rawData: DataNetwork;

	// DOM references
	private container: d3.Selection<HTMLElement, unknown, HTMLElement, any>;
	private svg: d3.Selection<SVGSVGElement, unknown, HTMLElement, any>;
	private g: d3.Selection<SVGGElement, unknown, HTMLElement, any>;

	// D3 selections
	private nodeSelection: d3.Selection<SVGGElement, Node, SVGGElement, unknown> | null = null;
	private linkSelection: d3.Selection<SVGPathElement, Link, SVGGElement, unknown> | null = null;

	// Module managers
	private nodeManager: NodeManager;
	private linkManager: LinkManager;
	private interactionManager: InteractionManager;
	private uiControlsManager: UIControlsManager;
	private tooltipManager: TooltipManager;
	private dataProcessor: DataProcessor;
	private animationManager: AnimationManager;

	// Simulation and lifecycle
	private simulation: d3.Simulation<Node, Link> | null = null;
	private isInitialized: boolean = false;
	private isDestroyed: boolean = false;

	// Performance tracking
	private performanceMetrics: {
		initializationTime: number;
		lastRenderTime: number;
		frameCount: number;
		averageFPS: number;
	} = {
		initializationTime: 0,
		lastRenderTime: 0,
		frameCount: 0,
		averageFPS: 0
	};

	constructor(containerId: string, data: DataNetwork, options: VisualizationOptions = {}) {
		const startTime = performance.now();

		// Validate inputs
		if (!containerId) {
			throw new Error('Container ID is required');
		}

		if (!TypeGuards.isDataNetwork(data)) {
			throw new Error('Invalid data network provided');
		}

		// Store basic properties
		this.containerId = containerId;
		this.rawData = data;

		// Setup configuration
		this.config = this.initializeConfiguration(containerId, options);

		// Initialize state
		this.state = this.initializeState(options.initialState);

		// Setup DOM elements
		this.initializeDOM();

		// Initialize all managers
		this.initializeManagers();

		// Setup event handlers and cross-manager communication
		this.setupEventHandlers();

		// Process data and initialize visualization
		this.initializeVisualization(data);

		// Performance tracking
		this.performanceMetrics.initializationTime = performance.now() - startTime;

		// Mark as initialized
		this.isInitialized = true;

		// Setup global reference for legacy compatibility
		this.setupGlobalReferences();

		// Debug mode setup
		if (options.enableDebugMode) {
			this.enableDebugMode();
		}

		console.log(`DataSourceVisualizationNetwork initialized in ${this.performanceMetrics.initializationTime.toFixed(2)}ms`);
	}

	/**
	 * Initializes the configuration with defaults and user overrides
	 */
	private initializeConfiguration(containerId: string, options: VisualizationOptions): VisualizationConfig {
		// Get container dimensions
		const container = document.getElementById(containerId);
		if (!container) {
			throw new Error(`Container with ID '${containerId}' not found`);
		}

		const rect = container.getBoundingClientRect();
		const containerConfig = {
			containerId,
			containerWidth: rect.width || 800,
			containerHeight: rect.height || 600
		};

		// Merge configurations
		let config = ConfigUtils.mergeConfig(
			{ ...containerConfig, ...options.config },
			DEFAULT_CONFIG
		);

		// Apply performance optimizations if specified
		if (options.performanceMode === 'performance') {
			const nodeCount = this.rawData?.nodes?.length || 0;
			const performanceConfig = ConfigUtils.createPerformanceConfig(nodeCount);
			config = ConfigUtils.mergeConfig(performanceConfig, config);
		}

		// Validate configuration
		const validation = ConfigUtils.validateConfig(config);
		if (!validation.isValid) {
			console.warn('Configuration validation warnings:', validation.errors);
		}

		return config;
	}

	/**
	 * Initializes the visualization state
	 */
	private initializeState(initialState?: Partial<VisualizationState>): VisualizationState {
		return {
			zoomLevel: 1,
			panPosition: { x: 0, y: 0 },
			activeDataSourceType: this.config.dataSourceType,
			visibleNodeIds: [],
			...initialState
		};
	}

	/**
	 * Initializes DOM elements
	 */
	private initializeDOM(): void {
		// Get container
		this.container = d3.select(`#${this.containerId}`);
		if (this.container.empty()) {
			throw new Error(`Container with ID '${this.containerId}' not found`);
		}

		// Clear any existing content
		this.container.selectAll('*').remove();

		// Create SVG
		this.svg = this.container
									 .append('svg')
									 .attr('width', this.config.containerWidth)
									 .attr('height', this.config.containerHeight)
									 .attr('class', 'data-source-visualization')
									 .style('background', this.config.colors.backgroundColor);

		// Create main group for zoomable content
		this.g = this.svg.append('g').attr('class', 'main-group');

		// Setup responsive behavior
		this.setupResponsiveBehavior();
	}

	/**
	 * Sets up responsive behavior for container resizing
	 */
	private setupResponsiveBehavior(): void {
		const resizeObserver = new ResizeObserver((entries) => {
			for (const entry of entries) {
				const { width, height } = entry.contentRect;
				this.handleContainerResize(width, height);
			}
		});

		const containerElement = this.container.node();
		if (containerElement) {
			resizeObserver.observe(containerElement);
		}
	}

	/**
	 * Handles container resize events
	 */
	private handleContainerResize(width: number, height: number): void {
		// Update config
		this.config.containerWidth = width;
		this.config.containerHeight = height;

		// Update SVG dimensions
		this.svg
				.attr('width', width)
				.attr('height', height);

		// Update managers with new dimensions
		this.updateManagersConfiguration();

		// Reposition UI controls
		this.uiControlsManager?.repositionControls?.();

		// Update tooltips if any are open
		this.tooltipManager?.repositionAllTooltips?.();
	}

	/**
	 * Initializes all manager modules
	 */
	private initializeManagers(): void {
		// Initialize managers in dependency order
		this.dataProcessor = new DataProcessor(this.config);
		this.animationManager = new AnimationManager(this.config);
		this.nodeManager = new NodeManager(this.config);
		this.linkManager = new LinkManager(this.config);
		this.tooltipManager = new TooltipManager(this.config);
		this.interactionManager = new InteractionManager(this.config, this.state);
		this.uiControlsManager = new UIControlsManager(this.svg, this.config);
	}

	/**
	 * Sets up event handlers and cross-manager communication
	 */
	private setupEventHandlers(): void {
		// Data processor events
		this.dataProcessor.on('onDataProcessed', ({ originalCount, processedCount, processingTime }) => {
			console.log(`Data processed: ${originalCount} → ${processedCount} nodes in ${processingTime.toFixed(2)}ms`);
		});

		// Node interaction events
		this.interactionManager.on('onNodeClick', (event: NodeInteractionEvent) => {
			this.handleNodeClick(event);
		});

		this.interactionManager.on('onNodeDoubleClick', (event: NodeInteractionEvent) => {
			this.handleNodeDoubleClick(event);
		});

		this.interactionManager.on('onIconClick', (event: NodeInteractionEvent) => {
			this.handleIconClick(event);
		});

		// Zoom and pan events
		this.interactionManager.on('onZoomChange', (event: VisualizationStateEvent) => {
			this.handleZoomChange(event);
		});

		// UI control events
		this.uiControlsManager.on('onZoomIn', () => {
			this.interactionManager.zoomIn();
		});

		this.uiControlsManager.on('onZoomOut', () => {
			this.interactionManager.zoomOut();
		});

		this.uiControlsManager.on('onReset', () => {
			this.resetVisualization();
		});

		this.uiControlsManager.on('onToggleDataSource', (type) => {
			this.toggleDataSourceType(type);
		});

		// Animation events for performance monitoring
		this.animationManager.on('onAnimationStart', ({ animationId, type }) => {
			if (this.config.debug.enableLogging) {
				console.log(`Animation started: ${type} (${animationId})`);
			}
		});
	}

	/**
	 * Initializes the visualization with processed data
	 */
	private initializeVisualization(data: DataNetwork): void {
		// Process the data
		const processedData = this.dataProcessor.processData(data, {
			calculateNodeSizes: true,
			filterUnconnectedNodes: true,
			createLinkBundles: this.config.links.enableBundling,
			validateData: true,
			assignExpansionLayers: true,
			processConnectedFields: true
		});

		// Store processed data
		this.rawData = processedData;
		this.state.visibleNodeIds = processedData.nodes.map(n => n.id);

		// Find root node and store globally for compatibility
		const rootNode = processedData.nodes.find(n => n.highlighted);
		if (rootNode && typeof window !== 'undefined') {
			window.sourceNode = rootNode;
			window.visualizationMainDatasource = rootNode;
		}

		// Setup node positions
		const centerX = this.config.containerWidth! / 4;
		const centerY = this.config.containerHeight! / 4;
		this.nodeManager.setupNodePositions(processedData.nodes, centerX, centerY);

		// Create visualization elements
		this.createVisualization(processedData);

		// Setup simulation
		this.setupSimulation(processedData);

		// Initialize interactions
		this.interactionManager.initialize(this.svg, this.g, this.nodeSelection!, processedData);

		// Start entrance animations
		this.startEntranceAnimations();
	}

	/**
	 * Creates the main visualization elements
	 */
	private createVisualization(data: DataNetwork): void {
		// Create links first (so they appear behind nodes)
		this.linkSelection = this.linkManager.createLinks(this.g, data, this.svg);

		// Create nodes
		this.nodeSelection = this.nodeManager.createNodes(this.g, data);

		// Update UI controls with current state
		this.uiControlsManager.updateControlStates({
			zoomLevel: this.state.zoomLevel,
			dataSourceType: this.state.activeDataSourceType
		});
	}

	/**
	 * Sets up the force simulation
	 */
	private setupSimulation(data: DataNetwork): void {
		const { layout } = this.config;

		if (!layout.enableForceSimulation) return;

		// Create simulation
		this.simulation = d3.forceSimulation<Node, Link>(data.nodes)
												.force('link', d3.forceLink<Node, Link>(data.links)
																				 .id(d => d.id)
																				 .distance(layout.linkDistance))
												.force('charge', d3.forceManyBody().strength(layout.chargeStrength))
												.force('collide', d3.forceCollide<Node>()
																						.radius(d => (d.size || 0) + layout.collisionRadius)
																						.strength(layout.collisionStrength))
												.force('center', d3.forceCenter(
													this.config.containerWidth! / 4,
													this.config.containerHeight! / 4
												));

		// Set up tick handler
		this.simulation.on('tick', () => {
			this.updateSimulationPositions();
		});

		// Run initial ticks for stabilization
		for (let i = 0; i < 10; i++) {
			this.simulation.tick();
		}

		// Final position update
		this.updateSimulationPositions();
	}

	/**
	 * Updates positions during simulation ticks
	 */
	private updateSimulationPositions(): void {
		if (!this.nodeSelection || !this.linkSelection) return;

		// Update node positions
		this.nodeManager.updateNodePositions(this.nodeSelection);

		// Update link positions
		this.linkManager.updateLinkPositions(this.linkSelection, this.rawData.nodes);

		// Update tooltip positions if any are open
		if (this.tooltipManager.hasVisibleTooltips()) {
			// Would need to implement repositioning of active tooltips
		}
	}

	/**
	 * Starts entrance animations for the visualization
	 */
	private startEntranceAnimations(): void {
		if (!this.nodeSelection || !this.linkSelection) return;

		// Animate nodes appearing
		this.animationManager.animateNodeAppearance(this.nodeSelection);

		// Animate links appearing with delay
		setTimeout(() => {
			this.animationManager.animateLinkAppearance(this.linkSelection!);
		}, 200);

		// Animate UI controls appearing
		setTimeout(() => {
			this.animationManager.animateControlsAppearance(
				this.svg.selectAll('.help-icon, .zoom-controls, .data-source-toggle, .reset-button')
			);
		}, 400);

		// Start fade out animation for grayed nodes after initial appearance
		setTimeout(() => {
			this.animationManager.animateNodeFadeOut(this.nodeSelection!);
			this.animationManager.animateLinkFadeOut(this.linkSelection!, this.rawData.nodes);
		}, 1000);
	}

	/**
	 * Event Handlers
	 */

	/**
	 * Handles single click on nodes
	 */
	private handleNodeClick(event: NodeInteractionEvent): void {
		// Close any existing tooltips for other nodes
		this.tooltipManager.closeAllTooltips();

		// Show tooltip for clicked node
		const nodeGroup = d3.select(`[data-node-id="${event.node.id}"]`) as d3.Selection<SVGGElement, unknown, null, undefined>;
		this.tooltipManager.showNodeTooltip(event, nodeGroup);

		// Update state
		this.state.highlightedNodeId = event.node.id;
	}

	/**
	 * Handles double click on nodes (navigation)
	 */
	private handleNodeDoubleClick(event: NodeInteractionEvent): void {
		if (event.node.id && typeof window !== 'undefined' && window.initializeVisualization) {
			// Close tooltips before navigation
			this.tooltipManager.closeAllTooltips();

			// Navigate to new root node
			window.initializeVisualization(event.node.id, this.state.activeDataSourceType);
		}
	}

	/**
	 * Handles icon click (external navigation)
	 */
	private handleIconClick(event: NodeInteractionEvent): void {
		if (event.node.slug && typeof window !== 'undefined' && window.visualizationMainDatasource) {
			const datasource = window.visualizationMainDatasource;
			const datastoreSlug = datasource.dataStore?.slug;

			if (datastoreSlug) {
				const url = `/Admin/DataStores/${datastoreSlug}/DataSources/${event.node.slug}/Connections`;
				window.open(url, '_blank');
			}
		}
	}

	/**
	 * Handles zoom level changes
	 */
	private handleZoomChange(event: VisualizationStateEvent): void {
		// Update internal state
		this.state.zoomLevel = event.newState.zoomLevel;
		this.state.panPosition = event.newState.panPosition;

		// Update UI controls
		this.uiControlsManager.updateControlStates({
			zoomLevel: this.state.zoomLevel
		});

		// Update global zoom scale for compatibility
		if (typeof window !== 'undefined') {
			window.zoomScale = this.state.zoomLevel;
		}
	}

	/**
	 * Public API Methods
	 */

	/**
	 * Resets the visualization to initial state
	 */
	public async resetVisualization(): Promise<void> {
		// Reset interaction manager
		this.interactionManager.resetVisualization();

		// Reset to initial data source type
		this.state.activeDataSourceType = 'pages';

		// Get main data source
		const mainDataSource = typeof window !== 'undefined' ? window.visualizationMainDatasource : null;

		if (mainDataSource?.id && typeof window !== 'undefined') {
			// Hide filter panel
			if (window.hideVisualizationFilter) {
				window.hideVisualizationFilter();
			}

			// Refresh filter panel
			if (window.refreshFilterPanel) {
				await window.refreshFilterPanel();
			}

			// Initialize with main data source
			if (window.initializeVisualization) {
				await window.initializeVisualization(mainDataSource.id, 'pages');
			}
		}
	}

	/**
	 * Toggles between data source types
	 */
	public async toggleDataSourceType(type: 'pages' | 'dependantSources'): Promise<void> {
		if (this.state.activeDataSourceType === type) return;

		// Update state
		this.state.activeDataSourceType = type;

		// Update UI controls
		this.uiControlsManager.updateControlStates({
			dataSourceType: type
		});

		// Close tooltips
		this.tooltipManager.closeAllTooltips();

		// Animate the transition
		if (this.nodeSelection && this.linkSelection) {
			this.animationManager.animateDataSourceToggle(
				this.nodeSelection,
				this.linkSelection,
				type
			);
		}

		// Update global state
		if (typeof window !== 'undefined') {
			window.routeKey = type;

			// Hide filter panel
			if (window.hideVisualizationFilter) {
				window.hideVisualizationFilter();
			}

			// Refresh filter panel based on type
			const urlKey = type === 'dependantSources' ? 'DependentDataVisualization' : 'DataVisualization';
			if (window.refreshFilterPanel) {
				await window.refreshFilterPanel(urlKey);
			}

			// Reinitialize visualization
			const rootNode = this.rawData.nodes.find(n => n.highlighted);
			if (rootNode?.id && window.initializeVisualization) {
				await window.initializeVisualization(rootNode.id, type);
			}
		}
	}

	/**
	 * Updates the visualization with new data
	 */
	public updateData(newData: DataNetwork): void {
		if (!TypeGuards.isDataNetwork(newData)) {
			throw new Error('Invalid data network provided');
		}

		// Process new data
		const processedData = this.dataProcessor.processData(newData);

		// Update stored data
		this.rawData = processedData;
		this.state.visibleNodeIds = processedData.nodes.map(n => n.id);

		// Animate out old elements
		if (this.nodeSelection && this.linkSelection) {
			this.animationManager.animateNodeFadeOut(this.nodeSelection);
			this.animationManager.animateLinkFadeOut(this.linkSelection, []);
		}

		// Update visualization after animation
		setTimeout(() => {
			this.updateVisualizationElements(processedData);
		}, 500);
	}

	/**
	 * Updates visualization elements with new data
	 */
	private updateVisualizationElements(data: DataNetwork): void {
		// Clear existing elements
		this.g.selectAll('.nodes-group').remove();
		this.g.selectAll('.links-group').remove();

		// Recreate with new data
		this.createVisualization(data);

		// Update simulation
		if (this.simulation) {
			this.simulation.nodes(data.nodes);

			const linkForce = this.simulation.force('link') as d3.ForceLink<Node, Link>;
			if (linkForce) {
				linkForce.links(data.links);
			}

			this.simulation.restart();
		}

		// Reinitialize interactions
		this.interactionManager.initialize(this.svg, this.g, this.nodeSelection!, data);

		// Start entrance animations
		this.startEntranceAnimations();
	}

	/**
	 * Gets current visualization state
	 */
	public getState(): VisualizationState {
		return { ...this.state };
	}

	/**
	 * Updates configuration
	 */
	public updateConfig(newConfig: Partial<VisualizationConfig>): void {
		this.config = { ...this.config, ...newConfig };
		this.updateManagersConfiguration();
	}

	/**
	 * Updates all managers with new configuration
	 */
	private updateManagersConfiguration(): void {
		this.nodeManager.updateConfig(this.config);
		this.linkManager.updateConfig(this.config);
		this.interactionManager.updateConfig(this.config);
		this.uiControlsManager.updateConfig(this.config);
		this.tooltipManager.updateConfig(this.config);
		this.dataProcessor.updateConfig(this.config);
		this.animationManager.updateConfig(this.config);
	}

	/**
	 * Gets performance metrics
	 */
	public getPerformanceMetrics(): typeof this.performanceMetrics {
		return { ...this.performanceMetrics };
	}

	/**
	 * Gets statistics about the current visualization
	 */
	public getVisualizationStats(): {
		nodeCount: number;
		linkCount: number;
		animationStats: any;
		tooltipStats: any;
		dataProcessingStats: any;
	} {
		return {
			nodeCount: this.rawData.nodes.length,
			linkCount: this.rawData.links.length,
			animationStats: this.animationManager.getAnimationStats(),
			tooltipStats: this.tooltipManager.getTooltipState(),
			dataProcessingStats: this.dataProcessor.getProcessingStats()
		};
	}

	/**
	 * Debug and Development Methods
	 */

	/**
	 * Enables debug mode with additional logging and tools
	 */
	private enableDebugMode(): void {
		// Add debug panel
		this.createDebugPanel();

		// Enable verbose logging
		this.config.debug.enableLogging = true;
		this.config.debug.showPerformanceMetrics = true;

		// Add global debug methods
		if (typeof window !== 'undefined') {
			(window as any).visualizationDebug = {
				getState: () => this.getState(),
				getStats: () => this.getVisualizationStats(),
				getPerformance: () => this.getPerformanceMetrics(),
				logAnimations: () => this.animationManager.logAnimationStats(),
				logTooltips: () => this.tooltipManager.logTooltipStats?.(),
				logProcessing: () => this.dataProcessor.logProcessingStats()
			};
		}
	}

	/**
	 * Creates a debug panel for development
	 */
	private createDebugPanel(): void {
		const debugPanel = this.container
													 .append('div')
													 .attr('class', 'debug-panel')
													 .style('position', 'absolute')
													 .style('top', '10px')
													 .style('right', '10px')
													 .style('background', 'rgba(0,0,0,0.8)')
													 .style('color', 'white')
													 .style('padding', '10px')
													 .style('border-radius', '5px')
													 .style('font-family', 'monospace')
													 .style('font-size', '12px')
													 .style('z-index', '9999')
													 .style('max-width', '200px');

		// Update debug info periodically
		setInterval(() => {
			const stats = this.getVisualizationStats();
			const performance = this.getPerformanceMetrics();

			debugPanel.html(`
        <div><strong>Debug Panel</strong></div>
        <div>Nodes: ${stats.nodeCount}</div>
        <div>Links: ${stats.linkCount}</div>
        <div>Animations: ${stats.animationStats.activeAnimations}</div>
        <div>Tooltips: ${stats.tooltipStats.totalTooltips}</div>
        <div>Init Time: ${performance.initializationTime.toFixed(2)}ms</div>
        <div>Zoom: ${(this.state.zoomLevel * 100).toFixed(0)}%</div>
        <div>Data Source: ${this.state.activeDataSourceType}</div>
      `);
		}, 1000);
	}

	/**
	 * Setup global references for legacy compatibility
	 */
	private setupGlobalReferences(): void {
		if (typeof window !== 'undefined') {
			// Store global reference
			window.DataSourceVisulizationNetwork = DataSourceVisualizationNetwork;

			// Store instance reference if needed
			(window as any).currentVisualizationInstance = this;
		}
	}

	/**
	 * Cleanup and Destruction
	 */

	/**
	 * Destroys the visualization and cleans up all resources
	 */
	public destroy(): void {
		if (this.isDestroyed) return;

		console.log('Destroying DataSourceVisualizationNetwork...');

		// Stop simulation
		if (this.simulation) {
			this.simulation.stop();
		}

		// Destroy all managers
		this.animationManager?.destroy();
		this.tooltipManager?.destroy();
		this.uiControlsManager?.destroy();
		this.interactionManager?.destroy();
		this.linkManager?.destroy();
		this.nodeManager?.destroy();
		this.dataProcessor?.destroy();

		// Clear DOM
		this.container?.selectAll('*').remove();

		// Clear global references
		if (typeof window !== 'undefined') {
			delete (window as any).currentVisualizationInstance;
			delete (window as any).visualizationDebug;
		}

		// Mark as destroyed
		this.isDestroyed = true;
	}

	/**
	 * Static Factory Methods
	 */

	/**
	 * Creates a visualization with default configuration
	 */
	public static create(
		containerId: string,
		data: DataNetwork,
		options: VisualizationOptions = {}
	): DataSourceVisualizationNetwork {
		return new DataSourceVisualizationNetwork(containerId, data, options);
	}

	/**
	 * Creates a high-performance visualization optimized for large datasets
	 */
	public static createHighPerformance(
		containerId: string,
		data: DataNetwork,
		options: VisualizationOptions = {}
	): DataSourceVisualizationNetwork {
		const performanceOptions: VisualizationOptions = {
			...options,
			performanceMode: 'performance',
			config: {
				...options.config,
				animations: {
					...options.config?.animations,
					enableAnimations: false
				},
				performance: {
					...options.config?.performance,
					enableLOD: true,
					enableVirtualization: true
				}
			}
		};

		return new DataSourceVisualizationNetwork(containerId, data, performanceOptions);
	}

	/**
	 * Creates a visualization with accessibility optimizations
	 */
	public static createAccessible(
		containerId: string,
		data: DataNetwork,
		options: VisualizationOptions = {}
	): DataSourceVisualizationNetwork {
		const accessibilityConfig = ConfigUtils.createAccessibilityConfig();

		const accessibleOptions: VisualizationOptions = {
			...options,
			config: ConfigUtils.mergeConfig(accessibilityConfig, options.config || {})
		};

		return new DataSourceVisualizationNetwork(containerId, data, accessibleOptions);
	}
}

/**
 * Legacy API Functions (for backward compatibility)
 */

/**
 * Initialize visualization (legacy function)
 */
export async function initializeVisualization(id: string, key: string): Promise<void> {
	if (!id) {
		console.error('ID is required for visualization initialization');
		return;
	}

	// Show loading overlay if available
	if (typeof window !== 'undefined' && window.Overlay?.showWait) {
		window.Overlay.showWait('Loading');
	}

	const container = document.getElementById('visualizationChartContainer');
	if (!container) {
		console.error('Visualization container not found');
		return;
	}

	/**
	 * Legacy API Functions (for backward compatibility)
	 */

	/**
	 * Initialize visualization (legacy function)
	 */
	export async function initializeVisualization(id: string, key: string): Promise<void> {
		if (!id) {
			console.error('ID is required for visualization initialization');
			return;
		}

		// Show loading overlay if available
		if (typeof window !== 'undefined' && window.Overlay?.showWait) {
			window.Overlay.showWait('Loading');
		}

		const container = document.getElementById('visualizationChartContainer');
		if (!container) {
			console.error('Visualization container not found');
			return;
		}

		// Store key globally for compatibility
		if (typeof window !== 'undefined') {
			window.key = key;
		}

		try {
			// Fetch data from API
			const response = await fetch(`/api/${key}/${id}/DataNetwork`);
			if (!response.ok) {
				throw new Error(`HTTP error! status: ${response.status}`);
			}

			const data = await response.json();

			// Validate data
			if (!TypeGuards.isDataNetwork(data)) {
				throw new Error('Invalid data received from API');
			}

			// Hide loading overlay
			if (typeof window !== 'undefined' && window.Overlay?.hideWait) {
				await window.Overlay.hideWait();
			}

			// Destroy existing visualization if present
			const existingInstance = (window as any).currentVisualizationInstance;
			if (existingInstance) {
				existingInstance.destroy();
			}

			// Create new visualization
			const visualization = new DataSourceVisualizationNetwork('visualizationChartContainer', data, {
				config: {
					dataSourceType: key as 'pages' | 'dependantSources'
				}
			});

			// Hide loading element
			const loadingElement = container.querySelector('.loading');
			if (loadingElement) {
				loadingElement.classList.add('hide');
			}

		} catch (error) {
			console.error('Error initializing visualization:', error);

			// Hide loading overlay and element
			if (typeof window !== 'undefined' && window.Overlay?.hideWait) {
				await window.Overlay.hideWait();
			}

			const loadingElement = container?.querySelector('.loading');
			if (loadingElement) {
				loadingElement.classList.add('hide');
			}

			// Show error message
			if (container) {
				container.innerHTML = `
        <div class="error-message" style="
          display: flex; 
          align-items: center; 
          justify-content: center; 
          height: 100%; 
          color: var(--clr-red-600);
          font-family: var(--font-family);
        ">
          <div style="text-align: center;">
            <h3>Visualization Error</h3>
            <p>Failed to load visualization data. Please try again.</p>
          </div>
        </div>
      `;
			}

			throw error;
		}
	}

	/**
	 * Refresh filter panel (legacy function)
	 */
	export async function refreshFilterPanel(urlKey: string = "DataVisualization"): Promise<void> {
		const filterPanel = document.getElementById('data-visualization-filter') as HTMLElement & {
			url?: string;
			reload?: () => void;
		};

		if (!filterPanel) return;

		try {
			// Show loading state
			filterPanel.setAttribute('skeleton', 'true');

			// Get current data source ID
			const dataSourceId = (typeof window !== 'undefined' && window.visualizationMainDatasource)
				? window.visualizationMainDatasource.id
				: null;

			if (!dataSourceId) {
				console.warn('No main data source found for filter refresh');
				return;
			}

			// Create base filters
			const baseFilters = [{
				FilterColumn: 'filterByDataSourceId',
				Operator: 'Equals',
				CompareValue: dataSourceId
			}];

			// Make API call to get updated data
			const response = await fetch(`/Api/${urlKey}/?filters=${encodeURIComponent(JSON.stringify(baseFilters))}`);
			if (!response.ok) {
				throw new Error(`API request failed with status ${response.status}`);
			}

			const data = await response.json();

			// Update the filter panel with new data
			if (data?.rows || data?.data?.rows) {
				// Update filter panel's internal data
				filterPanel.url = `/Api/${urlKey}/?filters=${encodeURIComponent(JSON.stringify(baseFilters))}`;

				// If the component has a reload method, use it
				if (typeof filterPanel.reload === 'function') {
					filterPanel.reload();
				} else {
					// Force refresh by dispatching a custom event
					filterPanel.dispatchEvent(new CustomEvent('refresh-data', {
						detail: { data: data.rows || data.data.rows }
					}));
				}
			}
		} catch (error) {
			console.error('Error refreshing filter panel:', error);
		} finally {
			// Remove loading state
			filterPanel.removeAttribute('skeleton');
		}
	}

	/**
	 * Export legacy functions to global scope
	 */
	if (typeof window !== 'undefined') {
		window.initializeVisualization = initializeVisualization;
		window.refreshFilterPanel = refreshFilterPanel;
	}

	/**
	 * Module Exports
	 */
	export {
		DataSourceVisualizationNetwork as default,
		initializeVisualization,
		refreshFilterPanel
	};

	// Named exports for ES6 modules
	export {
		DataSourceVisualizationNetwork,
		type VisualizationOptions
	};