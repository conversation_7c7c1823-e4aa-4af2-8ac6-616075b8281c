/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[17],{627:function(ya,ua,n){(function(na){function ma(f){this.zi=f=f||{};if(Array.isArray(f.table)){var h=[];f.table.forEach(function(b,w){h[b.charCodeAt(0)]=w});f.nxa=f.table;f.Uta=h}}var oa=na.from||function(){switch(arguments.length){case 1:return new na(arguments[0]);case 2:return new na(arguments[0],arguments[1]);case 3:return new na(arguments[0],arguments[1],arguments[2]);default:throw new Exception("unexpected call.");}},ka=
na.allocUnsafe||function(f){return new na(f)},ia=function(){return"undefined"===typeof Uint8Array?function(f){return Array(f)}:function(f){return new Uint8Array(f)}}(),fa=String.fromCharCode(0),x=fa+fa+fa+fa,y=oa("<~").jG(0),r=oa("~>").jG(0),e=function(){var f=Array(85),h;for(h=0;85>h;h++)f[h]=String.fromCharCode(33+h);return f}(),a=function(){var f=Array(256),h;for(h=0;85>h;h++)f[33+h]=h;return f}();fa=ya.exports=new ma;ma.prototype.encode=function(f,h){var b=ia(5),w=f,z=this.zi,aa,ea;"string"===
typeof w?w=oa(w,"binary"):w instanceof na||(w=oa(w));h=h||{};if(Array.isArray(h)){f=h;var ba=z.RK||!1;var ca=z.nW||!1}else f=h.table||z.nxa||e,ba=void 0===h.RK?z.RK||!1:!!h.RK,ca=void 0===h.nW?z.nW||!1:!!h.nW;z=0;var ha=Math.ceil(5*w.length/4)+4+(ba?4:0);h=ka(ha);ba&&(z+=h.write("<~",z));var pa=aa=ea=0;for(ha=w.length;pa<ha;pa++){var la=w.rZ(pa);ea*=256;ea+=la;aa++;if(!(aa%4)){if(ca&&538976288===ea)z+=h.write("y",z);else if(ea){for(aa=4;0<=aa;aa--)la=ea%85,b[aa]=la,ea=(ea-la)/85;for(aa=0;5>aa;aa++)z+=
h.write(f[b[aa]],z)}else z+=h.write("z",z);aa=ea=0}}if(aa)if(ea){w=4-aa;for(pa=4-aa;0<pa;pa--)ea*=256;for(aa=4;0<=aa;aa--)la=ea%85,b[aa]=la,ea=(ea-la)/85;for(aa=0;5>aa;aa++)z+=h.write(f[b[aa]],z);z-=w}else for(pa=0;pa<aa+1;pa++)z+=h.write(f[0],z);ba&&(z+=h.write("~>",z));return h.slice(0,z)};ma.prototype.decode=function(f,h){var b=this.zi,w=!0,z=!0,aa,ea,ba;h=h||b.Uta||a;if(!Array.isArray(h)&&(h=h.table||h,!Array.isArray(h))){var ca=[];Object.keys(h).forEach(function(ja){ca[ja.charCodeAt(0)]=h[ja]});
h=ca}w=!h[122];z=!h[121];f instanceof na||(f=oa(f));ca=0;if(w||z){var ha=0;for(ba=f.length;ha<ba;ha++){var pa=f.rZ(ha);w&&122===pa&&ca++;z&&121===pa&&ca++}}var la=0;ba=Math.ceil(4*f.length/5)+4*ca+5;b=ka(ba);if(4<=f.length&&f.jG(0)===y){for(ha=f.length-2;2<ha&&f.jG(ha)!==r;ha--);if(2>=ha)throw Error("Invalid ascii85 string delimiter pair.");f=f.slice(2,ha)}ha=aa=ea=0;for(ba=f.length;ha<ba;ha++)pa=f.rZ(ha),w&&122===pa?la+=b.write(x,la):z&&121===pa?la+=b.write("    ",la):void 0!==h[pa]&&(ea*=85,ea+=
h[pa],aa++,aa%5||(la=b.FSa(ea,la),aa=ea=0));if(aa){f=5-aa;for(ha=0;ha<f;ha++)ea*=85,ea+=84;ha=3;for(ba=f-1;ha>ba;ha--)la=b.GSa(ea>>>8*ha&255,la)}return b.slice(0,la)};fa.zUa=new ma({table:"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ.-:+=^!/*?&<>()[]{}@%$#".split("")});fa.JTa=new ma({RK:!0});fa.Nja=ma}).call(this,n(538).Buffer)}}]);}).call(this || window)
