/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[14],{624:function(ya,ua,n){n.r(ua);var na=n(0),ma=n(358);ya=n(614);var oa=n(124);n=n(537);var ka={},ia=function(fa){function x(y,r){var e=fa.call(this,y,r)||this;e.url=y;e.range=r;e.status=ma.a.NOT_STARTED;return e}Object(na.c)(x,fa);x.prototype.start=function(y){var r=this;"undefined"===typeof ka[this.range.start]&&(ka[this.range.start]={f0:function(e){var a=atob(e),f,h=a.length;e=new Uint8Array(h);for(f=0;f<h;++f)e[f]=a.charCodeAt(f);
a=e.length;f="";for(var b=0;b<a;)h=e.subarray(b,b+1024),b+=1024,f+=String.fromCharCode.apply(null,h);r.f0(f,y)},$Va:function(){r.status=ma.a.ERROR;y({code:r.status})}},window.external.notify(this.url),this.status=ma.a.STARTED);r.fM()};return x}(ya.ByteRangeRequest);ya=function(fa){function x(y,r,e,a){y=fa.call(this,y,e,a)||this;y.hG=ia;return y}Object(na.c)(x,fa);x.prototype.jD=function(y,r){return"".concat(y,"?").concat(r.start,"&").concat(r.stop?r.stop:"")};return x}(oa.a);Object(n.a)(ya);Object(n.b)(ya);
ua["default"]=ya}}]);}).call(this || window)
