/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[12],{626:function(ya,ua,n){n.r(ua);var na=n(0),ma=n(1);n.n(ma);ya=n(124);n=n(537);ya=function(oa){function ka(ia,fa,x){fa=oa.call(this,ia,fa,x)||this;fa.db=ia;return fa}Object(na.c)(ka,oa);ka.prototype.request=function(ia){var fa=this;Object(ma.each)(ia,function(x){fa.db.get(x,function(y,r,e){return y?fa.trigger("partReady.partRetriever",{Rb:x,error:y}):fa.trigger("partReady.partRetriever",{Rb:x,data:r,$l:!1,Fi:!1,error:null,Ie:e})})})};
ka.prototype.gA=function(ia){ia()};return ka}(ya.a);Object(n.a)(ya);Object(n.b)(ya);ua["default"]=ya}}]);}).call(this || window)
