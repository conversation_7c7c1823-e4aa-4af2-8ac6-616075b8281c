{"version": 3, "sources": ["webpack:///./src/ui/src/components/ZoomOverlay/ZoomOverlayContainer.js", "webpack:///./src/ui/src/components/ZoomOverlay/index.js"], "names": ["ZoomOverlayContainer", "dispatch", "useDispatch", "t", "useTranslation", "activeDocumentViewerKey", "useSelector", "state", "selectors", "getActiveDocumentViewerKey", "FlyoutMenu", "menu", "DataElements", "ZOOM_OVERLAY", "trigger", "ZOOM_OVERLAY_BUTTON", "aria<PERSON><PERSON><PERSON>", "ZoomOverlay", "zoomList", "getZoomList", "currentZoomLevel", "getZoom", "isReaderMode", "isMarqueeZoomActive", "getActiveToolName", "isMarqueeToolButtonDisabled", "isElementDisabled", "fitToWidth", "fitToPage", "onClickZoomLevelOption", "zoomLevel", "zoomTo", "actions", "closeElements", "onClickMarqueeZoom"], "mappings": "2xCA+CeA,MArCf,WACE,IAAMC,EAAWC,cACVC,EAAqB,EAAhBC,cAAgB,GAApB,GACDC,EAEL,EAFgCC,aAAY,SAACC,GAAK,MAAK,CACvDC,IAAUC,2BAA2BF,OACrC,GAF4B,GAa9B,OACE,kBAACG,EAAA,EAAU,CACTC,KAAMC,IAAaC,aACnBC,QAASF,IAAaG,oBACtBC,UAAWb,EAAE,0BAEb,kBAACc,EAAA,EAAW,CACVC,SAAUZ,YAAYE,IAAUW,aAChCC,iBAAkBd,YAAYE,IAAUa,SACxCC,aAAchB,YAAYE,IAAUc,cACpCC,oBAAkE,oBAA7CjB,YAAYE,IAAUgB,mBAC3CC,4BAA6BnB,aAAY,SAACC,GAAK,OAAKC,IAAUkB,kBAAkBnB,EAAO,wBACvFoB,WAAY,kBAAMA,YAAWtB,IAC7BuB,UAAW,kBAAMA,YAAUvB,IAC3BwB,uBAvBN,SAAgCC,GAC9BC,YAAOD,GACP7B,EAAS+B,IAAQC,cAAc,CAACrB,IAAaC,iBAsBzCqB,mBAnBN,WACEjC,EAAS+B,IAAQC,cAAc,CAACrB,IAAaC,qBCrBlCI", "file": "chunks/chunk.98.js", "sourcesContent": ["import React from 'react';\nimport actions from 'actions';\nimport selectors from 'selectors';\nimport { zoomTo, fitToPage, fitToWidth } from 'helpers/zoom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\nimport FlyoutMenu from 'components/FlyoutMenu/FlyoutMenu';\nimport ZoomOverlay from './ZoomOverlay';\nimport DataElements from 'src/constants/dataElement';\n\nfunction ZoomOverlayContainer() {\n  const dispatch = useDispatch();\n  const [t] = useTranslation();\n  const [activeDocumentViewerKey] = useSelector((state) => [\n    selectors.getActiveDocumentViewerKey(state),\n  ]);\n\n  function onClickZoomLevelOption(zoomLevel) {\n    zoomTo(zoomLevel);\n    dispatch(actions.closeElements([DataElements.ZOOM_OVERLAY]));\n  }\n\n  function onClickMarqueeZoom() {\n    dispatch(actions.closeElements([DataElements.ZOOM_OVERLAY]));\n  }\n\n  return (\n    <FlyoutMenu\n      menu={DataElements.ZOOM_OVERLAY}\n      trigger={DataElements.ZOOM_OVERLAY_BUTTON}\n      ariaLabel={t('component.zoomOverlay')}\n    >\n      <ZoomOverlay\n        zoomList={useSelector(selectors.getZoomList)}\n        currentZoomLevel={useSelector(selectors.getZoom)}\n        isReaderMode={useSelector(selectors.isReaderMode)}\n        isMarqueeZoomActive={useSelector(selectors.getActiveToolName) === 'MarqueeZoomTool'}\n        isMarqueeToolButtonDisabled={useSelector((state) => selectors.isElementDisabled(state, 'marqueeToolButton'))}\n        fitToWidth={() => fitToWidth(activeDocumentViewerKey)}\n        fitToPage={() => fitToPage(activeDocumentViewerKey)}\n        onClickZoomLevelOption={onClickZoomLevelOption}\n        onClickMarqueeZoom={onClickMarqueeZoom}\n      />\n    </FlyoutMenu>\n  );\n}\n\nexport default ZoomOverlayContainer;\n", "import ZoomOverlay from './ZoomOverlayContainer';\n\nexport default ZoomOverlay;\n"], "sourceRoot": ""}