{"version": 3, "sources": ["webpack:///./src/ui/node_modules/react-lifecycles-compat/react-lifecycles-compat.es.js", "webpack:///./src/ui/node_modules/dom-helpers/esm/canUseDOM.js", "webpack:///./src/ui/node_modules/dom-helpers/esm/scrollbarSize.js"], "names": ["componentWillMount", "state", "this", "constructor", "getDerivedStateFromProps", "props", "setState", "componentWillReceiveProps", "nextProps", "prevState", "bind", "componentWillUpdate", "nextState", "prevProps", "__reactInternalSnapshotFlag", "__reactInternalSnapshot", "getSnapshotBeforeUpdate", "polyfill", "Component", "prototype", "isReactComponent", "Error", "foundWillMountName", "foundWillReceivePropsName", "foundWillUpdateName", "UNSAFE_componentWillMount", "UNSAFE_componentWillReceiveProps", "UNSAFE_componentWillUpdate", "componentName", "displayName", "name", "newApiName", "componentDidUpdate", "maybeSnapshot", "snapshot", "call", "__suppressDeprecationWarning", "size", "scrollbarSize", "recalc", "canUseDOM", "scrollDiv", "document", "createElement", "style", "position", "top", "width", "height", "overflow", "body", "append<PERSON><PERSON><PERSON>", "offsetWidth", "clientWidth", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": "2FAOA,SAASA,IAEP,IAAIC,EAAQC,KAAKC,YAAYC,yBAAyBF,KAAKG,MAAOH,KAAKD,OACnEA,SACFC,KAAKI,SAASL,GAIlB,SAASM,EAA0BC,GAQjCN,KAAKI,SALL,SAAiBG,GACf,IAAIR,EAAQC,KAAKC,YAAYC,yBAAyBI,EAAWC,GACjE,OAAOR,QAAwCA,EAAQ,MAGnCS,KAAKR,OAG7B,SAASS,EAAoBH,EAAWI,GACtC,IACE,IAAIC,EAAYX,KAAKG,MACjBI,EAAYP,KAAKD,MACrBC,KAAKG,MAAQG,EACbN,KAAKD,MAAQW,EACbV,KAAKY,6BAA8B,EACnCZ,KAAKa,wBAA0Bb,KAAKc,wBAClCH,EACAJ,GAEF,QACAP,KAAKG,MAAQQ,EACbX,KAAKD,MAAQQ,GAUjB,SAASQ,EAASC,GAChB,IAAIC,EAAYD,EAAUC,UAE1B,IAAKA,IAAcA,EAAUC,iBAC3B,MAAM,IAAIC,MAAM,sCAGlB,GACgD,mBAAvCH,EAAUd,0BAC4B,mBAAtCe,EAAUH,wBAEjB,OAAOE,EAMT,IAAII,EAAqB,KACrBC,EAA4B,KAC5BC,EAAsB,KAgB1B,GAf4C,mBAAjCL,EAAUnB,mBACnBsB,EAAqB,qBACmC,mBAAxCH,EAAUM,4BAC1BH,EAAqB,6BAE4B,mBAAxCH,EAAUZ,0BACnBgB,EAA4B,4BACmC,mBAA/CJ,EAAUO,mCAC1BH,EAA4B,oCAEe,mBAAlCJ,EAAUR,oBACnBa,EAAsB,sBACmC,mBAAzCL,EAAUQ,6BAC1BH,EAAsB,8BAGC,OAAvBF,GAC8B,OAA9BC,GACwB,OAAxBC,EACA,CACA,IAAII,EAAgBV,EAAUW,aAAeX,EAAUY,KACnDC,EAC4C,mBAAvCb,EAAUd,yBACb,6BACA,4BAEN,MAAMiB,MACJ,2FACEO,EACA,SACAG,EACA,uDACwB,OAAvBT,EAA8B,OAASA,EAAqB,KAC9B,OAA9BC,EACG,OAASA,EACT,KACqB,OAAxBC,EAA+B,OAASA,EAAsB,IATjE,wIA0BJ,GARkD,mBAAvCN,EAAUd,2BACnBe,EAAUnB,mBAAqBA,EAC/BmB,EAAUZ,0BAA4BA,GAMS,mBAAtCY,EAAUH,wBAAwC,CAC3D,GAA4C,mBAAjCG,EAAUa,mBACnB,MAAM,IAAIX,MACR,qHAIJF,EAAUR,oBAAsBA,EAEhC,IAAIqB,EAAqBb,EAAUa,mBAEnCb,EAAUa,mBAAqB,SAC7BnB,EACAJ,EACAwB,GAUA,IAAIC,EAAWhC,KAAKY,4BAChBZ,KAAKa,wBACLkB,EAEJD,EAAmBG,KAAKjC,KAAMW,EAAWJ,EAAWyB,IAIxD,OAAOhB,EA1JT,gDA6CAlB,EAAmBoC,8BAA+B,EAClD7B,EAA0B6B,8BAA+B,EACzDzB,EAAoByB,8BAA+B,G,iFC/CpC,ICCXC,EDDW,kFCEA,SAASC,EAAcC,GACpC,KAAKF,GAAiB,IAATA,GAAcE,IACrBC,EAAW,CACb,IAAIC,EAAYC,SAASC,cAAc,OACvCF,EAAUG,MAAMC,SAAW,WAC3BJ,EAAUG,MAAME,IAAM,UACtBL,EAAUG,MAAMG,MAAQ,OACxBN,EAAUG,MAAMI,OAAS,OACzBP,EAAUG,MAAMK,SAAW,SAC3BP,SAASQ,KAAKC,YAAYV,GAC1BJ,EAAOI,EAAUW,YAAcX,EAAUY,YACzCX,SAASQ,KAAKI,YAAYb,GAI9B,OAAOJ", "file": "chunks/chunk.8.js", "sourcesContent": ["/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nfunction componentWillMount() {\n  // Call this.constructor.gDSFP to support sub-classes.\n  var state = this.constructor.getDerivedStateFromProps(this.props, this.state);\n  if (state !== null && state !== undefined) {\n    this.setState(state);\n  }\n}\n\nfunction componentWillReceiveProps(nextProps) {\n  // Call this.constructor.gDSFP to support sub-classes.\n  // Use the setState() updater to ensure state isn't stale in certain edge cases.\n  function updater(prevState) {\n    var state = this.constructor.getDerivedStateFromProps(nextProps, prevState);\n    return state !== null && state !== undefined ? state : null;\n  }\n  // Binding \"this\" is important for shallow renderer support.\n  this.setState(updater.bind(this));\n}\n\nfunction componentWillUpdate(nextProps, nextState) {\n  try {\n    var prevProps = this.props;\n    var prevState = this.state;\n    this.props = nextProps;\n    this.state = nextState;\n    this.__reactInternalSnapshotFlag = true;\n    this.__reactInternalSnapshot = this.getSnapshotBeforeUpdate(\n      prevProps,\n      prevState\n    );\n  } finally {\n    this.props = prevProps;\n    this.state = prevState;\n  }\n}\n\n// React may warn about cWM/cWRP/cWU methods being deprecated.\n// Add a flag to suppress these warnings for this special case.\ncomponentWillMount.__suppressDeprecationWarning = true;\ncomponentWillReceiveProps.__suppressDeprecationWarning = true;\ncomponentWillUpdate.__suppressDeprecationWarning = true;\n\nfunction polyfill(Component) {\n  var prototype = Component.prototype;\n\n  if (!prototype || !prototype.isReactComponent) {\n    throw new Error('Can only polyfill class components');\n  }\n\n  if (\n    typeof Component.getDerivedStateFromProps !== 'function' &&\n    typeof prototype.getSnapshotBeforeUpdate !== 'function'\n  ) {\n    return Component;\n  }\n\n  // If new component APIs are defined, \"unsafe\" lifecycles won't be called.\n  // Error if any of these lifecycles are present,\n  // Because they would work differently between older and newer (16.3+) versions of React.\n  var foundWillMountName = null;\n  var foundWillReceivePropsName = null;\n  var foundWillUpdateName = null;\n  if (typeof prototype.componentWillMount === 'function') {\n    foundWillMountName = 'componentWillMount';\n  } else if (typeof prototype.UNSAFE_componentWillMount === 'function') {\n    foundWillMountName = 'UNSAFE_componentWillMount';\n  }\n  if (typeof prototype.componentWillReceiveProps === 'function') {\n    foundWillReceivePropsName = 'componentWillReceiveProps';\n  } else if (typeof prototype.UNSAFE_componentWillReceiveProps === 'function') {\n    foundWillReceivePropsName = 'UNSAFE_componentWillReceiveProps';\n  }\n  if (typeof prototype.componentWillUpdate === 'function') {\n    foundWillUpdateName = 'componentWillUpdate';\n  } else if (typeof prototype.UNSAFE_componentWillUpdate === 'function') {\n    foundWillUpdateName = 'UNSAFE_componentWillUpdate';\n  }\n  if (\n    foundWillMountName !== null ||\n    foundWillReceivePropsName !== null ||\n    foundWillUpdateName !== null\n  ) {\n    var componentName = Component.displayName || Component.name;\n    var newApiName =\n      typeof Component.getDerivedStateFromProps === 'function'\n        ? 'getDerivedStateFromProps()'\n        : 'getSnapshotBeforeUpdate()';\n\n    throw Error(\n      'Unsafe legacy lifecycles will not be called for components using new component APIs.\\n\\n' +\n        componentName +\n        ' uses ' +\n        newApiName +\n        ' but also contains the following legacy lifecycles:' +\n        (foundWillMountName !== null ? '\\n  ' + foundWillMountName : '') +\n        (foundWillReceivePropsName !== null\n          ? '\\n  ' + foundWillReceivePropsName\n          : '') +\n        (foundWillUpdateName !== null ? '\\n  ' + foundWillUpdateName : '') +\n        '\\n\\nThe above lifecycles should be removed. Learn more about this warning here:\\n' +\n        'https://fb.me/react-async-component-lifecycle-hooks'\n    );\n  }\n\n  // React <= 16.2 does not support static getDerivedStateFromProps.\n  // As a workaround, use cWM and cWRP to invoke the new static lifecycle.\n  // Newer versions of React will ignore these lifecycles if gDSFP exists.\n  if (typeof Component.getDerivedStateFromProps === 'function') {\n    prototype.componentWillMount = componentWillMount;\n    prototype.componentWillReceiveProps = componentWillReceiveProps;\n  }\n\n  // React <= 16.2 does not support getSnapshotBeforeUpdate.\n  // As a workaround, use cWU to invoke the new lifecycle.\n  // Newer versions of React will ignore that lifecycle if gSBU exists.\n  if (typeof prototype.getSnapshotBeforeUpdate === 'function') {\n    if (typeof prototype.componentDidUpdate !== 'function') {\n      throw new Error(\n        'Cannot polyfill getSnapshotBeforeUpdate() for components that do not define componentDidUpdate() on the prototype'\n      );\n    }\n\n    prototype.componentWillUpdate = componentWillUpdate;\n\n    var componentDidUpdate = prototype.componentDidUpdate;\n\n    prototype.componentDidUpdate = function componentDidUpdatePolyfill(\n      prevProps,\n      prevState,\n      maybeSnapshot\n    ) {\n      // 16.3+ will not execute our will-update method;\n      // It will pass a snapshot value to did-update though.\n      // Older versions will require our polyfilled will-update value.\n      // We need to handle both cases, but can't just check for the presence of \"maybeSnapshot\",\n      // Because for <= 15.x versions this might be a \"prevContext\" object.\n      // We also can't just check \"__reactInternalSnapshot\",\n      // Because get-snapshot might return a falsy value.\n      // So check for the explicit __reactInternalSnapshotFlag flag to determine behavior.\n      var snapshot = this.__reactInternalSnapshotFlag\n        ? this.__reactInternalSnapshot\n        : maybeSnapshot;\n\n      componentDidUpdate.call(this, prevProps, prevState, snapshot);\n    };\n  }\n\n  return Component;\n}\n\nexport { polyfill };\n", "export default !!(typeof window !== 'undefined' && window.document && window.document.createElement);", "import canUseDOM from './canUseDOM';\nvar size;\nexport default function scrollbarSize(recalc) {\n  if (!size && size !== 0 || recalc) {\n    if (canUseDOM) {\n      var scrollDiv = document.createElement('div');\n      scrollDiv.style.position = 'absolute';\n      scrollDiv.style.top = '-9999px';\n      scrollDiv.style.width = '50px';\n      scrollDiv.style.height = '50px';\n      scrollDiv.style.overflow = 'scroll';\n      document.body.appendChild(scrollDiv);\n      size = scrollDiv.offsetWidth - scrollDiv.clientWidth;\n      document.body.removeChild(scrollDiv);\n    }\n  }\n\n  return size;\n}"], "sourceRoot": ""}