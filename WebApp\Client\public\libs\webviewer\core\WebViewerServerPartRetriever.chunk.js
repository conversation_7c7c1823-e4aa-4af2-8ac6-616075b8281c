/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[13],{625:function(ya,ua,n){function na(){return!1}function ma(z,aa,ea){if(!(aa in w))return!0;aa=w[aa];for(var ba=0;ba<aa.length;ba++){var ca=z;var ha=aa[ba];var pa=ea;if(ha.name in ca){var la="",ja=!1;ca=ca[ha.name];switch(ha.type){case "s":la="String";ja=Object(ia.isString)(ca);break;case "a":la="Array";ja=Object(ia.isArray)(ca);break;case "n":la="Number";ja=Object(ia.isNumber)(ca)&&Object(ia.isFinite)(ca);break;case "o":la="Object",
ja=Object(ia.isObject)(ca)&&!Object(ia.isArray)(ca)}ja||pa.reject('Expected response field "'.concat(ha.name,'" to have type ').concat(la));ha=ja}else pa.reject('Response missing field "'.concat(ha.name,'"')),ha=!1;if(!ha)return!1}return!0}function oa(z){for(var aa=0,ea=["locale","excelMaxAllowedCellCount","applyPageBreaksToSheet","excelDefaultCellBorderWidth","displayChangeTracking"];aa<ea.length;aa++){var ba=ea[aa],ca=ba;ba=ba.charAt(0).toUpperCase()+ba.slice(1);z[ca]&&(Object.defineProperty(z,
ba,Object.getOwnPropertyDescriptor(z,ca)),delete z[ca])}return z}n.r(ua);var ka=n(0),ia=n(1);n.n(ia);var fa=n(2);ya=n(53);var x=n(36),y=n(644),r=n(124),e=n(537),a=n(51),f=n(237),h=function(){function z(){this.request=this.result=null;this.state=0;var aa=this;aa.promise=new Promise(function(ea,ba){aa.resolve=function(){if(0===aa.state||4===aa.state)aa.state=1,aa.result=arguments[0],ea.apply(null,arguments)};aa.reject=function(){if(0===aa.state||4===aa.state)aa.state=2,ba.apply(null,arguments)}})}z.prototype.Zz=
function(){return 1===(this.state&1)};z.prototype.oFa=function(){return 2===(this.state&2)};z.prototype.Am=function(){return!this.oFa()&&!this.Zz()};z.prototype.DEa=function(){return 4===(this.state&4)};z.prototype.t_=function(){this.state|=4};return z}(),b=function(){function z(){this.Lz={};this.Pc=[]}z.prototype.pop=function(){var aa=this.Pc.pop();this.Lz[aa.key]=void 0;return aa};z.prototype.push=function(aa,ea){ea={key:aa,data:ea};this.Pc.push(ea);this.Lz[aa]=ea.data};z.prototype.contains=function(aa){return!!this.Lz[aa]};
z.prototype.get=function(aa){return this.Lz[aa]};z.prototype.set=function(aa,ea){var ba=this;this.Lz[aa]=ea;this.Pc.forEach(function(ca,ha){ca.key===aa&&(ba.Pc[ha]=ca)})};z.prototype.remove=function(aa){var ea=this;this.Lz[aa]=void 0;this.Pc.forEach(function(ba,ca){ba.key===aa&&ea.Pc.splice(ca,1)})};z.prototype.length=function(){return this.Pc.length};return z}(),w={pages:[{name:"pages",type:"a"}],pdf:[{name:"url",type:"s"}],docmod:[{name:"url",type:"s"},{name:"rID",type:"s"}],health:[],tiles:[{name:"z",
type:"n"},{name:"rID",type:"n"},{name:"tiles",type:"a"},{name:"size",type:"n"}],cAnnots:[{name:"annots",type:"a"}],annots:[{name:"url",type:"s"},{name:"name",type:"s"}],image:[{name:"url",type:"s"},{name:"name",type:"s"},{name:"p",type:"n"}],text:[{name:"url",type:"s"},{name:"name",type:"s"},{name:"p",type:"n"}],ApString2Xod:[{name:"url",type:"s"},{name:"rID",type:"s"}]};n=function(){function z(aa,ea,ba){var ca=this;this.Y_=this.K7=!1;this.Tj=this.TP=this.AB=this.zg=this.VC=this.vt=this.UC=this.wr=
null;this.Oq=new h;this.fv=new h;this.EK=!1;this.Sh=this.Eg=this.Fg=this.Gh=null;this.si=[];this.zL=[];this.cache={};this.timeStamp=0;this.Pj=[];this.Km=[];this.CU=null;this.w7=!1;this.jga=this.id=null;this.NX=this.raa=na;this.hi=0;this.sW=!1;this.Ica=1;this.Gm={};this.jy=0;this.HA=new b;ea.endsWith("/")||(ea+="/");ba=ba||{};this.K7=ba.disableWebsockets||!1;this.Y_=ba.singleServerMode||!1;null!=ba.customQueryParameters&&Object(a.b)("wvsQueryParameters",ba.customQueryParameters);ea.endsWith("blackbox/")||
(ea+="blackbox/");this.wr=ba.uploadData||null;this.AB=ba.uriData||null;this.UC=ba.cacheKey||null;if(this.vt=ba.officeOptions||null)this.vt=oa(this.vt);this.zg=ba.rasterizerOptions||null;this.VC=ba.cadOptions||null;this.Zg=ea;this.bU=aa;this.Rt(!0);this.Fy=(new y.a(ea,null,this.Pk())).yya(!this.K7,function(ha){ca.LHa(ha)},function(){return null},function(){ca.EK=!1},function(){ca.fMa()})}z.prototype.oua=function(){var aa=this;return new Promise(function(ea,ba){var ca=new XMLHttpRequest,ha="".concat(aa.Zg,
"ck");ca.open("GET",ha);ca.withCredentials=aa.Pk();ca.onreadystatechange=function(){ca.readyState===XMLHttpRequest.DONE&&(200===ca.status?ea():ba())};ca.send()})};z.prototype.iOa=function(aa){this.raa=aa||na;this.NX=na};z.prototype.psa=function(){this.zfa();return this.Fy.zv()};z.prototype.zfa=function(){Object(ka.b)(this,void 0,void 0,function(){return Object(ka.d)(this,function(aa){switch(aa.label){case 0:return this.fv=new h,this.Oq=new h,this.EK=!1,this.id=null,this.w7=!1,[4,this.oua().catch(function(){})];
case 1:return aa.aa(),[2]}})})};z.prototype.fMa=function(){this.raa();this.zfa();this.Gh&&(this.Gh.Am()?this.Yj(this.Gh.request):this.Gh.Zz()&&this.NX(this.Gh.result.url,"pdf")&&(this.Gh=null,this.wfa()));this.Sh&&this.Sh.Am()&&this.Yj(this.Sh.request);this.Fg&&this.Fg.Am()?this.Yj(this.Fg.request):this.Eg&&this.Eg.Am()&&this.T$();var aa;for(aa=0;aa<this.Pj.length;aa++)this.Pj[aa]&&(this.Pj[aa].Am()?this.Yj(this.Pj[aa].request):this.Pj[aa].Zz()&&this.NX(this.Pj[aa].result.url,"image")&&(this.Pj[aa]=
null,this.BO(Object(ia.uniqueId)(),aa)));for(aa=0;aa<this.Km.length;aa++)this.Km[aa]&&this.Km[aa].Am()&&!this.Km[aa].DEa()&&this.Yj(this.Km[aa].request);for(aa=0;aa<this.si.length;aa++)this.si[aa]&&this.si[aa].Am()&&this.Yj(this.si[aa].request)};z.prototype.KDa=function(){return this.EK?Promise.resolve():(this.EK=!0,this.timeStamp=Date.now(),this.Fy.SM())};z.prototype.eSa=function(){var aa=this,ea,ba,ca,ha,pa;return new Promise(function(la,ja){if(aa.wr)ea=new FormData,ea.append("file",aa.wr.fileHandle,
aa.wr.fileHandle.name),aa.vt&&ea.append("officeOptions",JSON.stringify(aa.vt)),aa.zg&&ea.append("rasterizerOptions",JSON.stringify(aa.zg)),aa.VC&&ea.append("cadOptions",aa.VC.getJsonString()),aa.UC&&ea.append("cacheKey",aa.UC),ba=aa.wr.loadCallback,ha="upload",ca=aa.wr.extension;else if(aa.AB)ea={uri:aa.AB.uri,BXa:aa.AB.shareId},ea=Object.keys(ea).map(function(sa){return"".concat(sa,"=").concat(ea[sa]?encodeURIComponent(ea[sa]):"")}).join("&"),pa="application/x-www-form-urlencoded; charset=UTF-8",
ba=aa.AB.loadCallback,ha="url",ca=aa.AB.extension;else{la();return}var qa=new XMLHttpRequest,ra=Object(x.l)(aa.Zg,"AuxUpload");ra=Object(f.a)(ra,{type:ha,ext:ca});qa.open("POST",ra);qa.withCredentials=aa.Pk();pa&&qa.setRequestHeader("Content-Type",pa);qa.addEventListener("load",function(){if(qa.readyState===qa.DONE&&200===qa.status){var sa=JSON.parse(qa.response);aa.bU=sa.uri;ba(sa);la(sa)}});qa.addEventListener("error",function(){ja("".concat(qa.statusText," ").concat(JSON.stringify(qa)))});aa.wr&&
null!=aa.wr.onProgress&&(qa.upload.onprogress=function(sa){aa.wr.onProgress(sa)});qa.send(ea)})};z.prototype.SLa=function(aa){this.password=aa||null;this.Oq.Zz()||(this.Oq=new h,this.Yj({t:"pages"}));return this.Oq.promise};z.prototype.vG=function(aa){this.CU=aa||null;this.Oq.Zz()||this.Yj({t:"pages"});return this.Oq.promise};z.prototype.fD=function(aa){aa=Object.assign(aa,{uri:encodeURIComponent(this.bU)});this.CU&&(aa.ext=this.CU);this.Tj&&(aa.c=this.Tj);this.password&&(aa.pswd=this.password);this.UC&&
(aa.cacheKey=this.UC);this.vt&&(aa.officeOptions=this.vt);this.zg&&(aa.rastOptions=this.zg);this.VC&&(aa.cadOptions=this.VC.mImpl);return aa};z.prototype.PMa=function(){0<this.HA.length()&&10>=this.jy&&this.QMa(this.HA.pop().data)};z.prototype.Ara=function(aa){0<this.HA.length()&&this.HA.contains(aa)&&this.HA.remove(aa)};z.prototype.Yj=function(aa){aa=this.fD(aa);this.Fy.send(aa)};z.prototype.ega=function(aa,ea){10<this.jy?this.HA.push(aa,ea):(this.jy++,aa=this.fD(ea),this.Fy.send(aa))};z.prototype.QMa=
function(aa){this.jy++;aa=this.fD(aa);this.Fy.send(aa)};z.prototype.Pp=function(aa){return aa};z.prototype.qaa=function(aa){this.Y_&&aa?Object(fa.i)("Server failed health check. Single server mode ignoring check."):!this.MVa&&aa&&3>=this.hi?(this.sW=!0,this.Fy.zv()):3<this.hi&&(this.Y_=!0)};z.prototype.LHa=function(aa){var ea=this,ba=aa.data,ca=aa.err,ha=aa.t;switch(ha){case "upload":ca?this.fSa.reject(ca):this.fSa.resolve("Success");break;case "pages":ca?this.Oq.reject(ca):ma(ba,ha,this.Oq)&&this.Oq.resolve(ba);
break;case "config":if(ca)this.fv.reject(ca);else if(ma(ba,ha,this.fv)){this.qaa(ba.unhealthy);ba.id&&(this.id=ba.id);if(ba.auth){var pa=Object(a.a)("wvsQueryParameters");pa.auth=ba.auth;Object(a.b)("wvsQueryParameters",pa)}ba.serverVersion&&(this.TP=ba.serverVersion,Object(fa.g)("[WebViewer Server] server version: ".concat(this.TP)));ba.serverID?(this.hi=ba.serverID===this.jga&&this.sW?this.hi+1:0,this.jga=ba.serverID):this.hi=0;this.sW=!1;this.fv.resolve(ba)}break;case "health":ca?this.fv.reject(ca):
ma(ba,ha,this.fv)&&this.qaa(ba.unhealthy);break;case "pdf":ba.url=Object(f.a)("".concat(this.Zg,"../").concat(encodeURI(ba.url)));ca?this.Gh.reject(ca):ma(ba,ha,this.Gh)&&this.Gh.resolve(ba);break;case "ApString2Xod":ba.url=Object(f.a)("".concat(this.Zg,"../data/").concat(encodeURI(ba.url)));ca?this.Gm[ba.rID].reject(ca):ma(ba,ha,this.Gm[ba.rID])&&this.Gm[ba.rID].resolve(ba);break;case "docmod":ba.url=Object(f.a)("".concat(this.Zg,"../").concat(encodeURI(ba.url)));ca?this.Gm[ba.rID].reject(ca):ma(ba,
ha,this.Gh)&&this.Gm[ba.rID].resolve(ba);break;case "xod":if(ca)this.Fg&&this.Fg.Am()&&this.Fg.reject(ca),this.Eg&&this.Eg.Am()&&this.Eg.reject(ca);else if(ba.notFound)ba.noCreate||this.Fg&&this.Fg.Am()&&this.Fg.resolve(ba),this.Eg&&this.Eg.Am()&&this.Eg.resolve(ba);else{ba.url&&(ba.url=Object(f.a)("".concat(this.Zg,"../").concat(encodeURI(ba.url))));if(!this.Eg||this.Eg.Zz())this.Eg=new h,this.Eg.request={t:"xod",noCreate:!0};this.Fg||(this.Fg=new h,this.Fg.request={t:"xod"});this.Eg.resolve(ba);
this.Fg.resolve(ba)}break;case "cAnnots":pa=this.Sh;if(ca)pa.reject(ca);else if(ma(ba,ha,pa)){pa.t_();var la=[],ja=ba.annots;ba=function(Ba){var Aa=ja[Ba].s,za=ja[Ba].e,Ga="".concat(qa.Zg,"../").concat(encodeURI(ja[Ba].xfdf)),Da="true"===ja[Ba].hasAppearance?Object(f.a)("".concat(Ga,".xodapp")):null,Ja=Object(ia.range)(Aa,za);la[Ba]={range:Ja,promise:new Promise(function(La,Ka){var Qa=new XMLHttpRequest;Qa.open("GET",Object(f.a)(Ga));Qa.responseType="text";Qa.withCredentials=ea.Pk();Qa.addEventListener("load",
function(){Qa.readyState===Qa.DONE&&200===Qa.status&&La({sx:Qa.response,Dp:Da,range:Ja})});Qa.addEventListener("error",function(){Ka("".concat(Qa.statusText," ").concat(JSON.stringify(Qa)))});Qa.send()})}};var qa=this;for(ca=0;ca<ja.length;ca++)ba(ca);pa.resolve(la)}break;case "annots":if(ca)this.Sh.reject(ca);else if(ma(ba,ha,this.Sh)){this.Sh.t_();var ra=new XMLHttpRequest;pa="".concat(this.Zg,"../").concat(encodeURI(ba.url));var sa=ba.hasAppearance?Object(f.a)("".concat(pa,".xodapp")):null;ra.open("GET",
Object(f.a)(pa));ra.responseType="text";ra.withCredentials=this.Pk();ra.addEventListener("load",function(){ra.readyState===ra.DONE&&200===ra.status&&ea.Sh.resolve({sx:ra.response,Dp:sa})});ra.addEventListener("error",function(){ea.Sh.reject("".concat(ra.statusText," ").concat(JSON.stringify(ra)))});ra.send()}break;case "image":this.jy--;var ta=this.Pj[ba.p];ca?ta.promise.reject(ca):ma(ba,ha,ta)&&(ta.result=ba,ta.result.url=Object(f.a)("".concat(this.Zg,"../").concat(encodeURI(ta.result.url))),ta.resolve(ta.result));
break;case "tiles":this.jy--;ta=ba.rID;pa=this.si[ta];this.si[ta]=null;this.zL.push(ta);if(ca)pa.reject(ca);else if(ma(ba,ha,pa)){for(ca=0;ca<ba.tiles.length;ca++)ba.tiles[ca]=Object(f.a)("".concat(this.Zg,"../").concat(encodeURI(ba.tiles[ca])));pa.resolve(ba)}break;case "text":ta=this.Km[ba.p];if(ca)ta.reject(ca);else if(ma(ba,ha,ta)){ta.t_();var va=new XMLHttpRequest;ba=Object(f.a)("".concat(this.Zg,"../").concat(encodeURI(ba.url)));va.open("GET",ba);va.withCredentials=this.Pk();va.addEventListener("load",
function(){va.readyState===va.DONE&&200===va.status&&(ta.result=JSON.parse(va.response),ta.resolve(ta.result))});va.addEventListener("error",function(Ba){ta.reject("".concat(va.statusText," ").concat(JSON.stringify(Ba)))});va.send()}break;case "progress":"loading"===ba.t&&this.trigger(r.a.Events.DOCUMENT_LOADING_PROGRESS,[ba.bytes,ba.total])}this.PMa();!ha&&aa.echo&&aa&&"apstring2xod"===aa.echo.t&&(aa=aa.echo.reqID)&&(2<=parseInt(this.TP,10)?this.Gm[aa].reject("Message unhandled by server"):this.Gm[aa].reject())};
z.prototype.hza=function(){return Object(ka.b)(this,void 0,void 0,function(){return Object(ka.d)(this,function(aa){switch(aa.label){case 0:return[4,this.KDa()];case 1:return aa.aa(),[2,this.fv.promise]}})})};z.prototype.Mya=function(aa){for(var ea=this,ba=new XMLHttpRequest,ca=Object(f.a)("".concat(this.Zg,"aul"),{id:this.id}),ha=new FormData,pa={},la=0;la<aa.body.length;la++){var ja=aa.body[la];pa[ja.id]="".concat(ja.tS.w,";").concat(ja.tS.h);ha.append(ja.id,ja.tS.dataString)}aa={t:"apstring2xod",
reqID:this.Ica++,parts:pa};var qa=this.fD(aa);ha.append("msg",JSON.stringify(qa));this.Gm[qa.reqID]=new h;ba.open("POST",ca);ba.withCredentials=this.Pk;ca=new Promise(function(ra,sa){ba.onreadystatechange=function(){4===ba.readyState&&(200===ba.status?ra():sa("An error occurred while sending down appearance strings to the server"))}});ba.send(ha);return ca.then(function(){return ea.Gm[qa.reqID].promise})};z.prototype.tsa=function(){var aa=this.TP.split("-")[0].split("."),ea=["1","5","9"];if(3!==aa.length)throw Error("Invalid WVS version length.");
if(3!==ea.length)throw Error("Invalid version length.");for(var ba=0;ba<aa.length;++ba){if(ea.length===ba||aa[ba]>ea[ba])return-1;if(aa[ba]!==ea[ba])return 1}return 0};z.prototype.bv=function(){return 0>=this.tsa()};z.prototype.TU=function(){this.Sh||(this.Sh=new h,this.bv()?this.Sh.request={t:"cAnnots"}:this.Sh.request={t:"annots"},this.Yj(this.Sh.request));return this.Sh.promise};z.prototype.BO=function(aa,ea){this.Pj[ea]||(this.Pj[ea]=new h,this.Pj[ea].request={t:"image",p:ea},this.ega(aa,this.Pj[ea].request));
return this.Pj[ea].promise};z.prototype.TLa=function(aa){this.Km[aa]||(this.Km[aa]=new h,this.Km[aa].request={t:"text",p:aa},this.Yj(this.Km[aa].request));return this.Km[aa].promise};z.prototype.ULa=function(aa,ea,ba,ca,ha){var pa=this.si.length;this.zL.length&&(pa=this.zL.pop());this.si[pa]=new h;this.si[pa].request={t:"tiles",p:ea,z:ba,r:ca,size:ha,rID:pa};this.ega(aa,this.si[pa].request);return this.si[pa].promise};z.prototype.wfa=function(){this.Gh||(this.Gh=new h,this.Gh.request={t:"pdf"},this.w7?
this.Gh.resolve({url:this.bU}):this.Yj(this.Gh.request));return this.Gh.promise};z.prototype.V9=function(aa){var ea=this,ba=new XMLHttpRequest,ca=Object(f.a)("".concat(this.Zg,"aul"),{id:this.id}),ha=new FormData,pa={};aa.annots&&(pa.annots="xfdf");aa.watermark&&(pa.watermark="png");aa.redactions&&(pa.redactions="redact");pa={t:"docmod",reqID:this.Ica++,parts:pa};aa.print&&(pa.print=!0);var la=this.fD(pa);ha.append("msg",JSON.stringify(la));return Promise.all([aa.annots,aa.watermark,aa.redactions].map(function(ja){return Promise.resolve(ja)})).then(function(ja){var qa=
ja[0],ra=ja[1];ja=ja[2];qa&&ha.append("annots",qa);ra&&ha.append("watermark",ra);ja&&ha.append("redactions",ja);ea.Gm[la.reqID]=new h;ba.open("POST",ca);ba.withCredentials=ea.Pk;qa=new Promise(function(sa,ta){ba.onreadystatechange=function(){4===ba.readyState&&(200===ba.status?sa():ta("An error occurred while sending down annotation data to the server"))}});ba.send(ha);return qa.then(function(){return ea.Gm[la.reqID].promise})})};z.prototype.T$=function(){this.Eg||(this.Eg=new h,this.Eg.request={t:"xod",
noCreate:!0},this.Yj(this.Eg.request));return this.Eg.promise};z.prototype.VLa=function(){this.Fg||(this.Fg=new h,this.Fg.request={t:"xod"},this.Yj(this.Fg.request));return this.Fg.promise};z.prototype.Jt=function(){return!0};z.prototype.request=function(){};z.prototype.Lea=function(){};z.prototype.abort=function(){for(var aa=0;aa<this.si.length;aa++)this.si[aa]&&(this.si[aa].resolve(null),this.si[aa]=null,this.zL.push(aa));this.close()};z.prototype.VO=function(aa){this.Tj=this.Tj||{};this.Tj.headers=
aa};z.prototype.Rt=function(aa){this.Tj=this.Tj||{};this.Tj.internal=this.Tj.internal||{};this.Tj.internal.withCredentials=aa};z.prototype.Pk=function(){return this.Tj&&this.Tj.internal?this.Tj.internal.withCredentials:null};z.prototype.getFileData=function(){return Promise.reject()};return z}();Object(ya.a)(n);Object(e.a)(n);Object(e.b)(n);ua["default"]=n},644:function(ya,ua,n){var na=n(0),ma=n(2),oa=n(36),ka=n(51),ia=n(237),fa=n(105),x=function(){function r(e,a,f,h,b,w){void 0===f&&(f=null);void 0===
h&&(h=null);void 0===b&&(b=null);void 0===w&&(w=null);this.vW=!1;this.hi=0;this.qF=8;this.Mfa=3E3;this.pP=!1;this.Q5=this.HSa(e);this.url=a?"".concat(this.Q5,"/").concat(a):"".concat(this.Q5,"/ws");this.HT=f;this.JF=h;this.dD=b;this.xfa=w}r.prototype.HSa=function(e){var a=e.indexOf("://"),f="ws://";0>a?a=0:(5===a&&(f="wss://"),a+=3);var h=e.lastIndexOf("/");0>h&&(h=e.length);return f+e.slice(a,h)};r.prototype.send=function(e){this.fu.readyState===WebSocket.CLOSED||this.vW||this.fu.send(JSON.stringify(e))};
r.prototype.SM=function(){return Object(na.b)(this,void 0,void 0,function(){var e,a=this;return Object(na.d)(this,function(){e=Object(ka.a)("wvsQueryParameters");e.bcid=Object(oa.m)(8);Object(ka.b)("wvsQueryParameters",e);return[2,new Promise(function(f,h){var b=Object(ia.a)(a.url);a.pP=!1;a.fu=new WebSocket(b);a.fu.onopen=function(){a.vW=!1;a.hi=0;a.JF&&a.JF();f()};a.fu.onerror=function(){a.vW=!0};a.fu.onclose=function(w){w=w.code;a.dD&&a.dD();1E3!==w&&3E3!==w&&a.YCa(w,f,h)};a.fu.onmessage=function(w){w&&
w.data&&(w=JSON.parse(w.data),w.hb?a.send({hb:!0}):w.end?close():a.HT(w))}})]})})};r.prototype.YCa=function(e,a,f){Object(na.b)(this,void 0,void 0,function(){var h=this;return Object(na.d)(this,function(){if(this.pP)return a(),[2];this.hi<this.qF?setTimeout(function(){h.pP?a():(h.hi++,Object(ma.i)("Failed to connect to server with WebSocket close code ".concat(e,". Reconnecting to WebViewer Server, attempt ").concat(h.hi," of ").concat(h.qF," ...")),h.SM().then(a).catch(f))},this.Mfa):f(fa.a);return[2]})})};
r.prototype.zv=function(){var e;void 0===e&&(e=!1);this.hi=0;this.pP=!0;e?this.fu.close(3E3):this.fu.close();return Promise.resolve()};return r}(),y=function(){function r(e,a,f,h,b,w,z){void 0===h&&(h=null);void 0===b&&(b=null);void 0===w&&(w=null);void 0===z&&(z=null);this.hi=this.AO=this.id=0;this.FE=!1;this.request=null;this.qF=8;this.Mfa=3E3;e=this.FJa(e);this.url=a?"".concat(e,"/").concat(a,"pf"):"".concat(e,"/pf");this.MP=f;this.HT=h;this.JF=b;this.dD=w;this.xfa=z}r.prototype.FJa=function(e){var a=
e.lastIndexOf("/");0>a&&(a=e.length);return e.slice(0,a)};r.prototype.Ota=function(e){e=e.split("\n");for(e[e.length-1]&&e.pop();0<e.length&&3>e[e.length-1].length;)"]"===e.pop()&&(this.id=0);0<e.length&&3>e[0].length&&e.shift();for(var a=0;a<e.length;++a)e[a].endsWith(",")&&(e[a]=e[a].substr(0,e[a].length-1));return e};r.prototype.Lfa=function(){return Object(na.b)(this,void 0,void 0,function(){var e=this;return Object(na.d)(this,function(a){switch(a.label){case 0:return this.hi++<this.qF?[4,new Promise(function(f){return setTimeout(function(){e.xfa();
e.SM();f()},3E3)})]:[3,2];case 1:a.aa(),a.label=2;case 2:return[2]}})})};r.prototype.KJa=function(e){Object(na.b)(this,void 0,void 0,function(){var a,f;return Object(na.d)(this,function(h){switch(h.label){case 0:a=null,f=0,h.label=1;case 1:if(!(f<e.length))return[3,6];a=JSON.parse(e[f]);if(!a)return[3,5];if(!a.end)return[3,2];close();return[3,5];case 2:if(!a.id||Number(a.id)===this.id)return[3,4];Object(ma.i)("Reconnecting, new server detected");this.zv();return[4,this.Lfa()];case 3:return h.aa(),
[3,5];case 4:a.hb&&Number(a.id)===this.id?this.send({hb:!0}):this.FE||this.HT(a),h.label=5;case 5:return++f,[3,1];case 6:return[2]}})})};r.prototype.GHa=function(e){Object(na.b)(this,void 0,void 0,function(){var a,f,h;return Object(na.d)(this,function(b){switch(b.label){case 0:if(!(3<=e.readyState))return[3,2];try{a=e.responseText.length}catch(w){return Object(ma.g)("caught exception"),[2]}if(0<a)try{f=this.Ota(e.responseText),0===this.id&&0<f.length&&(h=JSON.parse(f.shift()),this.id=h.id,this.hi=
0),this.KJa(f)}catch(w){}return this.FE?[3,2]:[4,this.z8()];case 1:b.aa(),b.label=2;case 2:return[2]}})})};r.prototype.z8=function(){return Object(na.b)(this,void 0,void 0,function(){var e=this;return Object(na.d)(this,function(){return[2,new Promise(function(a,f){function h(){return Object(na.b)(e,void 0,void 0,function(){return Object(na.d)(this,function(w){switch(w.label){case 0:f(),this.zv(),w.label=1;case 1:return this.FE&&this.hi<this.qF?[4,this.Lfa()]:[3,3];case 2:return w.aa(),[3,1];case 3:return[2]}})})}
e.request=new XMLHttpRequest;e.request.withCredentials=e.MP;var b=Object(ia.a)(e.url,0!==e.id?{id:String(e.id),uc:String(e.AO)}:{uc:String(e.AO)});e.AO++;e.request.open("GET",b,!0);e.request.setRequestHeader("Cache-Control","no-cache");e.request.setRequestHeader("X-Requested-With","XMLHttpRequest");e.request.onreadystatechange=function(){e.GHa(e.request)};e.request.addEventListener("error",h);e.request.addEventListener("timeout",h);e.request.addEventListener("load",function(){e.JF&&e.JF();a()});e.request.send()})]})})};
r.prototype.SM=function(){var e=Object(ka.a)("wvsQueryParameters");e.bcid=Object(oa.m)(8);Object(ka.b)("wvsQueryParameters",e);this.AO=this.id=0;this.FE=!1;return this.z8()};r.prototype.send=function(e){var a=this,f=new XMLHttpRequest;f.withCredentials=this.MP;var h=Object(ia.a)(this.url,{id:String(this.id)}),b=new FormData;b.append("data",JSON.stringify(e));f.addEventListener("error",function(){a.zv()});f.open("POST",h);f.setRequestHeader("X-Requested-With","XMLHttpRequest");f.send(b)};r.prototype.zv=
function(){this.id=0;this.FE=!0;this.dD&&this.dD();this.request.abort();return Promise.resolve()};return r}();ya=function(){function r(e,a,f){this.M6=e;this.target=a;this.MP=f}r.prototype.yya=function(e,a,f,h,b){void 0===e&&(e=!0);void 0===a&&(a=null);void 0===f&&(f=null);void 0===h&&(h=null);void 0===b&&(b=null);return e?new x(this.M6,this.target,a,f,h,b):new y(this.M6,this.target,this.MP,a,f,h,b)};return r}();ua.a=ya}}]);}).call(this || window)
