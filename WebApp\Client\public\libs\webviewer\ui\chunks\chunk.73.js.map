{"version": 3, "sources": ["webpack:///./src/ui/src/components/PageRedactionModal/PageRedactionModal.scss?c0a5", "webpack:///./src/ui/src/components/PageRedactionModal/PageRedactionModal.scss", "webpack:///./src/ui/src/components/PageRedactionModal/PageRedactionModal.js", "webpack:///./src/ui/src/components/PageRedactionModal/PageRedactionModalContainer.js", "webpack:///./src/ui/src/components/PageRedactionModal/index.js"], "names": ["api", "content", "__esModule", "default", "module", "i", "options", "styleTag", "window", "isApryseWebViewerWebComponent", "document", "head", "append<PERSON><PERSON><PERSON>", "webComponents", "getElementsByTagName", "length", "findNestedWebComponents", "tagName", "root", "elements", "querySelectorAll", "for<PERSON>ach", "el", "push", "shadowRoot", "clonedStyleTags", "webComponent", "onload", "styleNode", "innerHTML", "cloneNode", "exports", "locals", "propTypes", "closeModal", "PropTypes", "func", "currentPage", "number", "pageLabe<PERSON>", "array", "selectedPages", "markPages", "redactPages", "evenDisabled", "bool", "render<PERSON>an<PERSON><PERSON>", "isOpen", "SelectionTypes", "PageRedactionModal", "t", "useTranslation", "useState", "selectionType", "setSelectionType", "pages", "setPages", "pageNumberError", "setPageNumberError", "useEffect", "getSelectedPages", "pageNumbers", "onRedactWithFocusTransfer", "useFocusOnClose", "onMarktWithFocusTransfer", "canvasContainer", "useRef", "onSwipe", "e", "eventTarget", "event", "target", "current", "clientHeight", "scrollHeight", "clientWidth", "scrollWidth", "contains", "stopPropagation", "specifyPagesLabelElement", "className", "classNames", "error", "PageNumberInput", "selectedPageNumbers", "pageCount", "aria<PERSON><PERSON><PERSON>", "onSelectedPageNumbersChange", "onBlurHandler", "onError", "pageNumber", "Modal", "open", "closed", "data-element", "DataElements", "PAGE_REDACT_MODAL", "ModalWrapper", "title", "onCloseClick", "<PERSON><PERSON><PERSON><PERSON>", "onSwipedDown", "onSwipedUp", "swipeToClose", "ref", "role", "aria-<PERSON>by", "onChange", "classList", "value", "onSubmit", "preventDefault", "Choice", "checked", "radio", "name", "label", "disabled", "<PERSON><PERSON>", "dataElement", "onClick", "PageRedactionModalContainer", "dispatch", "useDispatch", "useSelector", "state", "selectors", "isElementOpen", "getCurrentPage", "getSelectedThumbnailPageIndexes", "getPageLabels", "getActiveToolName", "getActiveToolStyles", "selectedIndexes", "activeToolName", "activeToolStyles", "renderCanvasesCount", "map", "index", "actions", "closeElements", "PRINT_MODAL", "LOADING_MODAL", "PROGRESS_MODAL", "ERROR_MODAL", "closeElement", "getRedactionStyles", "includes", "setEvenDisabled", "docLoaded", "core", "getDocument", "documentCompletePromise", "getDocumentCompletePromise", "then", "getPageCount", "addEventListener", "removeEventListener", "callCount", "pagesToRender", "doc", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "splice", "pageInfo", "getPageInfo", "zoom", "rect", "getBoundingClientRect", "borderWidth", "parseInt", "getComputedStyle", "height", "width", "loadCanvas", "pageRotation", "drawComplete", "canvas", "setAttribute", "allowUseOfOptimizedThumbnail", "createPageRedactions"], "mappings": "+EAAA,IAAIA,EAAM,EAAQ,IACFC,EAAU,EAAQ,MAIC,iBAFvBA,EAAUA,EAAQC,WAAaD,EAAQE,QAAUF,KAG/CA,EAAU,CAAC,CAACG,EAAOC,EAAIJ,EAAS,MAG9C,IAAIK,EAAU,CAEd,OAAiB,SAAUC,GAgBX,IAAKC,OAAOC,8BAEV,YADAC,SAASC,KAAKC,YAAYL,GAI5B,IAAIM,EAEJA,EAAgBH,SAASI,qBAAqB,oBAEzCD,EAAcE,SACjBF,EAzBF,SAASG,EAAwBC,EAASC,EAAOR,UAC/C,MAAMS,EAAW,GAYjB,OATAD,EAAKE,iBAAiBH,GAASI,QAAQC,GAAMH,EAASI,KAAKD,IAG3DJ,EAAKE,iBAAiB,KAAKC,QAAQC,IAC7BA,EAAGE,YACLL,EAASI,QAAQP,EAAwBC,EAASK,EAAGE,eAIlDL,EAYSH,CAAwB,qBAG1C,MAAMS,EAAkB,GACxB,IAAK,IAAIpB,EAAI,EAAGA,EAAIQ,EAAcE,OAAQV,IAAK,CAC7C,MAAMqB,EAAeb,EAAcR,GACnC,GAAU,IAANA,EACFqB,EAAaF,WAAWZ,YAAYL,GACpCA,EAASoB,OAAS,WACZF,EAAgBV,OAAS,GAC3BU,EAAgBJ,QAASO,IAEvBA,EAAUC,UAAYtB,EAASsB,iBAIhC,CACL,MAAMD,EAAYrB,EAASuB,WAAU,GACrCJ,EAAaF,WAAWZ,YAAYgB,GACpCH,EAAgBF,KAAKK,MAIzC,WAAoB,GAEP5B,EAAIC,EAASK,GAI1BF,EAAO2B,QAAU9B,EAAQ+B,QAAU,I,sBClEnCD,EAAU3B,EAAO2B,QAAU,EAAQ,GAAR,EAAkE,IAKrFR,KAAK,CAACnB,EAAOC,EAAI,qwRAAswR,KAG/xR0B,EAAQC,OAAS,CAChB,kBAAqB,OACrB,mBAAsB,S,k1CCGvB,IAAMC,EAAY,CAChBC,WAAYC,IAAUC,KACtBC,YAAaF,IAAUG,OACvBC,WAAYJ,IAAUK,MACtBC,cAAeN,IAAUK,MACzBE,UAAWP,IAAUC,KACrBO,YAAaR,IAAUC,KACvBQ,aAAcT,IAAUU,KACxBC,eAAgBX,IAAUC,KAC1BW,OAAQZ,IAAUU,MAGdG,EACK,UADLA,EAEK,UAFLA,EAGC,MAHDA,EAIE,OAGFC,EAAqB,SAAH,GAUlB,IATJf,EAAU,EAAVA,WACAK,EAAU,EAAVA,WACAE,EAAa,EAAbA,cACAJ,EAAW,EAAXA,YACAK,EAAS,EAATA,UACAC,EAAW,EAAXA,YACAC,EAAY,EAAZA,aACAE,EAAc,EAAdA,eACAC,EAAM,EAANA,OAEQG,EAAMC,cAAND,EAEkE,IAAhCE,mBAASJ,GAAuB,GAAnEK,EAAa,KAAEC,EAAgB,KACF,IAAVF,qBAAU,GAA7BG,EAAK,KAAEC,EAAQ,KACoC,IAAZJ,mBAAS,IAAG,GAAnDK,EAAe,KAAEC,EAAkB,KAE1CC,qBAAU,WACRH,EAASf,KACR,CAACA,IAEJ,IAAMmB,EAAmB,WACvB,IAAMC,EAAc,GACpB,GAAIR,IAAkBL,EACpB,MAAO,CAACX,GAEV,GAAIgB,IAAkBL,EACpB,OAAOO,EAET,GAAIF,IAAkBL,EACpB,IAAK,IAAI3C,EAAI,EAAGkC,EAAWxB,QAAUV,EAAGA,GAAK,EAC3CwD,EAAYtC,KAAKlB,QAEd,GAAIgD,IAAkBL,EAC3B,IAAK,IAAI3C,EAAI,EAAGkC,EAAWxB,QAAUV,EAAGA,GAAK,EAC3CwD,EAAYtC,KAAKlB,GAGrB,OAAOwD,GAKHC,EAA4BC,aADjB,WAAH,OAASpB,EAAYiB,QAE7BI,EAA2BD,aAHlB,WAAH,OAASrB,EAAUkB,QAKzBK,EAAkBC,mBACxBP,qBAAU,WACJZ,GACFD,EAAemB,EAAiBL,OAEjC,CAACP,EAAeN,EAAQQ,EAAOT,EAAgBc,IAElD,IAAMO,EAAU,SAACC,GACf,IAAMC,EAAcD,EAAEE,MAAMC,QAE1BN,EAAgBO,QAAQC,aAAeR,EAAgBO,QAAQE,cAC/DT,EAAgBO,QAAQG,YAAcV,EAAgBO,QAAQI,eAG7DP,IAAgBJ,EAAgBO,SAAWP,EAAgBO,QAAQK,SAASR,IAE7ED,EAAEE,MAAMQ,kBAER5C,KAuBE6C,EACJ,oCACE,2BAAOC,UAAU,2BACf,8BAAO9B,EAAE,mCACU,YAAlBG,GACC,0BAAM2B,UAAU,4BAA2B,KACtC9B,EAAE,mDAIQ,YAAlBG,GACC,yBAAK2B,UAAWC,IAAW,8BAA+B,CAAEC,QAASzB,KACnE,kBAAC0B,EAAA,EAAe,CACdC,oBAAqB7B,EACrB8B,UAAW9C,EAAWxB,OACtBuE,UAAWpC,EAAE,kCACbqC,4BA7Ba,SAAC1B,GAClBA,EAAY9C,OAAS,IACvB2C,EAAmB,IACnBF,EAASK,KA2BH2B,cAAehC,EACfiC,QAxBoB,SAACC,GACzBA,GACFhC,EAAmB,GAAD,OAAIR,EAAE,2BAA0B,YAAIX,EAAWxB,UAuB3D0C,gBAAiBA,MAO3B,OACE,yBACEuB,UAAWC,IAAW,CACpBU,OAAO,EACP1C,oBAAoB,EACpB2C,KAAM7C,EACN8C,QAAS9C,IAEX+C,eAAcC,IAAaC,mBAE3B,kBAACC,EAAA,EAAY,CACXC,MAAM,qBACNnD,OAAQA,EACRoD,aAAcjE,EACdkE,aAAclE,EACdmE,aAAclC,EACdmC,WAAYnC,EACZoC,cAAY,GAEZ,yBAAKvB,UAAU,QACb,yBAAKA,UAAU,mBAAmBwB,IAAKvC,IACvC,0BAAMe,UAAU,oBAAoByB,KAAK,QAAQC,kBAAiBxD,EAAE,wCAAyCyD,SAlE3F,SAACvC,GACpBA,EAAEG,OAAOqC,UAAU/B,SAAS,uBAC/BvB,EAAiBc,EAAEG,OAAOsC,OAC1BnD,EAAmB,MA+D2HoD,SAAU,SAAC1C,GAAC,OAAKA,EAAE2C,mBAC3J,kCACE,gCACE,gCAAS7D,EAAE,0CAEb,kBAAC8D,EAAA,EAAM,CACLC,QAAS5D,IAAkBL,EAC3BkE,OAAK,EACLC,KAAK,wBACLC,MAAOlE,EAAE,kCACT2D,MAAO7D,IAET,kBAACgE,EAAA,EAAM,CACLC,QAAS5D,IAAkBL,EAC3BkE,OAAK,EACLC,KAAK,wBACLnC,UAAU,uBACVoC,MAAOrC,EACP8B,MAAO7D,IAET,kBAACgE,EAAA,EAAM,CACLC,QAAS5D,IAAkBL,EAC3BkE,OAAK,EACLC,KAAK,wBACLC,MAAOlE,EAAE,8BACT2D,MAAO7D,IAET,kBAACgE,EAAA,EAAM,CACLC,QAAS5D,IAAkBL,EAC3BkE,OAAK,EACLC,KAAK,wBACLC,MAAOlE,EAAE,+BACT2D,MAAO7D,EACPqE,SAAUzE,OAKlB,yBAAKoC,UAAU,UACb,kBAACsC,EAAA,EAAM,CACLtC,UAAU,uCACVuC,YAAY,oBACZH,MAAM,oBACNC,SAAU5D,EACV+D,QAAS1D,IAEX,kBAACwD,EAAA,EAAM,CACLtC,UAAU,uBACVuC,YAAY,wBACZH,MAAM,iCACNC,SAAU5D,EACV+D,QAASxD,QAQrBf,EAAmBhB,UAAYA,EAEhBgB,Q,g4CCvNf,IA0HewE,EAxHqB,WAClC,IAAMC,EAAWC,cAQf,IAP2FC,aAAY,SAACC,GAAK,MAAK,CAClHC,IAAUC,cAAcF,EAAO9B,IAAaC,mBAC5C8B,IAAUE,eAAeH,GACzBC,IAAUG,gCAAgCJ,GAC1CC,IAAUI,cAAcL,GACxBC,IAAUK,kBAAkBN,GAC5BC,IAAUM,oBAAoBP,OAC9B,GAPK9E,EAAM,KAAEV,EAAW,KAAEgG,EAAe,KAAE9F,EAAU,KAAE+F,EAAc,KAAEC,EAAgB,KAQnFC,EAAsBtE,iBAAO,GAE7BzB,EAAgB4F,EAAgBI,KAAI,SAACC,GAAK,OAAKA,EAAQ,KAE7D/E,qBAAU,WACJZ,GACF2E,EAASiB,IAAQC,cAAc,CAC7B7C,IAAa8C,YACb9C,IAAa+C,cACb/C,IAAagD,eACbhD,IAAaiD,iBAGhB,CAACjG,EAAQ2E,IAEZ,IAAMxF,EAAa,WAAH,OAASwF,EAASiB,IAAQM,aAAalD,IAAaC,qBAC9DkD,EAAqB,WAAH,OAAUZ,WAAgBa,SAAS,6BAA+BZ,EAAmB,IACrGrF,EAAMC,cAAND,EAyD+C,IAAfE,oBAAS,GAAM,GAAhDR,EAAY,KAAEwG,EAAe,KAqBpC,OApBAzF,qBAAU,WACR,IAAM0F,EAAY,WAChB,IAAM3I,EAAW4I,IAAKC,cAChBC,EAA0B9I,EAAS+I,6BAIzCD,WAAyBE,MAAK,WAC5B,IAAMrE,EAAY3E,EAASiJ,eAEzBP,EADE/D,EAAY,OAQpB,OADAiE,IAAKM,iBAAiB,iBAAkBP,GACjC,kBAAMC,IAAKO,oBAAoB,iBAAkBR,MACvD,IAGD,kBAAC,EAAkB,CACjBzG,aAAcA,EACdV,WAAYA,EACZY,eAtEmB,SAACmB,EAAiBJ,GAAgB,MACvD2E,EAAoBhE,UAMpB,IALA,IAAMsF,EAAYtB,EAAoBhE,QAElCuF,EAAgB,EAAIlG,GAElBmG,EAAMV,IAAKC,cACVtF,EAAgBO,QAAQyF,YAC7BhG,EAAgBO,QAAQ0F,YAAYjG,EAAgBO,QAAQyF,YAE9D,GAAKF,EAAL,EAGiB,QAAb,EAAAA,SAAa,aAAb,EAAehJ,QAtDE,KAuDnBgJ,EAAgBA,EAAcI,OAAO,EAvDlB,KAwDpB,IACqC,EADrC,E,goBAAA,CACwBJ,GAAa,qBAAE,IAA7BrE,EAAU,QACb0E,EAAWJ,aAAG,EAAHA,EAAKK,YAAY3E,GAClC,GAAI3C,GAAUiH,GAAO/F,EAAgBO,SAAW4F,EAAU,CACxD,IAAIE,EAAO,EACLC,EAAOtG,EAAgBO,QAAQgG,wBAC/BC,EAAcC,SAASlK,OAAOmK,iBAAiB1G,EAAgBO,SAASiG,aAAe,GAC7FF,EAAKK,QAAUH,EACfF,EAAKM,OAASJ,GAEZH,EADEF,EAASS,MAAQT,EAASQ,OACrBL,EAAKM,MAAQT,EAASS,MAEtBN,EAAKK,OAASR,EAASQ,QAEzB,GAAKZ,EAAIc,WAAW,CACzBpF,aACA4E,OACAS,aAAc,EACdC,aAAc,SAACC,GAAW,MACpBnB,IAActB,EAAoBhE,SAAkC,QAA3B,EAAIP,EAAgBO,eAAO,OAAvB,EAAyB5D,YAAYqK,KACpFA,EAAOC,aAAa,OAAQ,OAC5BD,EAAOC,aAAa,aAAc,GAAF,OAAKhI,EAAE,eAAc,YAAIwC,MAG7DyF,8BAA8B,MAvBpC,IAAK,EAAL,yBA0BC,iCA6BCxI,YAjFa,SAACkB,GAChBlB,YAAYkB,EAAaqF,KACzBhH,KAgFEQ,UA7Ec,SAACmB,GACjBuH,YAAqBvH,EAAaqF,KAClChH,KA4EEG,YAAaA,EACbI,cAAeA,EACfF,WAAYA,EACZQ,OAAQA,KC/HC0E", "file": "chunks/chunk.73.js", "sourcesContent": ["var api = require(\"!../../../../../node_modules/style-loader/dist/runtime/injectStylesIntoStyleTag.js\");\n            var content = require(\"!!../../../../../node_modules/css-loader/index.js!../../../../../node_modules/postcss-loader/src/index.js??postcss!../../../../../node_modules/sass-loader/dist/cjs.js!./PageRedactionModal.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function (styleTag) {\n                function findNestedWebComponents(tagName, root = document) {\n                  const elements = [];\n\n                  // Check direct children\n                  root.querySelectorAll(tagName).forEach(el => elements.push(el));\n\n                  // Check shadow DOMs\n                  root.querySelectorAll('*').forEach(el => {\n                    if (el.shadowRoot) {\n                      elements.push(...findNestedWebComponents(tagName, el.shadowRoot));\n                    }\n                  });\n\n                  return elements;\n                }\n                if (!window.isApryseWebViewerWebComponent) {\n                  document.head.appendChild(styleTag);\n                  return;\n                }\n\n                let webComponents;\n                // First we see if the webcomponent is at the document level\n                webComponents = document.getElementsByTagName('apryse-webviewer');\n                // If not, we check have to check if it is nested in another webcomponent\n                if (!webComponents.length) {\n                  webComponents = findNestedWebComponents('apryse-webviewer');\n                }\n                // Now we append the style tag to each webcomponent\n                const clonedStyleTags = [];\n                for (let i = 0; i < webComponents.length; i++) {\n                  const webComponent = webComponents[i];\n                  if (i === 0) {\n                    webComponent.shadowRoot.appendChild(styleTag);\n                    styleTag.onload = function () {\n                      if (clonedStyleTags.length > 0) {\n                        clonedStyleTags.forEach((styleNode) => {\n                          // eslint-disable-next-line no-unsanitized/property\n                          styleNode.innerHTML = styleTag.innerHTML;\n                        });\n                      }\n                    };\n                  } else {\n                    const styleNode = styleTag.cloneNode(true);\n                    webComponent.shadowRoot.appendChild(styleNode);\n                    clonedStyleTags.push(styleNode);\n                  }\n                }\n              };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\n\nmodule.exports = content.locals || {};", "exports = module.exports = require(\"../../../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".open.PageRedactionModal{visibility:visible}.closed.PageRedactionModal{visibility:hidden}:host{display:inline-block;container-type:inline-size;width:100%;height:100%;overflow:hidden}@media(min-width:901px){.App:not(.is-web-component) .hide-in-desktop{display:none}}@container (min-width: 901px){.hide-in-desktop{display:none}}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .hide-in-tablet{display:none}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .hide-in-tablet{display:none}}@media(max-width:640px)and (min-width:431px){.App:not(.is-web-component) .hide-in-mobile{display:none}}@container (max-width: 640px) and (min-width: 431px){.App.is-web-component .hide-in-mobile{display:none}}@media(max-width:430px){.App:not(.is-web-component) .hide-in-small-mobile{display:none}}@container (max-width: 430px){.App.is-web-component .hide-in-small-mobile{display:none}}.always-hide{display:none}.PageRedactionModal .footer .modal-button.confirm:hover{background:var(--primary-button-hover);border-color:var(--primary-button-hover);color:var(--gray-0)}.PageRedactionModal .footer .modal-button.confirm{background:var(--primary-button);border-color:var(--primary-button);color:var(--primary-button-text)}.PageRedactionModal .footer .modal-button.confirm.disabled{cursor:default;background:var(--disabled-button-color);color:var(--primary-button-text)}.PageRedactionModal .footer .modal-button.confirm.disabled span{color:var(--primary-button-text)}.PageRedactionModal .footer .modal-button.cancel:hover,.PageRedactionModal .footer .modal-button.secondary-btn-custom:hover{border:none;box-shadow:inset 0 0 0 1px var(--blue-6);color:var(--blue-6)}.PageRedactionModal .footer .modal-button.cancel,.PageRedactionModal .footer .modal-button.secondary-btn-custom{border:none;box-shadow:inset 0 0 0 1px var(--primary-button);color:var(--primary-button)}.PageRedactionModal .footer .modal-button.cancel.disabled,.PageRedactionModal .footer .modal-button.secondary-btn-custom.disabled{cursor:default;border:none;box-shadow:inset 0 0 0 1px rgba(43,115,171,.5);color:rgba(43,115,171,.5)}.PageRedactionModal .footer .modal-button.cancel.disabled span,.PageRedactionModal .footer .modal-button.secondary-btn-custom.disabled span{color:rgba(43,115,171,.5)}.PageRedactionModal{position:fixed;left:0;bottom:0;z-index:100;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:var(--modal-negative-space)}.PageRedactionModal .modal-container .wrapper .modal-content{padding:10px}.PageRedactionModal .footer{display:flex;flex-direction:row;justify-content:flex-end;width:100%;margin-top:13px}.PageRedactionModal .footer.modal-footer{padding:16px;margin:0;border-top:1px solid var(--divider)}.PageRedactionModal .footer .modal-button{display:flex;justify-content:center;align-items:center;padding:6px 18px;margin:8px 0 0;width:auto;width:-moz-fit-content;width:fit-content;border-radius:4px;height:30px;cursor:pointer}.PageRedactionModal .footer .modal-button.confirm{margin-left:4px}.PageRedactionModal .footer .modal-button.secondary-btn-custom{border-radius:4px;padding:2px 20px 4px;cursor:pointer}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .PageRedactionModal .footer .modal-button{padding:23px 8px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .PageRedactionModal .footer .modal-button{padding:23px 8px}}.PageRedactionModal .swipe-indicator{background:var(--swipe-indicator-bg);border-radius:2px;height:4px;width:38px;position:absolute;top:12px;margin-left:auto;margin-right:auto;left:0;right:0}@media(min-width:641px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .PageRedactionModal .swipe-indicator{display:none}}@container (min-width: 641px){.App.is-web-component:not(.is-in-desktop-only-mode) .PageRedactionModal .swipe-indicator{display:none}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .PageRedactionModal .swipe-indicator{width:32px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .PageRedactionModal .swipe-indicator{width:32px}}.PageRedactionModal .modal-container{align-items:center;background:var(--component-background);width:888px}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .PageRedactionModal .modal-container{width:480px}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .PageRedactionModal .modal-container{width:480px}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .PageRedactionModal .modal-container{width:100%}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .PageRedactionModal .modal-container{width:100%}}.PageRedactionModal .modal-container .body{padding:16px;grid-gap:16px;gap:16px;display:flex;width:100%}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .PageRedactionModal .modal-container .body{display:flex;grid-gap:5px;gap:5px;flex-direction:column}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .PageRedactionModal .modal-container .body{display:flex;grid-gap:5px;gap:5px;flex-direction:column}}.PageRedactionModal .modal-container .body .canvas-container{border:1px solid var(--lighter-border);background-color:var(--file-preview-background);height:448px;width:60%;display:flex;align-items:center;justify-content:space-between;flex-direction:column;overflow:auto;grid-gap:5px;gap:5px}@media(min-width:641px)and (max-width:900px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .PageRedactionModal .modal-container .body .canvas-container{width:55%;height:300px}}@container (min-width: 641px) and (max-width: 900px){.App.is-web-component:not(.is-in-desktop-only-mode) .PageRedactionModal .modal-container .body .canvas-container{width:55%;height:300px}}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .PageRedactionModal .modal-container .body .canvas-container{width:100%;height:250px}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .PageRedactionModal .modal-container .body .canvas-container{width:100%;height:250px}}.PageRedactionModal .modal-container .body .canvas-container canvas{box-shadow:0 0 3px 0 var(--box-shadow)}.PageRedactionModal .modal-container .body .selection-options{width:40%;grid-gap:12px;gap:12px;display:flex;flex-direction:column}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .PageRedactionModal .modal-container .body .selection-options{width:100%}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .PageRedactionModal .modal-container .body .selection-options{width:100%}}.PageRedactionModal .modal-container .body .selection-options fieldset{border:0;padding:0;margin:0;min-width:0;display:grid;grid-gap:12px;gap:12px}.PageRedactionModal .modal-container .body .selection-options legend{padding:0;margin-bottom:12px}.PageRedactionModal .modal-container .body .selection-options .page-number-input-container{width:100%;margin-top:8px}.PageRedactionModal .modal-container .body .selection-options .page-number-input-container .PageNumberInput{height:54px}.PageRedactionModal .modal-container .body .selection-options .page-number-input-container .PageNumberInput .page-number-input{height:32px;width:100%;align-self:flex-end}.PageRedactionModal .modal-container .body .selection-options .ui__choice--disabled{text-decoration:line-through}.PageRedactionModal .modal-container .body .selection-options .ui__choice{font-size:13px;margin:0}.PageRedactionModal .modal-container .body .selection-options .ui__choice.specify-pages-choice.ui__choice--checked{align-items:baseline;height:75px}.PageRedactionModal .modal-container .body .selection-options .ui__choice.specify-pages-choice.ui__choice--checked .ui__choice__label{width:100%;position:relative;top:-2px}.PageRedactionModal .modal-container .body .selection-options .ui__choice .specifyPagesChoiceLabel{display:flex}.PageRedactionModal .modal-container .body .selection-options .ui__choice .specifyPagesChoiceLabel .specifyPagesExampleLabel{margin-left:4px;color:var(--faded-text)}.PageRedactionModal .modal-container .footer{margin-top:0;width:100%;display:flex;align-items:center;justify-content:flex-end;padding:16px;border-top:1px solid var(--divider)}.PageRedactionModal .modal-container .footer .Button{margin-top:0;height:32px}@media(max-width:640px){.App:not(.is-in-desktop-only-mode):not(.is-web-component) .PageRedactionModal .modal-container .footer .Button{padding:8px 16px!important}}@container (max-width: 640px){.App.is-web-component:not(.is-in-desktop-only-mode) .PageRedactionModal .modal-container .footer .Button{padding:8px 16px!important}}\", \"\"]);\n\n// exports\nexports.locals = {\n\t\"LEFT_HEADER_WIDTH\": \"41px\",\n\t\"RIGHT_HEADER_WIDTH\": \"41px\"\n};", "import React, { useEffect, useState, useRef } from 'react';\nimport DataElements from 'constants/dataElement';\nimport classNames from 'classnames';\nimport { useTranslation } from 'react-i18next';\nimport Button from 'components/Button';\nimport Choice from 'components/Choice';\nimport PropTypes from 'prop-types';\nimport PageNumberInput from 'components/PageReplacementModal/PageNumberInput';\nimport ModalWrapper from 'components/ModalWrapper';\nimport useFocusOnClose from 'hooks/useFocusOnClose';\n\nimport './PageRedactionModal.scss';\n\nconst propTypes = {\n  closeModal: PropTypes.func,\n  currentPage: PropTypes.number,\n  pageLabels: PropTypes.array,\n  selectedPages: PropTypes.array,\n  markPages: PropTypes.func,\n  redactPages: PropTypes.func,\n  evenDisabled: PropTypes.bool,\n  renderCanvases: PropTypes.func,\n  isOpen: PropTypes.bool,\n};\n\nconst SelectionTypes = {\n  CURRENT: 'current',\n  SPECIFY: 'specify',\n  ODD: 'odd',\n  EVEN: 'even',\n};\n\nconst PageRedactionModal = ({\n  closeModal,\n  pageLabels,\n  selectedPages,\n  currentPage,\n  markPages,\n  redactPages,\n  evenDisabled,\n  renderCanvases,\n  isOpen,\n}) => {\n  const { t } = useTranslation();\n\n  const [selectionType, setSelectionType] = useState(SelectionTypes.CURRENT);\n  const [pages, setPages] = useState();\n  const [pageNumberError, setPageNumberError] = useState('');\n\n  useEffect(() => {\n    setPages(selectedPages);\n  }, [selectedPages]);\n\n  const getSelectedPages = () => {\n    const pageNumbers = [];\n    if (selectionType === SelectionTypes.CURRENT) {\n      return [currentPage];\n    }\n    if (selectionType === SelectionTypes.SPECIFY) {\n      return pages;\n    }\n    if (selectionType === SelectionTypes.ODD) {\n      for (let i = 1; pageLabels.length >= i; i += 2) {\n        pageNumbers.push(i);\n      }\n    } else if (selectionType === SelectionTypes.EVEN) {\n      for (let i = 2; pageLabels.length >= i; i += 2) {\n        pageNumbers.push(i);\n      }\n    }\n    return pageNumbers;\n  };\n\n  const onMark = () => markPages(getSelectedPages());\n  const onRedact = () => redactPages(getSelectedPages());\n  const onRedactWithFocusTransfer = useFocusOnClose(onRedact);\n  const onMarktWithFocusTransfer = useFocusOnClose(onMark);\n\n  const canvasContainer = useRef();\n  useEffect(() => {\n    if (isOpen) {\n      renderCanvases(canvasContainer, getSelectedPages());\n    }\n  }, [selectionType, isOpen, pages, renderCanvases, getSelectedPages]);\n\n  const onSwipe = (e) => {\n    const eventTarget = e.event.target;\n    const containerHasScroll =\n      canvasContainer.current.clientHeight < canvasContainer.current.scrollHeight ||\n      canvasContainer.current.clientWidth < canvasContainer.current.scrollWidth;\n    if (\n      containerHasScroll &&\n      (eventTarget === canvasContainer.current || canvasContainer.current.contains(eventTarget))\n    ) {\n      e.event.stopPropagation();\n    } else {\n      closeModal();\n    }\n  };\n\n  const onSelectionChange = (e) => {\n    if (!e.target.classList.contains('page-number-input')) {\n      setSelectionType(e.target.value);\n      setPageNumberError('');\n    }\n  };\n  const onPagesChanged = (pageNumbers) => {\n    if (pageNumbers.length > 0) {\n      setPageNumberError('');\n      setPages(pageNumbers);\n    }\n  };\n\n  const handlePageNumberError = (pageNumber) => {\n    if (pageNumber) {\n      setPageNumberError(`${t('message.errorPageNumber')} ${pageLabels.length}`);\n    }\n  };\n\n  const specifyPagesLabelElement = (\n    <>\n      <label className=\"specifyPagesChoiceLabel\">\n        <span>{t('option.pageRedactModal.specify')}</span>\n        {selectionType === 'specify' && (\n          <span className=\"specifyPagesExampleLabel\">\n            - {t('option.thumbnailPanel.multiSelectPagesExample')}\n          </span>\n        )}\n      </label>\n      {selectionType === 'specify' && (\n        <div className={classNames('page-number-input-container', { error: !!pageNumberError })}>\n          <PageNumberInput\n            selectedPageNumbers={pages}\n            pageCount={pageLabels.length}\n            ariaLabel={t('option.pageRedactModal.specify')}\n            onSelectedPageNumbersChange={onPagesChanged}\n            onBlurHandler={setPages}\n            onError={handlePageNumberError}\n            pageNumberError={pageNumberError}\n          />\n        </div>\n      )}\n    </>\n  );\n\n  return (\n    <div\n      className={classNames({\n        Modal: true,\n        PageRedactionModal: true,\n        open: isOpen,\n        closed: !isOpen,\n      })}\n      data-element={DataElements.PAGE_REDACT_MODAL}\n    >\n      <ModalWrapper\n        title=\"action.redactPages\"\n        isOpen={isOpen}\n        onCloseClick={closeModal}\n        closeHandler={closeModal}\n        onSwipedDown={onSwipe}\n        onSwipedUp={onSwipe}\n        swipeToClose\n      >\n        <div className=\"body\">\n          <div className=\"canvas-container\" ref={canvasContainer} />\n          <form className=\"selection-options\" role=\"group\" aria-labelledby={t('option.pageRedactModal.pageSelection')} onChange={onSelectionChange} onSubmit={(e) => e.preventDefault()}>\n            <fieldset>\n              <legend>\n                <strong>{t('option.pageRedactModal.pageSelection')}</strong>\n              </legend>\n              <Choice\n                checked={selectionType === SelectionTypes.CURRENT}\n                radio\n                name=\"page-redaction-option\"\n                label={t('option.pageRedactModal.current')}\n                value={SelectionTypes.CURRENT}\n              />\n              <Choice\n                checked={selectionType === SelectionTypes.SPECIFY}\n                radio\n                name=\"page-redaction-option\"\n                className=\"specify-pages-choice\"\n                label={specifyPagesLabelElement}\n                value={SelectionTypes.SPECIFY}\n              />\n              <Choice\n                checked={selectionType === SelectionTypes.ODD}\n                radio\n                name=\"page-redaction-option\"\n                label={t('option.pageRedactModal.odd')}\n                value={SelectionTypes.ODD}\n              />\n              <Choice\n                checked={selectionType === SelectionTypes.EVEN}\n                radio\n                name=\"page-redaction-option\"\n                label={t('option.pageRedactModal.even')}\n                value={SelectionTypes.EVEN}\n                disabled={evenDisabled}\n              />\n            </fieldset>\n          </form>\n        </div>\n        <div className=\"footer\">\n          <Button\n            className=\"cancel modal-button secondary-button\"\n            dataElement=\"modalRedactButton\"\n            label=\"annotation.redact\"\n            disabled={pageNumberError}\n            onClick={onRedactWithFocusTransfer}\n          />\n          <Button\n            className=\"confirm modal-button\"\n            dataElement=\"modalMarkRedactButton\"\n            label=\"option.pageRedactModal.addMark\"\n            disabled={pageNumberError}\n            onClick={onMarktWithFocusTransfer}\n          />\n        </div>\n      </ModalWrapper>\n    </div>\n  );\n};\n\nPageRedactionModal.propTypes = propTypes;\n\nexport default PageRedactionModal;\n", "import React, { useEffect, useState, useRef } from 'react';\nimport DataElements from 'constants/dataElement';\nimport { useDispatch, useSelector } from 'react-redux';\nimport selectors from 'selectors';\nimport actions from 'actions';\nimport core from 'core';\nimport { createPageRedactions, redactPages } from 'helpers/pageManipulationFunctions';\nimport PageRedactionModal from 'components/PageRedactionModal/PageRedactionModal';\nimport { useTranslation } from 'react-i18next';\n\nimport './PageRedactionModal.scss';\n\nconst MAX_CANVAS_COUNT = 10;\n\nconst PageRedactionModalContainer = () => {\n  const dispatch = useDispatch();\n  const [isOpen, currentPage, selectedIndexes, pageLabels, activeToolName, activeToolStyles] = useSelector((state) => [\n    selectors.isElementOpen(state, DataElements.PAGE_REDACT_MODAL),\n    selectors.getCurrentPage(state),\n    selectors.getSelectedThumbnailPageIndexes(state),\n    selectors.getPageLabels(state),\n    selectors.getActiveToolName(state),\n    selectors.getActiveToolStyles(state)\n  ]);\n  const renderCanvasesCount = useRef(0);\n\n  const selectedPages = selectedIndexes.map((index) => index + 1);\n\n  useEffect(() => {\n    if (isOpen) {\n      dispatch(actions.closeElements([\n        DataElements.PRINT_MODAL,\n        DataElements.LOADING_MODAL,\n        DataElements.PROGRESS_MODAL,\n        DataElements.ERROR_MODAL,\n      ]));\n    }\n  }, [isOpen, dispatch]);\n\n  const closeModal = () => dispatch(actions.closeElement(DataElements.PAGE_REDACT_MODAL));\n  const getRedactionStyles = () => (activeToolName?.includes('AnnotationCreateRedaction') ? activeToolStyles : {});\n  const { t } = useTranslation();\n\n  const onRedact = (pageNumbers) => {\n    redactPages(pageNumbers, getRedactionStyles());\n    closeModal();\n  };\n\n  const markPages = (pageNumbers) => {\n    createPageRedactions(pageNumbers, getRedactionStyles());\n    closeModal();\n  };\n\n  const renderCanvases = (canvasContainer, pageNumbers) => {\n    renderCanvasesCount.current++;\n    const callCount = renderCanvasesCount.current;\n    // Clone the array so that we don't modify the original array\n    let pagesToRender = [...pageNumbers];\n\n    const doc = core.getDocument();\n    while (canvasContainer.current.firstChild) {\n      canvasContainer.current.removeChild(canvasContainer.current.firstChild);\n    }\n    if (!pagesToRender) {\n      return;\n    }\n    if (pagesToRender?.length > MAX_CANVAS_COUNT) {\n      pagesToRender = pagesToRender.splice(0, MAX_CANVAS_COUNT);\n    }\n    for (const pageNumber of pagesToRender) {\n      const pageInfo = doc?.getPageInfo(pageNumber);\n      if (isOpen && doc && canvasContainer.current && pageInfo) {\n        let zoom = 1;\n        const rect = canvasContainer.current.getBoundingClientRect();\n        const borderWidth = parseInt(window.getComputedStyle(canvasContainer.current).borderWidth) + 0.1;\n        rect.height -= borderWidth;\n        rect.width -= borderWidth;\n        if (pageInfo.width > pageInfo.height) {\n          zoom = rect.width / pageInfo.width;\n        } else {\n          zoom = rect.height / pageInfo.height;\n        }\n        zoom > 0 && doc.loadCanvas({\n          pageNumber,\n          zoom,\n          pageRotation: 0,\n          drawComplete: (canvas) => {\n            if (callCount === renderCanvasesCount.current && canvasContainer.current?.appendChild(canvas)) {\n              canvas.setAttribute('role', 'img');\n              canvas.setAttribute('aria-label', `${t('action.page')} ${pageNumber}`);\n            }\n          },\n          allowUseOfOptimizedThumbnail: true\n        });\n      }\n    }\n  };\n\n  const [evenDisabled, setEvenDisabled] = useState(false);\n  useEffect(() => {\n    const docLoaded = () => {\n      const document = core.getDocument();\n      const documentCompletePromise = document.getDocumentCompletePromise();\n\n      // Office documents don't have all pages available onload, so we need to\n      // wait for the documentCompletePromise to resolve before we can check\n      documentCompletePromise?.then(() => {\n        const pageCount = document.getPageCount();\n        if (pageCount < 2) {\n          setEvenDisabled(true);\n        } else {\n          setEvenDisabled(false);\n        }\n      });\n    };\n    core.addEventListener('documentLoaded', docLoaded);\n    return () => core.removeEventListener('documentLoaded', docLoaded);\n  }, []);\n\n  return (\n    <PageRedactionModal\n      evenDisabled={evenDisabled}\n      closeModal={closeModal}\n      renderCanvases={renderCanvases}\n      redactPages={onRedact}\n      markPages={markPages}\n      currentPage={currentPage}\n      selectedPages={selectedPages}\n      pageLabels={pageLabels}\n      isOpen={isOpen}\n    />\n  );\n};\n\nexport default PageRedactionModalContainer;", "import PageRedactionModalContainer from 'components/PageRedactionModal/PageRedactionModalContainer';\n\nexport default PageRedactionModalContainer;"], "sourceRoot": ""}