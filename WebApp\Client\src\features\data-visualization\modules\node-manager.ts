﻿/**
 * Node Management Module
 * Orchestrates node positioning, rendering, and visual state management using specialized services
 */

import * as d3 from 'd3';
import {
	Node,
	DataNetwork,
	NodeInteractionEvent,
	// TypeGuards,
	// NetworkUtils
} from '../models/network-types';
import { VisualizationConfig } from '../models/visualization-configuration';

// Import specialized services
import { NodePositioningService } from './services/node/node-positioning-service';
import { NodeRenderer } from './services/node/node-render-service';
import { NodeEventService } from './services/node/node-events-service';

/**
 * Manages node positioning, rendering, and visual states using specialized services
 */
export class NodeManager {
	private config: VisualizationConfig;
	private eventCallbacks: Map<string, (event: NodeInteractionEvent) => void> = new Map();

	// Specialized services
	private positioningService: NodePositioningService;
	private renderService: NodeRenderer;
	private eventService: NodeEventService;

	constructor(config: VisualizationConfig) {
		this.config = config;

		// Initialize services
		this.positioningService = new NodePositioningService(config);
		this.renderService = new NodeRenderer(config);
		this.eventService = new NodeEventService();

		// Setup event forwarding
		this.setupEventForwarding();
	}

	/**
	 * Sets up event forwarding from services to external listeners
	 */
	private setupEventForwarding(): void {
		// Forward events from event service to external listeners
		this.eventService.on('nodeClick', (event) => this.emit('nodeClick', event));
		this.eventService.on('nodeMouseOver', (event) => this.emit('nodeMouseOver', event));
		this.eventService.on('nodeMouseOut', (event) => this.emit('nodeMouseOut', event));
		this.eventService.on('iconClick', (event) => this.emit('iconClick', event));
	}

	/**
	 * Sets up node positions based on the configuration strategy
	 * Delegates to positioning service
	 */
	public setupNodePositions(
		nodes: Node[],
		centerX: number,
		centerY: number
	): void {
		this.positioningService.setupNodePositions(nodes, centerX, centerY);
	}

	/**
	 * Positions the root node at the center
	 */
	private positionRootNode(rootNode: Node, centerX: number, centerY: number): void {
		rootNode.x = centerX;
		rootNode.y = centerY;
		rootNode.fx = centerX;
		rootNode.fy = centerY;

		// Store globally for compatibility
		if (typeof window !== 'undefined') {
			window.sourceNode = rootNode;
		}
	}

	/**
	 * Sets up clock face positioning for the initial 12 nodes
	 */
	public setupClockPositions(
		nodes: Node[],
		centerX: number,
		centerY: number,
		radius: number
	): void {
		const horizontalRadius = radius * this.config.layout.horizontalRadiusMultiplier;
		const distanceFor3n9ClockNodes = this.calculateHorizontalDistance(nodes.length);

		nodes.forEach((node, i) => {
			const position = this.getClockPosition(i, centerX, centerY, radius, horizontalRadius, distanceFor3n9ClockNodes);

			node.x = position.x;
			node.y = position.y;
			node.position = position.clockPosition;
			node.fx = node.x;
			node.fy = node.y;
		});
	}

	/**
	 * Calculates horizontal distance for 3 and 9 o'clock positions
	 */
	private calculateHorizontalDistance(nodeCount: number): number {
		const containerWidth = this.config.containerWidth || 800;
		return nodeCount > 8 ? containerWidth / 9 : containerWidth / 20;
	}

	/**
	 * Gets position and clock position for a node at a specific index
	 */
	private getClockPosition(
		index: number,
		centerX: number,
		centerY: number,
		radius: number,
		horizontalRadius: number,
		distanceFor3n9ClockNodes: number
	): { x: number; y: number; clockPosition: number } {
		const positions = [
			// 6 o'clock (bottom center)
			{ x: centerX, y: centerY + radius / 2, position: 6 },
			// 7 o'clock (bottom left)
			{ x: centerX - radius * Math.cos(Math.PI / 6), y: centerY + radius * Math.sin(Math.PI / 6), position: 7 },
			// 5 o'clock (bottom right)
			{ x: centerX + radius * Math.cos(Math.PI / 6), y: centerY + radius * Math.sin(Math.PI / 6), position: 5 },
			// 12 o'clock (top center)
			{ x: centerX, y: centerY - radius / 2, position: 12 },
			// 11 o'clock (top left)
			{ x: centerX - radius * Math.cos(Math.PI / 6), y: centerY - radius * Math.sin(Math.PI / 6), position: 11 },
			// 1 o'clock (top right)
			{ x: centerX + radius * Math.cos(Math.PI / 6), y: centerY - radius * Math.sin(Math.PI / 6), position: 1 },
			// 3 o'clock (right center)
			{ x: centerX + horizontalRadius + distanceFor3n9ClockNodes, y: centerY, position: 3 },
			// 9 o'clock (left center)
			{ x: centerX - horizontalRadius - distanceFor3n9ClockNodes, y: centerY, position: 9 },
			// 10 o'clock (top left outer)
			{ x: centerX - (radius * 2) * Math.cos(Math.PI / 6), y: centerY - radius * Math.sin(Math.PI / 6), position: 10 },
			// 2 o'clock (top right outer)
			{ x: centerX + (radius * 2) * Math.cos(Math.PI / 6), y: centerY - radius * Math.sin(Math.PI / 6), position: 2 },
			// 4 o'clock (bottom right outer)
			{ x: centerX + (radius * 2) * Math.cos(Math.PI / 6), y: centerY + radius * Math.sin(Math.PI / 6), position: 4 },
			// 8 o'clock (bottom left outer)
			{ x: centerX - (radius * 2) * Math.cos(Math.PI / 6), y: centerY + radius * Math.sin(Math.PI / 6), position: 8 }
		];

		if (index < positions.length) {
			return {
				x: positions[index].x,
				y: positions[index].y,
				clockPosition: positions[index].position
			};
		}

		// For additional nodes, distribute in a circle
		const angle = 2 * Math.PI * (index - positions.length) / (index - positions.length + 1);
		return {
			x: centerX + radius * Math.cos(angle),
			y: centerY + radius * Math.sin(angle),
			clockPosition: 0
		};
	}

	/**
	 * Sets up expanded node positioning for large datasets
	 */
	public setupExpandedNodePositions(
		nodes: Node[],
		centerX: number,
		centerY: number,
		baseRadius: number
	): void {
		if (nodes.length <= 12) {
			return;
		}

		// Position root node
		const rootNode = nodes.find(n => n.highlighted);
		if (rootNode) {
			this.positionRootNode(rootNode, centerX, centerY);
		}

		// Separate non-root nodes
		const childNodes = nodes.filter(node => !node.highlighted);

		// Set up initial clock positions
		this.setupClockPositions(childNodes.slice(0, 12), centerX, centerY, baseRadius);

		// Set up expansion layers for remaining nodes
		if (childNodes.length > 12) {
			this.setupExpansionLayers(
				childNodes.slice(12),
				centerX,
				centerY,
				baseRadius
			);
		}
	}

	/**
	 * Sets up expansion layers for nodes beyond the initial clock positions
	 */
	public setupExpansionLayers(
		extraNodes: Node[],
		centerX: number,
		centerY: number,
		baseRadius: number
	): void {
		const layerSpacing = this.config.layout.layerVerticalSpacing;
		let remainingNodes = extraNodes.length;
		let layers = 0;
		let southernHemisphereSize = this.config.layout.initialSouthernCount;
		let northernHemisphereSize = this.config.layout.initialNorthernCount;
		let nodesAccounted = 0;

		// Calculate required layers
		while (remainingNodes > 0) {
			layers++;
			const isEvenLayer = layers % 2 === 0;

			if (isEvenLayer) {
				northernHemisphereSize++;
			} else {
				southernHemisphereSize++;
			}

			const currentLayerNodeCount = isEvenLayer ? northernHemisphereSize : southernHemisphereSize;
			nodesAccounted += currentLayerNodeCount;
			remainingNodes = extraNodes.length - nodesAccounted;
		}

		// Reset counters and place nodes
		southernHemisphereSize = this.config.layout.initialSouthernCount;
		northernHemisphereSize = this.config.layout.initialNorthernCount;
		let processedNodes = 0;

		const firstLayerDistance = baseRadius;

		for (let layer = 1; layer <= layers; layer++) {
			const isEvenLayer = layer % 2 === 0;
			let layerNodeCount: number;

			if (isEvenLayer) {
				northernHemisphereSize++;
				layerNodeCount = northernHemisphereSize;

				const northernLayerNumber = Math.ceil(layer / 1.6);
				const verticalPosition = firstLayerDistance + ((northernLayerNumber - 1) * layerSpacing);

				this.placeNorthernLayerNodes(
					extraNodes.slice(processedNodes, processedNodes + layerNodeCount),
					centerX,
					centerY,
					baseRadius,
					verticalPosition,
					northernLayerNumber
				);
			} else {
				southernHemisphereSize++;
				layerNodeCount = southernHemisphereSize;

				const southernLayerNumber = Math.ceil(layer / 1.2);
				const verticalPosition = firstLayerDistance + ((southernLayerNumber - 1) * layerSpacing);

				this.placeSouthernLayerNodes(
					extraNodes.slice(processedNodes, processedNodes + layerNodeCount),
					centerX,
					centerY,
					baseRadius,
					verticalPosition,
					southernLayerNumber
				);
			}

			processedNodes += layerNodeCount;
			if (processedNodes >= extraNodes.length) break;
		}
	}

	/**
	 * Places nodes in a southern expansion layer
	 */
	public placeSouthernLayerNodes(
		nodes: Node[],
		centerX: number,
		centerY: number,
		baseRadius: number,
		verticalPosition: number,
		layerNumber: number
	): void {
		if (!nodes.length) return;

		const nodeCount = nodes.length <= 6 ? 6 : nodes.length;
		const layerWidth = baseRadius * (nodeCount / 2);
		const spacing = layerWidth / (nodeCount - (nodeCount > 4 ? 3 : 1));
		const startX = centerX - (layerWidth / (nodeCount > 4 ? 1.2 : 2));
		const layerY = centerY + verticalPosition;

		nodes.forEach((node, index) => {
			node.x = startX + (index * spacing);
			node.y = layerY;
			node.position = 100 + index; // Southern expansion positions
			node.layerNumber = layerNumber;
			node.expansionLayer = {
				hemisphere: 'southern',
				layer: layerNumber
			};
			node.trunkConnection = 'southern';
			node.fx = node.x;
			node.fy = node.y;
		});
	}

	/**
	 * Places nodes in a northern expansion layer
	 */
	public placeNorthernLayerNodes(
		nodes: Node[],
		centerX: number,
		centerY: number,
		baseRadius: number,
		verticalPosition: number,
		layerNumber: number
	): void {
		if (!nodes.length) return;

		const nodeCount = nodes.length < 6 ? 7 : nodes.length;
		const layerWidth = baseRadius * (nodeCount / 2);
		const spacing = layerWidth / (nodeCount - (nodeCount > 4 ? 3 : 1));
		const startX = centerX - (layerWidth / (nodeCount > 4 ? 1.2 : 2));
		const layerY = centerY - verticalPosition;

		nodes.forEach((node, index) => {
			node.x = startX + (index * spacing);
			node.y = layerY;
			node.position = 200 + index; // Northern expansion positions
			node.layerNumber = layerNumber;
			node.expansionLayer = {
				hemisphere: 'northern',
				layer: layerNumber
			};
			node.trunkConnection = 'northern';
			node.fx = node.x;
			node.fy = node.y;
		});
	}

	/**
	 * Creates and renders all nodes in the visualization
	 */
	public createNodes(
		g: d3.Selection<SVGGElement, unknown, HTMLElement, any>,
		data: DataNetwork
	): d3.Selection<SVGGElement, Node, SVGGElement, unknown> {
		const nodeSelection = g.append("g")
													 .attr("class", "nodes-group")
													 .selectAll<SVGGElement, Node>(".node-group")
													 .data(data.nodes)
													 .enter()
													 .append<SVGGElement>("g")
													 .attr("class", "node-group")
													 .attr("data-node-id", d => d.id);

		this.renderCustomNodes(nodeSelection);
		return nodeSelection;
	}

	/**
	 * Renders custom node visualization for each node
	 */
	private renderCustomNodes(
		selection: d3.Selection<SVGGElement, Node, SVGGElement, unknown>
	): void {
		const { cardWidth, cardHeight, cardMargin, leftBorderWidth, leftBorderOffset } = this.config.dimensions;
		const adjustedHeight = cardHeight + (cardMargin * 2);

		selection.each((d, i, nodes) => {
			const node = d3.select(nodes[i]);
			const x = (d.x || 0) - cardWidth / 2;
			const y = (d.y || 0) - cardHeight / 2;

			// Set initial opacity based on node state
			const isGrayedButNotHighlighted = d.isGrayedOut && !d.highlighted;
			const initialOpacity = isGrayedButNotHighlighted ? 0.5 : 1;
			node.style("opacity", String(initialOpacity));

			// Create tooltip for accessibility
			this.createTooltipDiv();

			// Render node components
			// @ts-ignore
			this.renderNodeBackground(node, d, x, y, cardWidth, cardHeight, cardMargin);
			// @ts-ignore
			this.renderLeftBorder(node, d, x, y, leftBorderOffset, leftBorderWidth, adjustedHeight);
			// @ts-ignore
			this.renderNodeText(node, d, x, y, leftBorderOffset, leftBorderWidth);
			// @ts-ignore
			this.renderNodeIcon(node, d, x, y, cardWidth);

			// Add grayed out overlay if needed
			if (isGrayedButNotHighlighted) {
				// @ts-ignore
				this.renderInactiveOverlay(node, x, y, cardWidth, cardHeight);
			}
		});
	}

	/**
	 * Creates the tooltip div element
	 */
	private createTooltipDiv(): void {
		if (d3.select(".custom-tooltip").empty()) {
			d3.select("body")
				.append("div")
				.attr("class", "custom-tooltip")
				.style("opacity", "0")
				.style("position", "absolute")
				.style("background", "var(--clr-blue-950)")
				.style("color", "white")
				.style("padding", "var(--size-spacing-s) var(--size-spacing-m)")
				.style("border-radius", "var(--size-border-radius-l)")
				.style("font-size", this.config.typography.tooltipFontSize)
				.style("pointer-events", "none");
		}
	}

	/**
	 * Renders the node background rectangle
	 */
	private renderNodeBackground(
		node: d3.Selection<SVGGElement, Node, SVGGElement, unknown>,
		d: Node,
		x: number,
		y: number,
		cardWidth: number,
		cardHeight: number,
		cardMargin: number
	): void {
		// @ts-ignore
		const isGrayedButNotHighlighted: boolean = d.isGrayedOut && !d.highlighted;

		const background = node.append('rect')
													 .attr('x', x - cardMargin)
													 .attr('y', y - cardMargin)
													 .attr('width', cardWidth + cardMargin * 2)
													 .attr('height', cardHeight + cardMargin * 2)
													 .attr('rx', this.config.dimensions.cornerRadius)
													 .attr('ry', this.config.dimensions.cornerRadius)
													 .attr('fill', this.config.colors.nodeBackground)
													 .attr('stroke', this.getNodeColor(d))
													 .attr('stroke-width', 2)
													 .attr('cursor', "pointer")
													 .attr('class', 'node-background');

		// Apply dashed stroke for grayed out nodes
		if (isGrayedButNotHighlighted) {
			background.attr('stroke-dasharray', '3,2');
		}

		// Add event handlers
		this.addNodeBackgroundEvents(background, node, d, isGrayedButNotHighlighted);
	}

	/**
	 * Adds event handlers to node background
	 */
	private addNodeBackgroundEvents(
		background: d3.Selection<SVGRectElement, Node, SVGGElement, unknown>,
		node: d3.Selection<SVGGElement, Node, SVGGElement, unknown>,
		d: Node,
		isGrayedButNotHighlighted: boolean
	): void {
		background
			.on('mouseover', (event: MouseEvent) => this.handleNodeMouseOver(event, node, d, isGrayedButNotHighlighted))
			.on('mouseout', (event: MouseEvent) => this.handleNodeMouseOut(event, node, d, isGrayedButNotHighlighted))
			.on('click', (event: MouseEvent) => this.handleNodeClick(event, node, d));
	}

	/**
	 * Renders the left border of the node
	 */
	private renderLeftBorder(
		node: d3.Selection<SVGGElement, Node, SVGGElement, unknown>,
		d: Node,
		x: number,
		y: number,
		leftBorderOffset: number,
		leftBorderWidth: number,
		adjustedHeight: number
	): void {
		const cardX = x - this.config.dimensions.cardMargin;
		const cardY = y - this.config.dimensions.cardMargin;

		node.append('line')
				.attr('x1', cardX + leftBorderOffset)
				.attr('y1', cardY)
				.attr('x2', cardX + leftBorderOffset)
				.attr('y2', cardY + adjustedHeight)
				.attr('stroke', this.getNodeColor(d))
				.attr('stroke-width', leftBorderWidth);
	}

	/**
	 * Renders node text elements (title and description)
	 */
	private renderNodeText(
		node: d3.Selection<SVGGElement, Node, SVGGElement, unknown>,
		d: Node,
		x: number,
		y: number,
		leftBorderOffset: number,
		leftBorderWidth: number
	): void {
		const textX = x + leftBorderOffset + leftBorderWidth - 5;
		// @ts-ignore
		const isGrayedButNotHighlighted: boolean = d.isGrayedOut && !d.highlighted ;

		// Title text
		const titleText = node.append('text')
													.attr('x', textX)
													.attr('y', y + 6)
													.attr('font-family', this.config.typography.fontFamily)
													.attr('font-size', this.config.typography.titleFontSize)
													.attr('fill', this.config.colors.primaryText)
													.attr('cursor', "pointer")
													.text(d.name);

		// Description text
		const descriptionText = node.append('text')
																.attr('x', textX)
																.attr('y', y + 19)
																.attr('font-family', this.config.typography.fontFamily)
																.attr('font-size', this.config.typography.descriptionFontSize)
																.attr('fill', this.config.colors.secondaryText)
																.attr('cursor', "pointer")
																.text(d.description);

		// Add event handlers to text elements
		[titleText, descriptionText].forEach(textElement => {
			textElement
				.on('mouseover', (event: MouseEvent) => this.handleNodeMouseOver(event, node, d, isGrayedButNotHighlighted))
				.on('mouseout', (event: MouseEvent) => this.handleNodeMouseOut(event, node, d, isGrayedButNotHighlighted))
				.on('click', (event: MouseEvent) => this.handleNodeClick(event, node, d));
		});
	}

	/**
	 * Renders the node icon (external link icon)
	 */
	private renderNodeIcon(
		node: d3.Selection<SVGGElement, Node, SVGGElement, unknown>,
		d: Node,
		x: number,
		y: number,
		cardWidth: number
	): void {
		const iconGroup = node.append('g')
													.attr('transform', `translate(${x + cardWidth - 20}, ${y + 4}) scale(0.018)`);

		// Invisible hit area for better interaction
		iconGroup.append('rect')
						 .attr('x', -300)
						 .attr('y', -300)
						 .attr('rx', 4)
						 .attr('ry', 4)
						 .attr('width', 1200)
						 .attr('height', 1200)
						 .attr('fill', 'transparent')
						 .attr('cursor', 'pointer')
						 .attr('class', 'icon-hit-area');

		// Icon path
		iconGroup.append('path')
						 .attr('d', 'M320 0c-17.7 0-32 14.3-32 32s14.3 32 32 32l82.7 0L201.4 265.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L448 109.3l0 82.7c0 17.7 14.3 32 32 32s32-14.3 32-32l0-160c0-17.7-14.3-32-32-32L320 0zM80 32C35.8 32 0 67.8 0 112L0 432c0 44.2 35.8 80 80 80l320 0c44.2 0 80-35.8 80-80l0-112c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 112c0 8.8-7.2 16-16 16L80 448c-8.8 0-16-7.2-16-16l0-320c0-8.8 7.2-16 16-16l112 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L80 32z')
						 .attr('fill', 'var(--visualization-bg-6)')
						 .attr('cursor', 'pointer')
						 .attr('class', 'icon-path');

		// Add icon event handlers
		this.addIconEventHandlers(iconGroup, node, d);
	}

	/**
	 * Adds event handlers to the icon
	 */
	private addIconEventHandlers(
		iconGroup: d3.Selection<SVGGElement, Node, SVGGElement, unknown>,
		node: d3.Selection<SVGGElement, Node, SVGGElement, unknown>,
		d: Node
	): void {
		// @ts-ignore
		const isGrayedButNotHighlighted: boolean = d.isGrayedOut && !d.highlighted;

		iconGroup.selectAll('.icon-hit-area, .icon-path')
						 .on('mouseover', (event: MouseEvent) => this.handleIconMouseOver(event, node, iconGroup, d, isGrayedButNotHighlighted))
						 .on('mouseout', (event: MouseEvent) => this.handleIconMouseOut(event, node, iconGroup, d, isGrayedButNotHighlighted))
						 .on('mousemove', (event: MouseEvent) => this.handleIconMouseMove(event))
						 .on('click', (event: MouseEvent) => this.handleIconClick(event, d));
	}

	/**
	 * Renders inactive overlay for grayed out nodes
	 */
	private renderInactiveOverlay(
		node: d3.Selection<SVGGElement, Node, SVGGElement, unknown>,
		x: number,
		y: number,
		cardWidth: number,
		cardHeight: number
	): void {
		node.append('text')
				.attr('x', x + cardWidth / 2)
				.attr('y', y + cardHeight / 2 + 2)
				.attr('text-anchor', 'middle')
				.attr('dominant-baseline', 'middle')
				.attr('font-family', this.config.typography.fontFamily)
				.attr('font-size', 'var(--size-text-xs)')
				.attr('font-style', 'italic')
				.attr('fill', this.config.colors.mutedText)
				.attr('pointer-events', 'none')
				.text('Inactive');
	}

	/**
	 * Event Handlers
	 */
	private handleNodeMouseOver(
		event: MouseEvent,
		node: d3.Selection<SVGGElement, Node, SVGGElement, unknown>,
		d: Node,
		isGrayedButNotHighlighted: boolean
	): void {
		event.stopPropagation();
		if (parseFloat(node.style("opacity") as string) > 0.05) {
			const hoverOpacity = isGrayedButNotHighlighted ? 0.8 : 1;
			node.style("opacity", String(hoverOpacity));
			node.select('rect').attr('fill', this.getNodeHoverColor(d.highlighted));
			node.select('.icon-path').attr('fill', "var(--visualization-bg-6)");
		}
	}

	private handleNodeMouseOut(
		event: MouseEvent,
		node: d3.Selection<SVGGElement, Node, SVGGElement, unknown>,
		_d: Node,
		isGrayedButNotHighlighted: boolean
	): void {
		event.stopPropagation();
		const initialOpacity = isGrayedButNotHighlighted ? 0.5 : 1;
		node.style("opacity", String(initialOpacity));
		node.select('rect').attr('fill', this.config.colors.nodeBackground);
		node.select('.icon-path').attr('fill', 'var(--visualization-bg-6)');
	}

	private handleNodeClick(
		event: MouseEvent,
		node: d3.Selection<SVGGElement, Node, SVGGElement, unknown>,
		d: Node
	): void {
		if (parseFloat(node.style("opacity") as string) > 0.1) {
			event.stopPropagation();
			event.preventDefault();

			const nodeEvent: NodeInteractionEvent = {
				type: 'click',
				node: d,
				event,
				position: { x: event.clientX, y: event.clientY }
			};

			this.emitEvent('nodeClick', nodeEvent);
		}
	}

	private handleIconMouseOver(
		event: MouseEvent,
		node: d3.Selection<SVGGElement, Node, SVGGElement, unknown>,
		iconGroup: d3.Selection<SVGGElement, Node, SVGGElement, unknown>,
		d: Node,
		isGrayedButNotHighlighted: boolean
	): void {
		if (parseFloat(node.style("opacity") as string) > 0.05) {
			event.stopPropagation();
			event.preventDefault();

			const hoverOpacity = isGrayedButNotHighlighted ? 0.8 : 1;
			node.style("opacity", String(hoverOpacity));
			node.select('rect').attr('fill', this.getNodeHoverColor(d.highlighted));
			iconGroup.select('.icon-path').attr('fill', 'var(--clr-blue-900)');

			// Show tooltip
			const tooltip = d3.select(".custom-tooltip");
			tooltip.style("opacity", "1")
						 .text("Open Data source")
						 .style("left", `${(event.pageX + 10) / 10}rem`)
						 .style("top", `${(event.pageY + 10) / 10}rem`);

			// Add hover background
			iconGroup.insert('rect', '.icon-hit-area')
							 .attr('x', -300)
							 .attr('y', -300)
							 .attr('rx', 5)
							 .attr('ry', 5)
							 .attr('width', 1200)
							 .attr('height', 1200)
							 .attr('fill', 'var(--visualization-bg-3)')
							 .attr('class', 'hover-bg');
		}
	}

	private handleIconMouseOut(
		event: MouseEvent,
		node: d3.Selection<SVGGElement, Node, SVGGElement, unknown>,
		iconGroup: d3.Selection<SVGGElement, Node, SVGGElement, unknown>,
		_d: Node,
		isGrayedButNotHighlighted: boolean
	): void {
		event.stopPropagation();
		const initialOpacity = isGrayedButNotHighlighted ? 0.5 : 1;
		node.style("opacity", String(initialOpacity));
		node.select('rect').attr('fill', this.config.colors.nodeBackground);
		iconGroup.select('.icon-path').attr('fill', 'var(--visualization-bg-6)');
		d3.select(".custom-tooltip").style("opacity", "0");
		iconGroup.selectAll('.hover-bg').remove();
	}

	private handleIconMouseMove(event: MouseEvent): void {
		d3.select(".custom-tooltip")
			.style("left", `${(event.pageX + 10) / 10}rem`)
			.style("top", `${(event.pageY + 10) / 10}rem`);
	}

	private handleIconClick(event: MouseEvent, d: Node): void {
		if (parseFloat(d3.select(`[data-node-id="${d.id}"]`).style("opacity") as string) > 0.1) {
			event.stopPropagation();
			event.preventDefault();

			const nodeEvent: NodeInteractionEvent = {
				type: 'click',
				node: d,
				event,
				position: { x: event.clientX, y: event.clientY }
			};

			this.emitEvent('iconClick', nodeEvent);
		}
	}

	/**
	 * Utility Methods
	 */

	/**
	 * Gets the color for a node based on its state
	 */
	public getNodeColor(data: Node): string {
		switch (true) {
			case data.isGrayedOut === true && !data.highlighted:
				return this.config.colors.nodeGrayedOut;
			case data.highlighted:
				return this.config.colors.nodeHighlighted;
			default:
				return this.config.colors.nodeBorder;
		}
	}

	/**
	 * Gets the background color for a node on hover
	 */
	public getNodeHoverColor(highlighted?: boolean): string {
		if (highlighted) return "var(--clr-purple-200)";
		return this.config.colors.nodeHoverBackground;
	}

	/**
	 * Calculates node sizes based on configuration and state
	 */
	public calculateNodeSizes(nodes: Node[]): void {
		const { cardWidth, cardHeight } = this.config.dimensions;
		const baseSize = Math.max(cardWidth, cardHeight) / 2 + 10;

		nodes.forEach(node => {
			node.size = baseSize + (node.highlighted ? 20 : 0);
		});
	}

	/**
	 * Updates node positions in the visualization
	 */
	public updateNodePositions(
		nodeSelection: d3.Selection<SVGGElement, Node, SVGGElement, unknown>
	): void {
		nodeSelection.attr('transform', d => `translate(${d.x || 0},${d.y || 0})`);
	}

	/**
	 * Filters nodes based on visibility criteria
	 */
	public filterVisibleNodes(nodes: Node[], usedNodeIds: Set<string>): Node[] {
		return nodes.filter(node =>
			node.highlighted ||
			usedNodeIds.has(node.id) ||
			this.shouldShowNode(node)
		);
	}

	/**
	 * Determines if a node should be visible based on current configuration
	 */
	private shouldShowNode(node: Node): boolean {
		// Custom logic for determining node visibility
		// Can be extended based on filters, zoom level, etc.
		return !node.isGrayedOut || node.highlighted;
	}

	/**
	 * Sets node visibility state
	 */
	public setNodeVisibility(
		nodeSelection: d3.Selection<SVGGElement, Node, SVGGElement, unknown>,
		nodeId: string,
		visible: boolean
	): void {
		nodeSelection
			.filter(d => d.id === nodeId)
			.style('display', visible ? 'block' : 'none');
	}

	/**
	 * Highlights a specific node
	 */
	public highlightNode(
		nodeSelection: d3.Selection<SVGGElement, Node, SVGGElement, unknown>,
		nodeId: string,
		highlighted: boolean = true
	): void {
		nodeSelection
			.filter(d => d.id === nodeId)
			.each((d) => {
				d.highlighted = highlighted;
			})
			.select('.node-background')
			.attr('stroke', this.getNodeColor)
			.attr('stroke-width', highlighted ? 3 : 2);
	}

	/**
	 * Updates node state (grayed out, highlighted, etc.)
	 */
	public updateNodeState(
		nodeSelection: d3.Selection<SVGGElement, Node, SVGGElement, unknown>,
		nodeId: string,
		state: Partial<Pick<Node, 'isGrayedOut' | 'highlighted'>>
	): void {
		nodeSelection
			.filter(d => d.id === nodeId)
			.each((d) => {
				Object.assign(d, state);
			})
			.call(() => this.refreshNodeAppearance(nodeSelection.filter(d => d.id === nodeId)));
	}

	/**
	 * Refreshes the visual appearance of nodes based on their current state
	 */
	private refreshNodeAppearance(
		nodeSelection: d3.Selection<SVGGElement, Node, SVGGElement, unknown>
	): void {
		nodeSelection.each((d, i, nodes) => {
			const node = d3.select(nodes[i]);
			const isGrayedButNotHighlighted = d.isGrayedOut && !d.highlighted;

			// Update opacity
			node.style('opacity', isGrayedButNotHighlighted ? '0.5' : '1');

			// Update stroke and fill
			node.select('.node-background')
					.attr('stroke', this.getNodeColor(d))
					.attr('stroke-dasharray', isGrayedButNotHighlighted ? '3,2' : null);
		});
	}

	/**
	 * Gets nodes by their expansion layer
	 */
	public getNodesByLayer(nodes: Node[], hemisphere: 'northern' | 'southern', layer: number): Node[] {
		return nodes.filter(node =>
			node.expansionLayer?.hemisphere === hemisphere &&
			node.expansionLayer?.layer === layer
		);
	}

	/**
	 * Gets nodes by their clock position
	 */
	public getNodesByClockPosition(nodes: Node[], position: number): Node[] {
		return nodes.filter(node => node.position === position);
	}

	/**
	 * Gets the root (highlighted) node
	 */
	public getRootNode(nodes: Node[]): Node | undefined {
		return nodes.find(node => node.highlighted);
	}

	/**
	 * Validates node positioning data
	 */
	public validateNodePositions(nodes: Node[]): { isValid: boolean; errors: string[] } {
		const errors: string[] = [];

		nodes.forEach((node, index) => {
			if (typeof node.x !== 'number' || typeof node.y !== 'number') {
				errors.push(`Node ${index} (${node.id}): Invalid position coordinates`);
			}

			if (node.fx !== null && typeof node.fx !== 'number') {
				errors.push(`Node ${index} (${node.id}): Invalid fixed x coordinate`);
			}

			if (node.fy !== null && typeof node.fy !== 'number') {
				errors.push(`Node ${index} (${node.id}): Invalid fixed y coordinate`);
			}
		});

		return {
			isValid: errors.length === 0,
			errors
		};
	}

	/**
	 * Event Management
	 */

	/**
	 * Registers an event callback
	 */
	public on(eventType: string, callback: (event: NodeInteractionEvent) => void): void {
		this.eventCallbacks.set(eventType, callback);
	}

	/**
	 * Unregisters an event callback
	 */
	public off(eventType: string): void {
		this.eventCallbacks.delete(eventType);
	}

	/**
	 * Emits an event to registered callbacks
	 */
	private emitEvent(eventType: string, event: NodeInteractionEvent): void {
		const callback = this.eventCallbacks.get(eventType);
		if (callback) {
			callback(event);
		}
	}

	/**
	 * Configuration Management
	 */

	/**
	 * Updates the configuration
	 */
	public updateConfig(newConfig: Partial<VisualizationConfig>): void {
		this.config = { ...this.config, ...newConfig };
	}

	/**
	 * Gets the current configuration
	 */
	public getConfig(): VisualizationConfig {
		return { ...this.config };
	}

	/**
	 * Performance and Optimization
	 */

	/**
	 * Optimizes node rendering for large datasets
	 */
	public optimizeForLargeDataset(
		nodeSelection: d3.Selection<SVGGElement, Node, SVGGElement, unknown>,
		nodeCount: number
	): void {
		if (nodeCount > this.config.performance.lodThresholds.hideLabels) {
			// Hide labels for better performance
			nodeSelection.selectAll('text').style('display', 'none');
		}

		if (nodeCount > this.config.performance.lodThresholds.hideTooltips) {
			// Disable tooltip interactions
			nodeSelection.selectAll('*').on('mouseover', null).on('mouseout', null);
		}
	}

	/**
	 * Enables or disables node interactions based on performance settings
	 */
	public setInteractionsEnabled(
		nodeSelection: d3.Selection<SVGGElement, Node, SVGGElement, unknown>,
		enabled: boolean
	): void {
		if (enabled) {
			// Re-enable interactions - would need to re-call renderCustomNodes or store original handlers
			console.log('Re-enabling interactions - implementation depends on use case');
		} else {
			// Disable all interactions
			nodeSelection.selectAll('*')
									 .on('click', null)
									 .on('mouseover', null)
									 .on('mouseout', null)
									 .on('mousemove', null);
		}
	}

	/**
	 * Cleanup method to remove event listeners and references
	 */
	public destroy(): void {
		this.eventCallbacks.clear();
		d3.select('.custom-tooltip').remove();
	}
}