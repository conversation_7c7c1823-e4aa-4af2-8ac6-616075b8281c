using Levelbuild.Domain.Storage.Db;
using Levelbuild.Domain.StorageEntities.Features.Dto;
using Microsoft.EntityFrameworkCore;
using MongoDB.Bson;
using MongoDB.Driver;

namespace Levelbuild.Domain.Storage.Helper;

public static class MongoDbMigrationHelper
{
	/// <summary>
	/// Create Index for given collection, IndexName and IndexKeysDefinition
	/// </summary>
	/// <param name="collection"></param>
	/// <param name="indexName"></param>
	/// <param name="builder"></param>
	private static void CreateIndex(IMongoCollection<BsonDocument> collection, String indexName, IndexKeysDefinition<BsonDocument> builder)
	{
		var indexKey = builder;
		var indexOption = new CreateIndexOptions() { Name = indexName };
		var indexModel = new CreateIndexModel<BsonDocument>(indexKey, indexOption);

		_ = collection.Indexes.CreateOneAsync(indexModel);
	}


	/// <summary>
	/// Add all defined Indexes to given indexDefinitionName
	/// </summary>
	/// <param name="mongoDB"></param>
	/// <param name="indexDefinitionName"></param>
	public static async Task CheckIndexOrCreate(IMongoDatabase mongoDb, String indexDefinitionName)
	{
		var indexes = new Dictionary<string, Action>();
		var collection = mongoDb.GetCollection<BsonDocument>(indexDefinitionName);


		var indexRevisionElementAscRevisionIdDescName = "RevElementId_Asc_Revision_Desc";
		indexes[indexRevisionElementAscRevisionIdDescName] = () =>
		{
			CreateIndex(collection, indexRevisionElementAscRevisionIdDescName,
						Builders<BsonDocument>.IndexKeys.Ascending(RevisionDefinition.ElementId).Descending(RevisionDefinition.Revision));
		};

		// It seems like we can just create the indexes, without having to check if they are already present
		// the test gets no error and the present indexes are not deleted
		// i needed to wait for maybe 2 seconds that the indexes are displayed
		indexes.Values.ToList().ForEach(index => index());
	}

	public static void MigrateMongoDb(StorageConnection connection, string? indexDefinitionName = null)
	{
		List<StorageIndexDefinition> definitionList = new List<StorageIndexDefinition>();
		if (indexDefinitionName != null)
		{
			var definition = connection.WithDbContext(db => { return db.StorageIndexDefinition.FirstOrDefault(it => it.Name == indexDefinitionName); });
			if (definition != null)
				definitionList.Add(definition);
		}
		else
			definitionList = connection.WithDbContext(db => { return db.StorageIndexDefinition.ToList(); });

		foreach (var storageIndexDefinition in definitionList)
		{
			CheckIndexOrCreate(connection.Db.MongoDbClient.GetDatabase(
								   FilterParser.ShortenAlias(connection.Db.ConnectionHelper.GetDatabaseName(), null,
															 MongoDbRevisionHelper.MongoDbNameMaxLength)),
							   FilterParser.ShortenAlias(storageIndexDefinition.Name));
			
			// for additional fields table
			CheckIndexOrCreate(connection.Db.MongoDbClient.GetDatabase(
								   FilterParser.ShortenAlias(connection.Db.ConnectionHelper.GetDatabaseName(), null,
															 MongoDbRevisionHelper.MongoDbNameMaxLength)),
							   FilterParser.ShortenAlias($"{storageIndexDefinition.Name}_{MongoDbRevisionHelper.Fields}"));
		}
	}
}