/** Notice * This file contains works from many authors under various (but compatible) licenses. Please see core.txt for more information. **/
(function(){(window.wpCoreControlsBundle=window.wpCoreControlsBundle||[]).push([[11],{623:function(ya,ua,n){n.r(ua);var na=n(0),ma=n(1);n.n(ma);var oa=n(2),ka=n(211);ya=n(53);var ia=n(124),fa=n(359),x=n(103),y=n(358);n=n(537);var r=window,e=function(){function h(b,w,z){var aa=-1===b.indexOf("?")?"?":"&";switch(w){case x.a.NEVER_CACHE:this.url="".concat(b+aa,"_=").concat(Object(ma.uniqueId)());break;default:this.url=b}this.Tf=z;this.request=new XMLHttpRequest;this.request.open("GET",this.url,!0);this.request.setRequestHeader("X-Requested-With",
"XMLHttpRequest");this.request.overrideMimeType?this.request.overrideMimeType("text/plain; charset=x-user-defined"):this.request.setRequestHeader("Accept-Charset","x-user-defined");this.status=y.a.NOT_STARTED}h.prototype.start=function(b,w){var z=this,aa=this,ea=this.request,ba;aa.lF=0;b&&Object.keys(b).forEach(function(ca){z.request.setRequestHeader(ca,b[ca])});w&&(this.request.withCredentials=w);this.lO=setInterval(function(){var ca=0===window.document.URL.indexOf("file:///");ca=200===ea.status||
ca&&0===ea.status;if(ea.readyState!==y.b.DONE||ca){try{ea.responseText}catch(ha){return}aa.lF<ea.responseText.length&&(ba=aa.UJa())&&aa.trigger(h.Events.DATA,[ba]);0===ea.readyState&&(clearInterval(aa.lO),aa.trigger(h.Events.DONE))}else clearInterval(aa.lO),aa.trigger(h.Events.DONE,["Error received return status ".concat(ea.status)])},1E3);this.request.send(null);this.status=y.a.STARTED};h.prototype.UJa=function(){var b=this.request,w=b.responseText;if(0!==w.length)if(this.lF===w.length)clearInterval(this.lO),
this.trigger(h.Events.DONE);else return w=Math.min(this.lF+3E6,w.length),b=r.q$(b,this.lF,!0,w),this.lF=w,b};h.prototype.abort=function(){clearInterval(this.lO);var b=this;this.request.onreadystatechange=function(){Object(oa.i)("StreamingRequest aborted");b.status=y.a.ABORTED;return b.trigger(h.Events.ABORTED)};this.request.abort()};h.prototype.finish=function(){var b=this;this.request.onreadystatechange=function(){b.status=y.a.SUCCESS;return b.trigger(h.Events.DONE)};this.request.abort()};h.Events=
{DONE:"done",DATA:"data",ABORTED:"aborted"};return h}();Object(ya.a)(e);var a;(function(h){h[h.LOCAL_HEADER=0]="LOCAL_HEADER";h[h.FILE=1]="FILE";h[h.CENTRAL_DIR=2]="CENTRAL_DIR"})(a||(a={}));var f=function(h){function b(){var w=h.call(this)||this;w.buffer="";w.state=a.LOCAL_HEADER;w.X_=4;w.Fq=null;w.mA=ka.c;w.Fs={};return w}Object(na.c)(b,h);b.prototype.HJa=function(w){var z;for(w=this.buffer+w;w.length>=this.mA;)switch(this.state){case a.LOCAL_HEADER:this.Fq=z=this.ZJa(w.slice(0,this.mA));if(z.YA!==
ka.g)throw Error("Wrong signature in local header: ".concat(z.YA));w=w.slice(this.mA);this.state=a.FILE;this.mA=z.PS+z.yw+z.DD+this.X_;this.trigger(b.Events.HEADER,[z]);break;case a.FILE:this.Fq.name=w.slice(0,this.Fq.yw);this.Fs[this.Fq.name]=this.Fq;z=this.mA-this.X_;var aa=w.slice(this.Fq.yw+this.Fq.DD,z);this.trigger(b.Events.FILE,[this.Fq.name,aa,this.Fq.mT]);w=w.slice(z);if(w.slice(0,this.X_)===ka.h)this.state=a.LOCAL_HEADER,this.mA=ka.c;else return this.state=a.CENTRAL_DIR,!0}this.buffer=w;
return!1};b.Events={HEADER:"header",FILE:"file"};return b}(fa.a);Object(ya.a)(f);ya=function(h){function b(w,z,aa,ea,ba){aa=h.call(this,w,aa,ea)||this;aa.url=w;aa.stream=new e(w,z);aa.$e=new f;aa.Wda=window.createPromiseCapability();aa.Mea={};aa.Tf=ba;return aa}Object(na.c)(b,h);b.prototype.vG=function(w){var z=this;this.request([this.dn,this.kp,this.cn]);this.stream.addEventListener(e.Events.DATA,function(aa){try{if(z.$e.HJa(aa))return z.stream.finish()}catch(ea){throw z.stream.abort(),z.Cv(ea),
w(ea),ea;}});this.stream.addEventListener(e.Events.DONE,function(aa){z.$Ia=!0;z.Wda.resolve();aa&&(z.Cv(aa),w(aa))});this.$e.addEventListener(f.Events.HEADER,Object(ma.bind)(this.Lea,this));this.$e.addEventListener(f.Events.FILE,Object(ma.bind)(this.qKa,this));return this.stream.start(this.Tf,this.withCredentials)};b.prototype.j$=function(w){var z=this;this.Wda.promise.then(function(){w(Object.keys(z.$e.Fs))})};b.prototype.Jt=function(){return!0};b.prototype.request=function(w){var z=this;this.$Ia&&
w.forEach(function(aa){z.Mea[aa]||z.rRa(aa)})};b.prototype.Lea=function(){};b.prototype.abort=function(){this.stream&&this.stream.abort()};b.prototype.rRa=function(w){this.trigger(ia.a.Events.PART_READY,[{Rb:w,error:"Requested part not found",$l:!1,Fi:!1}])};b.prototype.qKa=function(w,z,aa){this.Mea[w]=!0;this.trigger(ia.a.Events.PART_READY,[{Rb:w,data:z,$l:!1,Fi:!1,error:null,Ie:aa}])};return b}(ia.a);Object(n.a)(ya);Object(n.b)(ya);ua["default"]=ya}}]);}).call(this || window)
